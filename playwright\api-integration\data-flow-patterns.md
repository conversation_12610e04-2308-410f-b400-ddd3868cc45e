# SmartHR Data Flow Patterns

This document provides comprehensive documentation of data flow patterns between the SmartHR frontend and backend, including state management, API integration, and real-time updates.

## 📋 Data Flow Architecture

### Frontend-Backend Communication Flow
```
User Action → Frontend State → API Request → Backend Processing → Database → API Response → Frontend State Update → UI Update
```

### Key Components
- **Frontend State Management**: React Context API (JobContext, NavigationContext)
- **HTTP Client**: Axios with interceptors and error handling
- **Backend API**: FastAPI with RESTful endpoints
- **Database**: PostgreSQL with structured data models
- **Real-time Updates**: WebSocket connections (where applicable)

## 🔄 Core Data Flow Patterns

### Job Management Data Flow

#### Job Listing and Search Flow
```javascript
// 1. User initiates search/filter action
const searchParams = {
  search_term: 'developer',
  stage: 'Active',
  location: 'Remote',
  page: 1,
  chunk_size: 10
};

// 2. Frontend sends API request
const response = await api.post('/position/positions_pagination/', searchParams);

// 3. Backend processes request
// - Validates parameters
// - Queries database with filters
// - Applies pagination
// - Returns structured response

// 4. Frontend processes response
const { data: jobs, total, page, total_pages } = response.data;

// 5. State update triggers UI re-render
setJobs(jobs);
setPagination({ total, page, total_pages });
```

#### Job Details Data Flow
```javascript
// 1. User clicks on job row
const jobId = 'job-uuid-123';

// 2. Navigation to job details page
navigate(`/job/${jobId}`);

// 3. Job details component loads
useEffect(() => {
  // Fetch job details if not in cache
  if (!jobCache[jobId]) {
    fetchJobDetails(jobId);
  }
}, [jobId]);

// 4. Tab-based data loading
const handleTabChange = async (tabKey) => {
  switch (tabKey) {
    case 'matching':
      await fetchMatchingCandidates(jobId);
      break;
    case 'interview':
      await fetchInterviewQuestions(jobId);
      break;
  }
};
```

### Candidate Management Data Flow

#### Candidate Search and Filtering Flow
```javascript
// 1. User applies filters
const candidateFilters = {
  search_term: 'john',
  country: 'US',
  role: 'Developer',
  status: 'active',
  page: 1,
  chunk_size: 10
};

// 2. API request with filters
const candidatesResponse = await api.post('/candidate/candidates_pagination/', candidateFilters);

// 3. Response processing
const { data: candidates, total } = candidatesResponse.data;

// 4. State management with NavigationContext
const { setCandidatesTableState } = useNavigationContext();
setCandidatesTableState({
  searchTerm: candidateFilters.search_term,
  page: candidateFilters.page,
  filters: candidateFilters
});

// 5. UI update with preserved state
setCandidates(candidates);
```

#### File Upload Data Flow
```javascript
// 1. User selects files
const handleFileUpload = async (files) => {
  const formData = new FormData();
  files.forEach(file => formData.append('files', file));
  formData.append('project_id', projectId);
  formData.append('created_by', currentUser);

  // 2. Upload with progress tracking
  const response = await api.post('/candidate/uploadfiles/', formData, {
    headers: { 'Content-Type': 'multipart/form-data' },
    onUploadProgress: (progressEvent) => {
      const progress = (progressEvent.loaded / progressEvent.total) * 100;
      setUploadProgress(progress);
    }
  });

  // 3. Process upload results
  const { uploaded_files, duplicates, errors } = response.data;
  
  // 4. Update UI based on results
  if (uploaded_files.length > 0) {
    showSuccessNotification(`${uploaded_files.length} files uploaded successfully`);
    refreshCandidateList();
  }
  
  if (duplicates.length > 0) {
    showWarningNotification(`${duplicates.length} duplicate files found`);
  }
  
  if (errors.length > 0) {
    showErrorNotification(`${errors.length} files failed to upload`);
  }
};
```

### Interview Generation Data Flow

#### Question Generation Flow
```javascript
// 1. User configures question generation
const questionConfig = {
  n_questions: 10,
  include: ['Technical Skills', 'Soft Skills', 'Methodologies'],
  current_user: '<EMAIL>'
};

// 2. API request for question generation
setGenerating(true);
try {
  const response = await api.post(`/interview/${positionId}/questions`, null, {
    params: questionConfig
  });

  // 3. Process generated questions
  const { questions } = response.data;
  
  // 4. Update state and UI
  setQuestions(questions);
  setGenerating(false);
  showSuccessNotification('Questions generated successfully');
  
} catch (error) {
  // 5. Error handling
  setGenerating(false);
  showErrorNotification('Failed to generate questions');
}
```

#### Interview Feedback Submission Flow
```javascript
// 1. User fills feedback form
const feedbackData = {
  position_id: positionId,
  candidate_id: candidateId,
  recruiter_hr_id: formData.recruiter,
  feedback_hr: formData.feedback,
  interview_date_hr: formData.interviewDate,
  status_hr: 'completed',
  recommendation_hr: formData.recommendation === 'Yes'
};

// 2. Form validation
const validationErrors = validateFeedbackForm(feedbackData);
if (validationErrors.length > 0) {
  setFormErrors(validationErrors);
  return;
}

// 3. Submit feedback
setSubmitting(true);
try {
  await api.put('/interview/hr', feedbackData);
  
  // 4. Success handling
  setSubmitting(false);
  showSuccessNotification('Feedback submitted successfully');
  
  // 5. Update local state
  updateCandidateStatus(candidateId, 'hr_completed');
  
} catch (error) {
  // 6. Error handling
  setSubmitting(false);
  handleSubmissionError(error);
}
```

## 🏗️ State Management Patterns

### Context-Based State Management

#### JobContext Pattern
```javascript
const JobContext = createContext();

export const JobProvider = ({ children }) => {
  const [currentJob, setCurrentJob] = useState(null);
  const [jobQuestions, setJobQuestions] = useState([]);
  const [selectedCandidates, setSelectedCandidates] = useState([]);
  const [matchingResults, setMatchingResults] = useState([]);

  const value = {
    currentJob,
    setCurrentJob,
    jobQuestions,
    setJobQuestions,
    selectedCandidates,
    setSelectedCandidates,
    matchingResults,
    setMatchingResults,
    
    // Actions
    loadJobDetails: async (jobId) => {
      const job = await fetchJobDetails(jobId);
      setCurrentJob(job);
    },
    
    generateQuestions: async (config) => {
      const questions = await generateInterviewQuestions(currentJob.id, config);
      setJobQuestions(questions);
    }
  };

  return <JobContext.Provider value={value}>{children}</JobContext.Provider>;
};
```

#### NavigationContext Pattern
```javascript
const NavigationContext = createContext();

export const NavigationProvider = ({ children }) => {
  const [previousPath, setPreviousPath] = useState(null);
  const [candidatesTableState, setCandidatesTableState] = useState({
    searchTerm: null,
    page: 1,
    limit: 10,
    filters: {}
  });
  const [jobOrdersState, setJobOrdersState] = useState({
    searchTerm: '',
    page: 1,
    limit: 10,
    filters: {}
  });

  // Persist state to localStorage
  useEffect(() => {
    localStorage.setItem('candidatesTableState', JSON.stringify(candidatesTableState));
  }, [candidatesTableState]);

  useEffect(() => {
    localStorage.setItem('jobOrdersState', JSON.stringify(jobOrdersState));
  }, [jobOrdersState]);

  return (
    <NavigationContext.Provider value={{
      previousPath,
      setPreviousPath,
      candidatesTableState,
      setCandidatesTableState,
      jobOrdersState,
      setJobOrdersState
    }}>
      {children}
    </NavigationContext.Provider>
  );
};
```

### Local Storage Integration
```javascript
// Persist important state to localStorage
const persistState = (key, state) => {
  try {
    localStorage.setItem(key, JSON.stringify(state));
  } catch (error) {
    console.warn('Failed to persist state to localStorage:', error);
  }
};

// Restore state from localStorage
const restoreState = (key, defaultState) => {
  try {
    const stored = localStorage.getItem(key);
    return stored ? JSON.parse(stored) : defaultState;
  } catch (error) {
    console.warn('Failed to restore state from localStorage:', error);
    return defaultState;
  }
};
```

## 🔄 Real-time Data Patterns

### WebSocket Integration (Future Enhancement)
```javascript
// WebSocket connection for real-time updates
const useWebSocket = (url) => {
  const [socket, setSocket] = useState(null);
  const [isConnected, setIsConnected] = useState(false);

  useEffect(() => {
    const ws = new WebSocket(url);
    
    ws.onopen = () => {
      setIsConnected(true);
      setSocket(ws);
    };
    
    ws.onmessage = (event) => {
      const data = JSON.parse(event.data);
      handleRealTimeUpdate(data);
    };
    
    ws.onclose = () => {
      setIsConnected(false);
      setSocket(null);
    };
    
    return () => {
      ws.close();
    };
  }, [url]);

  return { socket, isConnected };
};

// Handle real-time updates
const handleRealTimeUpdate = (update) => {
  switch (update.type) {
    case 'candidate_status_change':
      updateCandidateStatus(update.candidate_id, update.status);
      break;
    case 'interview_scheduled':
      refreshInterviewList();
      break;
    case 'questions_generated':
      refreshQuestionsList(update.position_id);
      break;
  }
};
```

### Polling Pattern for Status Updates
```javascript
// Polling for long-running operations
const usePolling = (url, interval = 2000, condition) => {
  const [data, setData] = useState(null);
  const [isPolling, setIsPolling] = useState(false);

  const startPolling = useCallback(() => {
    setIsPolling(true);
    
    const poll = async () => {
      try {
        const response = await api.get(url);
        setData(response.data);
        
        if (condition && condition(response.data)) {
          setIsPolling(false);
          return;
        }
        
        if (isPolling) {
          setTimeout(poll, interval);
        }
      } catch (error) {
        console.error('Polling error:', error);
        setIsPolling(false);
      }
    };
    
    poll();
  }, [url, interval, condition, isPolling]);

  const stopPolling = useCallback(() => {
    setIsPolling(false);
  }, []);

  return { data, isPolling, startPolling, stopPolling };
};

// Usage for question generation status
const { data: generationStatus, startPolling } = usePolling(
  `/interview/${positionId}/generation-status`,
  2000,
  (status) => status.completed
);
```

## 🚨 Error Handling Patterns

### Centralized Error Handling
```javascript
// Axios response interceptor for error handling
api.interceptors.response.use(
  (response) => response,
  (error) => {
    const { response } = error;
    
    if (response?.status === 401) {
      // Handle authentication errors
      handleAuthError();
    } else if (response?.status === 422) {
      // Handle validation errors
      handleValidationError(response.data.detail);
    } else if (response?.status >= 500) {
      // Handle server errors
      handleServerError(response.data.detail);
    } else if (!response) {
      // Handle network errors
      handleNetworkError();
    }
    
    return Promise.reject(error);
  }
);

// Error handling functions
const handleValidationError = (errors) => {
  if (Array.isArray(errors)) {
    errors.forEach(error => {
      showFieldError(error.field, error.message);
    });
  } else {
    showErrorNotification('Validation error occurred');
  }
};

const handleServerError = (message) => {
  showErrorNotification(message || 'Server error occurred');
};

const handleNetworkError = () => {
  showErrorNotification('Network error. Please check your connection.');
};
```

This comprehensive data flow patterns documentation provides detailed understanding of how data moves through the SmartHR application for effective automated testing.
