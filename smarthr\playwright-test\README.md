# SmartHR Playwright Test Documentation

## Overview

This documentation provides comprehensive guidance for automated testing of the SmartHR application using <PERSON>wright. The test suite focuses on the interview question generation workflow, specifically the "Generate New Questions" functionality.

## Project Structure

```
smarthr/
├── smarthr-fe/                          # Frontend application
│   ├── tests/
│   │   └── e2e/
│   │       ├── generate-new-questions.spec.ts    # Main test file
│   │       ├── enhanced-interview-generation.spec.ts
│   │       └── interview-generation-with-export.spec.ts
│   ├── generated-questions/              # Auto-generated questions folder
│   ├── test-results/                     # Test execution results
│   └── playwright.config.ts              # Playwright configuration
├── smarthr-be/                          # Backend (Docker container)
└── playwright-test/                     # This documentation folder
    ├── README.md                         # This file
    ├── test-workflow-guide.md           # Detailed workflow guide
    ├── button-selectors.md              # CSS selectors reference
    └── troubleshooting.md               # Common issues and solutions
```

## Key Test: Generate New Questions

### Purpose
Automates the complete workflow of generating new interview questions for specific job positions in SmartHR.

### Test File Location
`smarthr-fe/tests/e2e/generate-new-questions.spec.ts`

### Workflow Steps
1. **Navigate to SmartHR homepage**
2. **Go to Jobs page**
3. **Select specific job position** (e.g., "Salesforce QA")
4. **Click "Generate AI Interview" button** (opens modal)
5. **Click "Generate New Questions" button** (in modal)
6. **Wait for generation to complete** (monitors button loading state)
7. **Extract generated questions and answers**
8. **Save to `generated-questions/` folder**

## Critical Technical Details

### Button Selectors (IMPORTANT)

#### Primary Button: Generate AI Interview
```css
#root > div > div > main > div > div:nth-child(2) > div:nth-child(1) > div > div:nth-child(4)
```

#### Secondary Button: Generate New Questions (in modal)
```css
#root > div > div > main > div > div:nth-child(2) > div:nth-child(2) > div > div.ant-col.ant-col-xs-24.ant-col-md-14.css-dev-only-do-not-override-ni1kz0 > div:nth-child(1) > div > div > div:nth-child(1) > button
```

### Loading Detection Strategy

**CRITICAL**: The test must wait for the correct loader to complete:

1. **Two Loaders Exist**:
   - Loader 1: Candidate list loading (ignore this)
   - Loader 2: Interview generation loading (wait for this)

2. **Correct Loading Detection**:
   ```typescript
   // Wait for "Generate AI Interview" button to stop loading
   const generateAILoadingButton = page.locator('.ant-btn-loading:has-text("Generate AI Interview")');
   await generateAILoadingButton.waitFor({ state: 'hidden', timeout: 60000 });
   ```

3. **Button States to Monitor**:
   - `button:has-text("loading Generate AI Interview")` - Loading state
   - `button:has-text("Generate AI Interview"):not(.ant-btn-loading)` - Normal state

### Question Parsing Logic

The test extracts questions using this pattern:

1. **Question Identification**: Look for numbered questions (`1.`, `2.`, `3.`, etc.)
2. **Answer Extraction**: Extract text after `"Expected Response:"`
3. **Category Detection**: Identify skill categories (TECHNICAL SKILLS, SOFT SKILLS, etc.)

```typescript
// Parsing pattern
const questionPattern = /(\d+)\.\s*([^]*?)(?=\d+\.\s|$)/g;
const parts = questionContent.split('Expected Response:');
```

## Configuration Requirements

### Environment Setup
- **Frontend**: localhost:5173 (Vite dev server)
- **Backend**: localhost:8080 (Docker container)
- **Browser**: Chromium (primary), supports all browsers including mobile

### Playwright Config Key Settings
```typescript
// playwright.config.ts
use: {
  baseURL: 'http://localhost:5173',
  timeout: 30000,
  actionTimeout: 15000,
}

projects: [
  { name: 'chromium', use: { ...devices['Desktop Chrome'] } },
  { name: 'Mobile Chrome', use: { ...devices['Pixel 5'] } },
  { name: 'Mobile Safari', use: { ...devices['iPhone 12'] } }
]
```

## Running the Tests

### Basic Execution
```bash
# Run the main Generate New Questions test
npx playwright test tests/e2e/generate-new-questions.spec.ts --headed

# Run on specific browser
npx playwright test tests/e2e/generate-new-questions.spec.ts --project=chromium --headed

# Run on all browsers
npx playwright test tests/e2e/generate-new-questions.spec.ts
```

### Advanced Options
```bash
# Run with debug mode
npx playwright test tests/e2e/generate-new-questions.spec.ts --debug

# Run with trace
npx playwright test tests/e2e/generate-new-questions.spec.ts --trace on

# Generate HTML report
npx playwright show-report
```

## Output Files

### Generated Questions Folder
- **Location**: `smarthr-fe/generated-questions/`
- **Format**: `[Position-Name]-questions-[timestamp].md`
- **Content**: Numbered questions with Expected Response answers

### Test Results
- **Location**: `smarthr-fe/test-results/`
- **Screenshots**: Visual documentation of each step
- **Reports**: Comprehensive test execution details

## Success Metrics

A successful test execution should show:
```
✅ Found [Position Name] position
✅ Clicked "Generate AI Interview" button
✅ Found "Generate New Questions" button in modal!
✅ Set question count to 10
✅ Clicked "Generate New Questions" button
✅ "[Generate AI Interview]" button is loading - waiting for generation to complete...
✅ Button loading completed, interview generation should be finished
✅ Extracted [N] questions
✅ Questions and answers saved to: generated-questions/[filename].md
```

## Common Issues and Solutions

### Issue 1: Button Not Found
**Problem**: "Generate AI Interview" button not visible
**Solution**: Check if position is selected correctly, verify selector path

### Issue 2: Wrong Loader Detection
**Problem**: Test completes too early, questions not generated
**Solution**: Ensure waiting for "Generate AI Interview" button loading state, not candidate list

### Issue 3: No Questions Extracted
**Problem**: Questions generated but not parsed correctly
**Solution**: Check question format, verify parsing regex patterns

### Issue 4: Position Not Found
**Problem**: Specific job position (e.g., "Salesforce QA") not available
**Solution**: Update position selection logic with fallback options

## Future Enhancements

### Potential Improvements
1. **Dynamic Position Selection**: Allow test to accept position name as parameter
2. **Question Count Configuration**: Make question count configurable
3. **Skill Category Selection**: Automate skill category checkbox selection
4. **Multi-Position Testing**: Run test for multiple positions in sequence
5. **Question Quality Validation**: Add validation for question content quality

### Extensibility
The test framework is designed to be easily extended for:
- Different job positions
- Various question types
- Multiple skill categories
- Different output formats
- Integration with other SmartHR workflows

## Maintenance Notes

### Regular Updates Required
1. **Selector Validation**: Verify CSS selectors if UI changes
2. **Loading Logic**: Update loading detection if button behavior changes
3. **Parsing Patterns**: Adjust question parsing if format changes
4. **Position Names**: Update available positions list

### Version Compatibility
- **Playwright**: v1.40+
- **Node.js**: v18+
- **SmartHR Frontend**: Current version with Vite + React
- **SmartHR Backend**: Docker container with FastAPI

---

*Documentation created: November 2025*  
*Last updated: November 2025*  
*Maintained by: SmartHR Development Team*
