# SmartHR Testing Best Practices and Common Patterns

This document provides comprehensive testing best practices, common patterns, and guidelines specifically designed for AI agents testing the SmartHR application.

## 📋 Core Testing Principles

### 1. Reliability First
- **Stable Selectors**: Prefer data-testid attributes over CSS classes
- **Explicit Waits**: Always wait for elements and network requests
- **Idempotent Tests**: Tests should produce same results regardless of execution order
- **Cleanup**: Ensure tests clean up after themselves

### 2. Maintainability
- **Page Object Models**: Use POM pattern for reusable components
- **DRY Principle**: Avoid duplicating test logic
- **Clear Naming**: Use descriptive test and function names
- **Documentation**: Comment complex test logic

### 3. Performance
- **Parallel Execution**: Design tests to run in parallel
- **Efficient Selectors**: Use the most efficient selector strategy
- **Resource Management**: Properly manage browser contexts and pages

## 🎯 Selector Strategy Best Practices

### Selector Priority Order
```javascript
// 1. Data-testid attributes (most reliable)
await page.click('[data-testid="generate-questions-button"]');

// 2. Ant Design component classes (reliable for UI components)
await page.click('.ant-btn-primary');

// 3. Text content selectors (good for buttons and labels)
await page.click('button:has-text("Generate")');

// 4. CSS selectors (use when above options aren't available)
await page.click('.question-generation-form input[type="number"]');

// 5. Position-based selectors (avoid when possible)
// await page.click('.ant-table-row:nth-child(1)'); // Avoid this
```

### Robust Selector Patterns
```javascript
// Combine selectors for specificity
const generateButton = page.locator('.ant-btn-primary:has-text("Generate")');

// Use contains for partial text matching
const searchInput = page.locator('input[placeholder*="Search"]');

// Chain selectors for nested elements
const firstJobTitle = page.locator('.ant-table-row').first().locator('.job-title');

// Use role-based selectors when appropriate
const submitButton = page.getByRole('button', { name: 'Submit' });
```

## ⏱️ Wait Strategy Best Practices

### Network-Aware Waiting
```javascript
// Wait for network idle after navigation
await page.goto('http://localhost:5173');
await page.waitForLoadState('networkidle');

// Wait for specific API calls to complete
await page.waitForResponse('**/api/position/positions_pagination/');

// Wait for loading spinners to disappear
await page.waitForSelector('.ant-spin-spinning', { state: 'detached' });
```

### Element State Waiting
```javascript
// Wait for element to be visible and enabled
await page.waitForSelector('.ant-btn-primary', { state: 'visible' });
await expect(page.locator('.ant-btn-primary')).toBeEnabled();

// Wait for form to be ready for interaction
await page.waitForSelector('.ant-form', { timeout: 10000 });
await page.waitForFunction(() => {
  const form = document.querySelector('.ant-form');
  return form && !form.classList.contains('ant-form-loading');
});
```

### Custom Wait Conditions
```javascript
// Wait for specific data to load
const waitForJobsToLoad = async (page) => {
  await page.waitForFunction(() => {
    const table = document.querySelector('.ant-table-tbody');
    const rows = table?.querySelectorAll('.ant-table-row');
    const emptyState = document.querySelector('.ant-empty');
    return (rows && rows.length > 0) || emptyState;
  }, { timeout: 15000 });
};

// Wait for question generation to complete
const waitForQuestionsGenerated = async (page) => {
  await page.waitForFunction(() => {
    const questionsContainer = document.querySelector('[data-testid="questions-container"]');
    const questions = questionsContainer?.querySelectorAll('.question-item');
    return questions && questions.length > 0;
  }, { timeout: 30000 });
};
```

## 🔄 Common Test Patterns

### Data-Driven Testing Pattern
```javascript
const testCases = [
  { searchTerm: 'Developer', expectedMinResults: 1 },
  { searchTerm: 'Manager', expectedMinResults: 0 },
  { searchTerm: 'Designer', expectedMinResults: 0 }
];

for (const testCase of testCases) {
  test(`Search for ${testCase.searchTerm}`, async ({ page }) => {
    await page.goto('http://localhost:5173');
    await page.fill('input[placeholder*="Search"]', testCase.searchTerm);
    await page.press('input[placeholder*="Search"]', 'Enter');
    await page.waitForSelector('.ant-spin-spinning', { state: 'detached' });
    
    const resultCount = await page.locator('.ant-table-row').count();
    expect(resultCount).toBeGreaterThanOrEqual(testCase.expectedMinResults);
  });
}
```

### Conditional Testing Pattern
```javascript
const testWithConditionalFlow = async (page) => {
  await page.goto('http://localhost:5173');
  
  const hasJobs = await page.locator('.ant-table-row').count() > 0;
  const hasEmptyState = await page.locator('.ant-empty').isVisible();
  
  if (hasJobs) {
    // Test job selection flow
    await page.click('.ant-table-row:first-child');
    await page.waitForURL('**/job/**');
    await expect(page.locator('.ant-tabs')).toBeVisible();
  } else if (hasEmptyState) {
    // Test empty state behavior
    await expect(page.locator('.ant-empty')).toBeVisible();
    console.log('No jobs available - testing empty state');
  } else {
    throw new Error('Unexpected page state');
  }
};
```

### Retry Pattern for Flaky Operations
```javascript
const retryOperation = async (operation, maxRetries = 3) => {
  for (let attempt = 1; attempt <= maxRetries; attempt++) {
    try {
      await operation();
      return; // Success
    } catch (error) {
      if (attempt === maxRetries) {
        throw error; // Final attempt failed
      }
      console.log(`Attempt ${attempt} failed, retrying...`);
      await page.waitForTimeout(1000 * attempt); // Exponential backoff
    }
  }
};

// Usage
await retryOperation(async () => {
  await page.click('.ant-btn:has-text("Generate")');
  await page.waitForSelector('.ant-notification-success', { timeout: 10000 });
});
```

## 🧪 Test Organization Patterns

### Test Suite Structure
```javascript
test.describe('SmartHR Job Management', () => {
  test.beforeEach(async ({ page }) => {
    // Common setup for all tests in this suite
    await page.goto('http://localhost:5173');
    await page.waitForSelector('.ant-table, .ant-empty', { timeout: 10000 });
  });

  test.describe('Job Listing', () => {
    test('should display job listings', async ({ page }) => {
      // Test implementation
    });

    test('should handle empty state', async ({ page }) => {
      // Test implementation
    });
  });

  test.describe('Job Search', () => {
    test('should search jobs by title', async ({ page }) => {
      // Test implementation
    });

    test('should filter jobs by stage', async ({ page }) => {
      // Test implementation
    });
  });
});
```

### Fixture Pattern for Reusable Setup
```javascript
// fixtures.js
import { test as base } from '@playwright/test';
import { JobOrdersPage } from './page-objects/job-orders-page.js';
import { JobDetailsPage } from './page-objects/job-details-page.js';

export const test = base.extend({
  jobOrdersPage: async ({ page }, use) => {
    const jobOrdersPage = new JobOrdersPage(page);
    await use(jobOrdersPage);
  },
  
  jobDetailsPage: async ({ page }, use) => {
    const jobDetailsPage = new JobDetailsPage(page);
    await use(jobDetailsPage);
  }
});

// Usage in tests
test('Job workflow with fixtures', async ({ jobOrdersPage, jobDetailsPage }) => {
  await jobOrdersPage.navigateToJobOrders();
  await jobOrdersPage.clickFirstJob();
  await jobDetailsPage.clickInterviewTab();
});
```

## 🚨 Error Handling Best Practices

### Graceful Error Handling
```javascript
const handleExpectedErrors = async (page, operation) => {
  try {
    await operation();
  } catch (error) {
    // Check if it's an expected error condition
    const hasErrorNotification = await page.locator('.ant-notification-error').isVisible();
    const hasValidationError = await page.locator('.ant-form-item-has-error').isVisible();
    
    if (hasErrorNotification || hasValidationError) {
      console.log('Expected error condition handled gracefully');
      return { success: false, handled: true };
    } else {
      throw error; // Unexpected error
    }
  }
  
  return { success: true, handled: false };
};
```

### Network Error Simulation
```javascript
const testNetworkResilience = async (page) => {
  // Simulate network failure
  await page.route('**/api/**', route => route.abort());
  
  try {
    await page.click('.ant-btn:has-text("Generate")');
    
    // Should show error handling
    const errorShown = await Promise.race([
      page.waitForSelector('.ant-notification-error', { timeout: 10000 }).then(() => true),
      page.waitForSelector('.error-message', { timeout: 10000 }).then(() => true),
      page.waitForTimeout(10000).then(() => false)
    ]);
    
    expect(errorShown).toBeTruthy();
    
  } finally {
    // Restore network
    await page.unroute('**/api/**');
  }
};
```

## 📊 Performance Testing Patterns

### Load Time Measurement
```javascript
const measurePageLoadTime = async (page, url) => {
  const startTime = Date.now();
  
  await page.goto(url);
  await page.waitForLoadState('networkidle');
  
  const loadTime = Date.now() - startTime;
  console.log(`Page loaded in ${loadTime}ms`);
  
  // Performance assertion
  expect(loadTime).toBeLessThan(5000); // 5 second budget
  
  return loadTime;
};
```

### Memory Usage Monitoring
```javascript
const monitorMemoryUsage = async (page) => {
  const getMemoryUsage = () => page.evaluate(() => {
    if (performance.memory) {
      return {
        used: performance.memory.usedJSHeapSize,
        total: performance.memory.totalJSHeapSize
      };
    }
    return null;
  });

  const initialMemory = await getMemoryUsage();
  
  // Perform memory-intensive operations
  await performTestOperations(page);
  
  const finalMemory = await getMemoryUsage();
  
  if (initialMemory && finalMemory) {
    const memoryIncrease = finalMemory.used - initialMemory.used;
    const increasePercent = (memoryIncrease / initialMemory.used) * 100;
    
    console.log(`Memory increase: ${increasePercent.toFixed(2)}%`);
    expect(increasePercent).toBeLessThan(50); // 50% increase threshold
  }
};
```

## 🔍 Debugging and Troubleshooting

### Debug Information Collection
```javascript
const collectDebugInfo = async (page, testName) => {
  const debugInfo = {
    url: page.url(),
    title: await page.title(),
    timestamp: new Date().toISOString(),
    viewport: await page.viewportSize(),
    userAgent: await page.evaluate(() => navigator.userAgent)
  };
  
  // Take screenshot for debugging
  await page.screenshot({ 
    path: `debug-screenshots/${testName}-${Date.now()}.png`,
    fullPage: true 
  });
  
  // Collect console logs
  const logs = [];
  page.on('console', msg => logs.push(`${msg.type()}: ${msg.text()}`));
  
  console.log('Debug Info:', JSON.stringify(debugInfo, null, 2));
  
  return debugInfo;
};
```

### Test Stability Helpers
```javascript
const ensureStableState = async (page) => {
  // Wait for all animations to complete
  await page.waitForFunction(() => {
    const animations = document.getAnimations();
    return animations.every(animation => 
      animation.playState === 'finished' || animation.playState === 'idle'
    );
  });
  
  // Wait for any pending network requests
  await page.waitForLoadState('networkidle');
  
  // Wait for React to finish rendering
  await page.waitForFunction(() => {
    return window.React && window.React.version;
  });
};
```

## 📝 Documentation and Reporting

### Test Documentation Pattern
```javascript
test('Interview question generation workflow', async ({ page }) => {
  // Test Description: Verify that users can generate interview questions
  // Prerequisites: Job position exists in the system
  // Expected Outcome: Questions are generated and displayed
  
  test.info().annotations.push({
    type: 'feature',
    description: 'Interview question generation'
  });
  
  test.info().annotations.push({
    type: 'severity',
    description: 'critical'
  });
  
  // Test implementation with detailed logging
  console.log('Step 1: Navigate to job details');
  await page.goto('http://localhost:5173/job/test-job-id');
  
  console.log('Step 2: Switch to interview tab');
  await page.click('.ant-tabs-tab:has-text("Interview")');
  
  console.log('Step 3: Configure question generation');
  await page.fill('.ant-input-number input', '10');
  
  console.log('Step 4: Generate questions');
  await page.click('button:has-text("Generate")');
  
  console.log('Step 5: Verify questions are generated');
  const questionCount = await page.locator('.question-item').count();
  expect(questionCount).toBeGreaterThan(0);
  
  console.log(`Test completed successfully. Generated ${questionCount} questions.`);
});
```

This comprehensive testing best practices guide provides AI agents with proven patterns and strategies for reliable, maintainable, and efficient testing of the SmartHR application.
