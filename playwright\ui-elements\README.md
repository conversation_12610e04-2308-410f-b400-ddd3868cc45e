# SmartHR UI Elements Reference

This directory contains comprehensive documentation for UI elements, selectors, and interaction patterns in the SmartHR application. These references are essential for reliable automated testing.

## 📋 Available References

### Core UI Documentation

1. **[Selectors Reference](selectors-reference.md)**
   - Complete catalog of UI element selectors
   - Ant Design component patterns
   - Custom component selectors
   - Data-testid attributes

2. **[Navigation Patterns](navigation-patterns.md)**
   - Application routing and navigation
   - Menu and tab interactions
   - Breadcrumb navigation
   - State-based navigation

3. **[Form Interactions](form-interactions.md)**
   - Form field selectors and interactions
   - Validation patterns and error handling
   - Submit and cancel actions
   - Dynamic form elements

## 🎯 UI Element Categories

### Layout Components
- **Header**: Navigation, user menu, authentication
- **Sidebar**: Tab navigation, section switching
- **Content Area**: Main application content
- **Footer**: Status information, pagination

### Data Display Components
- **Tables**: Job listings, candidate lists, interview data
- **Cards**: Job cards, candidate cards, summary cards
- **Lists**: Question lists, feedback lists, activity lists
- **Modals**: Confirmation dialogs, detail views, forms

### Input Components
- **Text Inputs**: Search fields, form inputs, text areas
- **Selectors**: Dropdowns, multi-select, date pickers
- **Checkboxes**: Skill categories, filter options
- **Buttons**: Actions, navigation, form submission

### Feedback Components
- **Notifications**: Success, error, warning messages
- **Loading States**: Spinners, progress indicators
- **Empty States**: No data messages, placeholders
- **Validation**: Field errors, form validation

## 🔍 Selector Strategy

### Priority Order
1. **Data-testid attributes** (highest reliability)
   ```javascript
   page.locator('[data-testid="job-details"]')
   ```

2. **Ant Design class names** (good reliability)
   ```javascript
   page.locator('.ant-btn-primary')
   page.locator('.ant-table-row')
   ```

3. **Text content selectors** (moderate reliability)
   ```javascript
   page.locator('button:has-text("Generate")')
   page.locator('.ant-btn:has-text("Save")')
   ```

4. **CSS selectors** (use sparingly)
   ```javascript
   page.locator('input[type="text"]')
   page.locator('.custom-class')
   ```

### Selector Best Practices

#### Reliable Selectors
- Use data-testid when available
- Prefer Ant Design component classes
- Combine selectors for specificity
- Use text content for buttons and labels

#### Avoid These Patterns
- Generic CSS classes (`.btn`, `.form`)
- Position-based selectors (`:nth-child()`)
- Overly specific nested selectors
- Dynamic class names or IDs

## 🧪 Interaction Patterns

### Common Interaction Sequences

#### Navigation
```javascript
// Navigate to job details
await page.click('.ant-table-row:has-text("Software Engineer")');
await page.waitForURL('**/job/**');

// Switch tabs
await page.click('.ant-tabs-tab:has-text("Interview")');
await page.waitForSelector('.ant-spin-spinning', { state: 'detached' });
```

#### Form Interactions
```javascript
// Fill form fields
await page.fill('input[placeholder="Search jobs..."]', 'Developer');
await page.selectOption('.ant-select', 'Active');
await page.check('.ant-checkbox:has-text("Technical Skills")');

// Submit form
await page.click('.ant-btn-primary:has-text("Apply")');
await page.waitForSelector('.ant-notification-success');
```

#### Modal Interactions
```javascript
// Open modal
await page.click('button:has-text("Create Interview")');
await page.waitForSelector('.ant-modal');

// Interact with modal content
await page.fill('.ant-modal input[type="text"]', 'Interview Title');
await page.click('.ant-modal .ant-btn-primary');

// Wait for modal to close
await page.waitForSelector('.ant-modal', { state: 'detached' });
```

## 🔄 State Management

### Loading States
```javascript
// Wait for loading to complete
await page.waitForSelector('.ant-spin-spinning', { state: 'detached' });

// Wait for specific content to load
await page.waitForSelector('.ant-table-row', { timeout: 10000 });
```

### Dynamic Content
```javascript
// Handle dynamic lists
const candidateCount = await page.locator('.candidate-card').count();
for (let i = 0; i < candidateCount; i++) {
  await page.locator('.candidate-card').nth(i).click();
}
```

### Error States
```javascript
// Handle error notifications
const errorNotification = page.locator('.ant-notification-error');
if (await errorNotification.isVisible()) {
  const errorMessage = await errorNotification.textContent();
  console.log('Error:', errorMessage);
}
```

## 📱 Responsive Considerations

### Mobile Adaptations
- Menu collapses to hamburger on mobile
- Tables become horizontally scrollable
- Modals may become full-screen
- Touch interactions replace hover states

### Viewport-Specific Selectors
```javascript
// Desktop navigation
const desktopMenu = page.locator('.ant-menu-horizontal');

// Mobile navigation
const mobileMenu = page.locator('.ant-drawer');

// Responsive table
const tableScroll = page.locator('.ant-table-scroll');
```

## 🔗 Integration with Existing Tests

### Page Object Models
These UI element references align with existing Page Object Models in `smarthr-fe/tests/pages/`:
- JobOrdersPage.ts
- JobDetailsPage.ts
- CandidatesPage.ts

### Test Utilities
Use with existing test utilities in `smarthr-fe/tests/utils/`:
- test-helpers.ts
- assertions.ts
- auth.ts

## 📝 Maintenance Guidelines

### Keeping References Updated
1. **Regular Review**: Check selectors against UI changes
2. **Version Tracking**: Note when selectors change
3. **Fallback Strategies**: Provide alternative selectors
4. **Documentation Updates**: Keep examples current

### Adding New Elements
1. **Follow Naming Conventions**: Use consistent patterns
2. **Test Reliability**: Verify selectors work across browsers
3. **Document Context**: Explain when/where elements appear
4. **Provide Examples**: Include usage examples

These UI element references provide the foundation for reliable automated testing of the SmartHR application, ensuring consistent and maintainable test automation.
