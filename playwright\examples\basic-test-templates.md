# SmartHR Basic Test Templates

This document provides ready-to-use basic test templates for common workflows and interactions in the SmartHR application. These templates serve as starting points for automated testing.

## 📋 Template Structure

### Basic Test Setup Template
```javascript
import { test, expect } from '@playwright/test';

test.describe('SmartHR Application Tests', () => {
  test.beforeEach(async ({ page }) => {
    // Navigate to application
    await page.goto('http://localhost:5173');
    await page.waitForLoadState('networkidle');
    
    // Wait for initial page load
    await page.waitForSelector('.ant-table, .ant-empty', { timeout: 10000 });
  });

  test.afterEach(async ({ page }) => {
    // Cleanup after each test
    await page.close();
  });
});
```

## 🏢 Job Management Test Templates

### Job Listing and Search Template
```javascript
test('Job listing and search functionality', async ({ page }) => {
  // Test 1: Verify job listings load
  const jobTable = page.locator('.ant-table');
  await expect(jobTable).toBeVisible();
  
  const jobRows = page.locator('.ant-table-row');
  const jobCount = await jobRows.count();
  console.log(`Found ${jobCount} jobs`);
  
  if (jobCount === 0) {
    await expect(page.locator('.ant-empty')).toBeVisible();
    return;
  }

  // Test 2: Search functionality
  const searchInput = page.locator('input[placeholder*="Search"]');
  if (await searchInput.isVisible()) {
    await searchInput.fill('Developer');
    await searchInput.press('Enter');
    await page.waitForSelector('.ant-spin-spinning', { state: 'detached' });
    
    // Verify search results
    const searchResults = await page.locator('.ant-table-row').count();
    console.log(`Search returned ${searchResults} results`);
  }

  // Test 3: Filter functionality
  const stageFilter = page.locator('.ant-select:has(.ant-select-selection-item:has-text("Stage"))');
  if (await stageFilter.isVisible()) {
    await stageFilter.click();
    await page.click('.ant-select-item-option:has-text("Active")');
    await page.click('.ant-btn:has-text("Apply")');
    await page.waitForSelector('.ant-spin-spinning', { state: 'detached' });
  }

  // Test 4: Job details navigation
  const firstJob = jobRows.first();
  await firstJob.click();
  await page.waitForURL('**/job/**');
  await expect(page.locator('.ant-tabs')).toBeVisible();
});
```

### Job Details Navigation Template
```javascript
test('Job details tab navigation', async ({ page }) => {
  // Navigate to first job
  await page.click('.ant-table-row:first-child');
  await page.waitForURL('**/job/**');
  await page.waitForSelector('.ant-tabs', { timeout: 10000 });

  // Test tab navigation
  const tabs = [
    { name: 'Description', selector: '.ant-tabs-tab:has-text("Description")' },
    { name: 'Matching', selector: '.ant-tabs-tab:has-text("Matching")' },
    { name: 'Manual', selector: '.ant-tabs-tab:has-text("Manual")' },
    { name: 'Interview', selector: '.ant-tabs-tab:has-text("Interview")' }
  ];

  for (const tab of tabs) {
    const tabElement = page.locator(tab.selector);
    if (await tabElement.isVisible()) {
      await tabElement.click();
      await page.waitForSelector('.ant-spin-spinning', { state: 'detached' });
      
      // Verify active tab
      await expect(page.locator('.ant-tabs-tab-active')).toContainText(tab.name);
      console.log(`Successfully navigated to ${tab.name} tab`);
    }
  }
});
```

## 👥 Candidate Management Test Templates

### Candidate Listing Template
```javascript
test('Candidate listing and filtering', async ({ page }) => {
  // Navigate to candidates page
  await page.goto('http://localhost:5173/candidates');
  await page.waitForSelector('.ant-table, .ant-empty', { timeout: 10000 });

  // Test 1: Verify candidates display
  const candidateTable = page.locator('.ant-table');
  if (await candidateTable.isVisible()) {
    const candidateRows = page.locator('.ant-table-row');
    const candidateCount = await candidateRows.count();
    console.log(`Found ${candidateCount} candidates`);
    
    if (candidateCount > 0) {
      // Test candidate details navigation
      await candidateRows.first().click();
      await page.waitForURL('**/candidates/**');
      await expect(page.locator('.ant-card, [data-testid="candidate-details"]')).toBeVisible();
    }
  } else {
    await expect(page.locator('.ant-empty')).toBeVisible();
  }

  // Test 2: Search functionality
  await page.goto('http://localhost:5173/candidates');
  const searchInput = page.locator('input[placeholder*="Search"]');
  if (await searchInput.isVisible()) {
    await searchInput.fill('John');
    await searchInput.press('Enter');
    await page.waitForSelector('.ant-spin-spinning', { state: 'detached' });
  }

  // Test 3: Filter by role
  const roleFilter = page.locator('.ant-select:has(.ant-select-selection-item:has-text("Role"))');
  if (await roleFilter.isVisible()) {
    await roleFilter.click();
    const roleOptions = page.locator('.ant-select-item-option');
    if (await roleOptions.count() > 0) {
      await roleOptions.first().click();
      await page.click('.ant-btn:has-text("Apply")');
      await page.waitForSelector('.ant-spin-spinning', { state: 'detached' });
    }
  }
});
```

## 🎤 Interview Generation Test Templates

### Question Generation Template
```javascript
test('Interview question generation', async ({ page }) => {
  // Navigate to job details and interview tab
  await page.click('.ant-table-row:first-child');
  await page.waitForURL('**/job/**');
  
  const interviewTab = page.locator('.ant-tabs-tab:has-text("Interview")');
  await interviewTab.click();
  await page.waitForSelector('.ant-spin-spinning', { state: 'detached' });

  // Test 1: Configure question generation
  const questionCountInput = page.locator('.ant-input-number input');
  if (await questionCountInput.isVisible()) {
    await questionCountInput.fill('10');
  }

  // Test 2: Select skill categories
  const skillCheckboxes = page.locator('.ant-checkbox:has-text("Technical"), .ant-checkbox:has-text("Soft")');
  const checkboxCount = await skillCheckboxes.count();
  
  for (let i = 0; i < Math.min(checkboxCount, 2); i++) {
    const checkbox = skillCheckboxes.nth(i);
    if (await checkbox.isVisible() && !await checkbox.locator('input').isChecked()) {
      await checkbox.click();
    }
  }

  // Test 3: Generate questions
  const generateButton = page.locator('button:has-text("Generate"), .ant-btn:has-text("Generate")');
  if (await generateButton.isVisible()) {
    await generateButton.click();
    
    // Wait for generation to complete
    await page.waitForSelector('.ant-spin-spinning', { state: 'detached', timeout: 30000 });
    
    // Verify questions are generated
    const questionsContainer = page.locator('[data-testid="questions-container"], .questions-list');
    if (await questionsContainer.isVisible()) {
      const questions = page.locator('.question-item, [data-testid="question"]');
      const questionCount = await questions.count();
      expect(questionCount).toBeGreaterThan(0);
      console.log(`Generated ${questionCount} questions`);
    }
  }
});
```

### Interview Feedback Template
```javascript
test('Interview feedback submission', async ({ page }) => {
  // Navigate to job with candidates
  await page.click('.ant-table-row:first-child');
  await page.waitForURL('**/job/**');
  
  const interviewTab = page.locator('.ant-tabs-tab:has-text("Interview")');
  await interviewTab.click();
  await page.waitForSelector('.ant-spin-spinning', { state: 'detached' });

  // Open candidate feedback
  const candidateCard = page.locator('.candidate-card, .ant-card').first();
  if (await candidateCard.isVisible()) {
    await candidateCard.click();
    await page.waitForSelector('.ant-drawer, .ant-modal', { timeout: 10000 });

    // Test HR feedback
    const hrTab = page.locator('.ant-tabs-tab:has-text("HR")');
    if (await hrTab.isVisible()) {
      await hrTab.click();
      
      // Fill HR feedback form
      await fillFeedbackForm(page, {
        recruiter: 'John Smith',
        status: 'completed',
        recommendation: 'Yes',
        comments: 'Excellent candidate with strong communication skills'
      });
      
      // Submit HR feedback
      const saveButton = page.locator('button:has-text("Save")');
      await saveButton.click();
      await page.waitForSelector('.ant-notification-success', { timeout: 10000 });
    }

    // Test Technical feedback
    const techTab = page.locator('.ant-tabs-tab:has-text("Technical")');
    if (await techTab.isVisible()) {
      await techTab.click();
      
      // Fill technical feedback form
      await fillFeedbackForm(page, {
        recruiter: 'Mike Johnson',
        status: 'completed',
        recommendation: 'Yes',
        comments: 'Strong technical skills and problem-solving ability'
      });
      
      // Submit technical feedback
      const saveButton = page.locator('button:has-text("Save")');
      await saveButton.click();
      await page.waitForSelector('.ant-notification-success', { timeout: 10000 });
    }
  }
});

// Helper function for filling feedback forms
async function fillFeedbackForm(page, data) {
  // Fill recruiter name
  const recruiterInput = page.locator('input[placeholder*="Recruiter"], input[name*="recruiter"]');
  if (await recruiterInput.isVisible()) {
    await recruiterInput.fill(data.recruiter);
  }

  // Set interview date
  const datePicker = page.locator('.ant-picker').first();
  if (await datePicker.isVisible()) {
    await datePicker.click();
    await page.click('.ant-picker-today-btn');
  }

  // Set status
  const statusSelect = page.locator('.ant-select:has(.ant-select-selection-item:has-text("Status"))');
  if (await statusSelect.isVisible()) {
    await statusSelect.click();
    await page.click(`.ant-select-item-option:has-text("${data.status}")`);
  }

  // Set recommendation
  const recommendationSelect = page.locator('.ant-select:has(.ant-select-selection-item:has-text("Recommendation"))');
  if (await recommendationSelect.isVisible()) {
    await recommendationSelect.click();
    await page.click(`.ant-select-item-option:has-text("${data.recommendation}")`);
  }

  // Fill comments
  const commentsTextArea = page.locator('textarea[placeholder*="Comments"], textarea[name*="comments"]');
  if (await commentsTextArea.isVisible()) {
    await commentsTextArea.fill(data.comments);
  }
}
```

## 🔍 Search and Filter Test Templates

### Universal Search Template
```javascript
test('Universal search functionality', async ({ page }) => {
  const testSearchScenarios = [
    { page: '/', searchTerm: 'Developer', expectedResults: 'jobs' },
    { page: '/candidates', searchTerm: 'John', expectedResults: 'candidates' }
  ];

  for (const scenario of testSearchScenarios) {
    await page.goto(`http://localhost:5173${scenario.page}`);
    await page.waitForSelector('.ant-table, .ant-empty', { timeout: 10000 });

    const searchInput = page.locator('input[placeholder*="Search"]');
    if (await searchInput.isVisible()) {
      // Clear previous search
      await searchInput.clear();
      
      // Perform search
      await searchInput.fill(scenario.searchTerm);
      await searchInput.press('Enter');
      await page.waitForSelector('.ant-spin-spinning', { state: 'detached' });

      // Verify search results
      const results = page.locator('.ant-table-row');
      const resultCount = await results.count();
      console.log(`Search for "${scenario.searchTerm}" returned ${resultCount} ${scenario.expectedResults}`);

      // Clear search
      await searchInput.clear();
      await searchInput.press('Enter');
      await page.waitForSelector('.ant-spin-spinning', { state: 'detached' });
    }
  }
});
```

### Filter Combination Template
```javascript
test('Combined filter functionality', async ({ page }) => {
  // Test multiple filters together
  const filters = [
    { type: 'select', selector: '.ant-select:has(.ant-select-selection-item:has-text("Stage"))', value: 'Active' },
    { type: 'select', selector: '.ant-select:has(.ant-select-selection-item:has-text("Location"))', value: 'Remote' },
    { type: 'search', selector: 'input[placeholder*="Search"]', value: 'Engineer' }
  ];

  for (const filter of filters) {
    if (filter.type === 'select') {
      const selectElement = page.locator(filter.selector);
      if (await selectElement.isVisible()) {
        await selectElement.click();
        await page.click(`.ant-select-item-option:has-text("${filter.value}")`);
      }
    } else if (filter.type === 'search') {
      const searchElement = page.locator(filter.selector);
      if (await searchElement.isVisible()) {
        await searchElement.fill(filter.value);
      }
    }
  }

  // Apply all filters
  const applyButton = page.locator('.ant-btn:has-text("Apply")');
  if (await applyButton.isVisible()) {
    await applyButton.click();
    await page.waitForSelector('.ant-spin-spinning', { state: 'detached' });
  }

  // Verify filtered results
  const filteredResults = await page.locator('.ant-table-row').count();
  console.log(`Combined filters returned ${filteredResults} results`);

  // Clear all filters
  const clearButton = page.locator('.ant-btn:has-text("Clear")');
  if (await clearButton.isVisible()) {
    await clearButton.click();
    await page.waitForSelector('.ant-spin-spinning', { state: 'detached' });
  }
});
```

## 🚨 Error Handling Test Templates

### Network Error Template
```javascript
test('Network error handling', async ({ page }) => {
  // Simulate network failure
  await page.route('**/api/**', route => route.abort());

  // Attempt to load data
  await page.goto('http://localhost:5173');
  
  // Check for error handling
  const errorMessage = page.locator('.ant-notification-error, .ant-message-error, .error-message');
  await expect(errorMessage).toBeVisible({ timeout: 10000 });

  const errorText = await errorMessage.textContent();
  expect(errorText).toMatch(/network|connection|failed|error/i);

  // Restore network
  await page.unroute('**/api/**');
  
  // Verify recovery
  await page.reload();
  await page.waitForSelector('.ant-table, .ant-empty', { timeout: 10000 });
});
```

### Form Validation Template
```javascript
test('Form validation handling', async ({ page }) => {
  // Navigate to a form (example: feedback form)
  await page.click('.ant-table-row:first-child');
  await page.waitForURL('**/job/**');
  
  const interviewTab = page.locator('.ant-tabs-tab:has-text("Interview")');
  await interviewTab.click();
  
  const candidateCard = page.locator('.candidate-card').first();
  if (await candidateCard.isVisible()) {
    await candidateCard.click();
    await page.waitForSelector('.ant-drawer', { timeout: 10000 });

    // Try to submit empty form
    const submitButton = page.locator('button:has-text("Save")');
    await submitButton.click();

    // Check for validation errors
    const validationErrors = page.locator('.ant-form-item-has-error, .ant-form-item-explain-error');
    const errorCount = await validationErrors.count();
    
    if (errorCount > 0) {
      console.log(`Found ${errorCount} validation errors`);
      expect(errorCount).toBeGreaterThan(0);
    }
  }
});
```

These basic test templates provide a solid foundation for automated testing of the SmartHR application, covering the most common workflows and interaction patterns.
