# SmartHR API Integration Documentation

This directory contains documentation for understanding the integration between the SmartHR frontend and backend APIs. This information is crucial for comprehensive testing that validates both UI interactions and underlying data operations.

## 📋 Available Documentation

### API Integration Guides

1. **[Backend Endpoints](backend-endpoints.md)**
   - Complete API endpoint reference
   - Request/response formats
   - Authentication requirements
   - Error response patterns

2. **[Data Flow Patterns](data-flow-patterns.md)**
   - Frontend-backend data flow patterns
   - State management and API integration
   - Real-time updates and WebSocket connections
   - Caching and data persistence

## 🎯 API Architecture Overview

### Backend Technology Stack
- **Framework**: FastAPI with Python 3.11+
- **Database**: PostgreSQL 15+
- **Authentication**: JWT tokens (bypassed in local development)
- **AI Integration**: Interview question generation
- **Real-time**: WebSocket connections for live updates

### Frontend API Integration
- **HTTP Client**: Axios for API requests
- **Base URL**: Configurable via environment variables
- **Error Handling**: Centralized error management
- **Loading States**: UI feedback for async operations

## 🔄 API Categories

### Core Business APIs

#### Job Management APIs
```
GET    /position/positions_pagination/     # Get paginated job listings
POST   /position/positions_pagination/     # Search/filter jobs
GET    /position/get/locations/            # Get available locations
GET    /position/get/clients/              # Get client names
```

#### Candidate Management APIs
```
GET    /candidate/candidates_pagination/   # Get paginated candidates
POST   /candidate/                         # Create new candidate
PUT    /candidate/                         # Update candidate
GET    /candidate/{id}                     # Get candidate details
DELETE /candidate/{id}/delete              # Delete candidate
POST   /candidate/uploadfiles/             # Upload candidate files
```

#### Interview Management APIs
```
POST   /interview/{position_id}/questions  # Generate interview questions
GET    /interview/{position_id}/questions  # Get generated questions
POST   /interview/{position_id}/create     # Create interviews
PUT    /interview/hr                       # Submit HR feedback
PUT    /interview/tec                      # Submit technical feedback
GET    /interview/candidate/{candidate_id} # Get candidate interviews
```

#### Matching and Analysis APIs
```
POST   /match                              # Match candidates to positions
POST   /match/custom                       # Custom matching with prompts
POST   /evaluate_candidate_custom_prompt   # Custom candidate evaluation
```

## 🔍 API Request Patterns

### Standard Request Format
```javascript
// GET request with parameters
const response = await api.get('/candidate/candidates_pagination/', {
  params: {
    chunk_size: 10,
    page: 1,
    search_term: 'developer',
    country: 'US'
  }
});
```

### POST Request with Body
```javascript
// POST request with JSON body
const response = await api.post('/interview/position-123/questions', {
  n_questions: 10,
  include: 'Technical Skills,Soft Skills',
  current_user: '<EMAIL>'
});
```

### File Upload Request
```javascript
// Multipart form data for file uploads
const formData = new FormData();
formData.append('files', file);
formData.append('project_id', 'project-123');

const response = await api.post('/candidate/uploadfiles/', formData, {
  headers: { 'Content-Type': 'multipart/form-data' }
});
```

## 📊 Response Patterns

### Success Response Format
```javascript
// Standard success response
{
  "data": [...],           // Response data
  "total": 150,           // Total count for pagination
  "page": 1,              // Current page
  "chunk_size": 10,       // Items per page
  "total_pages": 15       // Total pages
}
```

### Error Response Format
```javascript
// Standard error response
{
  "detail": "Error message",
  "status_code": 400,
  "error_type": "ValidationError"
}
```

### Interview Question Response
```javascript
// Interview questions response
{
  "questions": [
    {
      "question_number": 1,
      "question_text": "Explain the concept of...",
      "category": "Technical Skills",
      "junior_answer": "Basic explanation...",
      "mid_answer": "Intermediate explanation...",
      "senior_answer": "Advanced explanation..."
    }
  ]
}
```

## 🔐 Authentication Patterns

### Local Development
```javascript
// Authentication bypassed in local development
const isLocalDev = window.location.hostname === 'localhost';
// No authentication headers required
```

### Production Authentication
```javascript
// JWT token authentication (when not in local dev)
const token = await getAuthToken();
api.defaults.headers.common['Authorization'] = `Bearer ${token}`;
```

## 🚨 Error Handling Patterns

### Network Error Handling
```javascript
try {
  const response = await api.post('/endpoint', data);
  return response.data;
} catch (error) {
  if (error.response) {
    // Server responded with error status
    console.error('API Error:', error.response.data.detail);
    showErrorNotification(error.response.data.detail);
  } else if (error.request) {
    // Network error
    console.error('Network Error:', error.message);
    showErrorNotification('Network connection failed');
  } else {
    // Other error
    console.error('Error:', error.message);
    showErrorNotification('An unexpected error occurred');
  }
}
```

### Validation Error Handling
```javascript
// Handle form validation errors
if (error.response?.status === 422) {
  const validationErrors = error.response.data.detail;
  validationErrors.forEach(err => {
    setFieldError(err.field, err.message);
  });
}
```

## 🔄 Real-time Integration

### WebSocket Connections
```javascript
// WebSocket connection for real-time updates
const ws = new WebSocket('ws://localhost:8080/ws');

ws.onmessage = (event) => {
  const data = JSON.parse(event.data);
  handleRealTimeUpdate(data);
};
```

### Polling for Updates
```javascript
// Polling for status updates
const pollForUpdates = async (interviewId) => {
  const interval = setInterval(async () => {
    try {
      const response = await api.get(`/interview/${interviewId}/status`);
      if (response.data.status === 'completed') {
        clearInterval(interval);
        handleCompletedInterview(response.data);
      }
    } catch (error) {
      console.error('Polling error:', error);
    }
  }, 5000); // Poll every 5 seconds
};
```

## 🧪 Testing API Integration

### Mocking API Responses
```javascript
// Mock successful API response
await page.route('**/api/candidates/**', route => {
  route.fulfill({
    status: 200,
    contentType: 'application/json',
    body: JSON.stringify({
      data: mockCandidates,
      total: 100,
      page: 1,
      chunk_size: 10
    })
  });
});
```

### Testing Error Scenarios
```javascript
// Mock API error response
await page.route('**/api/interview/*/questions', route => {
  route.fulfill({
    status: 500,
    contentType: 'application/json',
    body: JSON.stringify({
      detail: 'Internal server error',
      status_code: 500
    })
  });
});
```

### Validating API Calls
```javascript
// Capture and validate API requests
const apiRequests = [];
page.on('request', request => {
  if (request.url().includes('/api/')) {
    apiRequests.push({
      url: request.url(),
      method: request.method(),
      postData: request.postData()
    });
  }
});

// After test actions, validate API calls
const createCandidateRequest = apiRequests.find(
  req => req.method === 'POST' && req.url.includes('/candidate/')
);
expect(createCandidateRequest).toBeDefined();
```

## 📝 Testing Best Practices

### API-UI Integration Testing
1. **Validate Request Data**: Ensure UI sends correct data to API
2. **Test Response Handling**: Verify UI correctly processes API responses
3. **Error State Testing**: Test UI behavior with API errors
4. **Loading State Testing**: Validate loading indicators during API calls

### Performance Considerations
1. **Response Time Testing**: Validate API response times
2. **Concurrent Request Testing**: Test multiple simultaneous requests
3. **Large Dataset Testing**: Test with large API responses
4. **Network Condition Testing**: Test with slow/unreliable networks

### Data Consistency Testing
1. **Cross-Page Consistency**: Ensure data consistency across pages
2. **State Persistence**: Validate data persistence after navigation
3. **Real-time Updates**: Test real-time data synchronization
4. **Cache Invalidation**: Test data refresh scenarios

This API integration documentation provides comprehensive understanding of the SmartHR application's backend integration, enabling thorough testing of both frontend functionality and underlying data operations.
