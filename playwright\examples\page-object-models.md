# SmartHR Page Object Model Examples

This document provides comprehensive page object model examples that align with the existing SmartHR test infrastructure, promoting maintainable and reusable test code.

## 📋 Page Object Model Architecture

### Base Page Class
```javascript
// base-page.js
export class BasePage {
  constructor(page) {
    this.page = page;
    this.baseUrl = 'http://localhost:5173';
  }

  async goto(path = '') {
    await this.page.goto(`${this.baseUrl}${path}`);
    await this.page.waitForLoadState('networkidle');
  }

  async waitForSpinnerToDisappear() {
    await this.page.waitForSelector('.ant-spin-spinning', { state: 'detached', timeout: 10000 });
  }

  async showSuccessNotification() {
    return this.page.locator('.ant-notification-success, .ant-message-success');
  }

  async showErrorNotification() {
    return this.page.locator('.ant-notification-error, .ant-message-error');
  }

  async clickButton(text) {
    await this.page.click(`button:has-text("${text}"), .ant-btn:has-text("${text}")`);
  }

  async fillInput(selector, value) {
    await this.page.fill(selector, value);
  }

  async selectOption(selector, value) {
    await this.page.click(selector);
    await this.page.click(`.ant-select-item-option:has-text("${value}")`);
  }
}
```

## 🏢 Job Management Page Objects

### Job Orders Page
```javascript
// job-orders-page.js
import { BasePage } from './base-page.js';

export class JobOrdersPage extends BasePage {
  constructor(page) {
    super(page);
    
    // Selectors
    this.selectors = {
      jobTable: '.ant-table',
      jobRows: '.ant-table-row',
      searchInput: 'input[placeholder*="Search"]',
      stageFilter: '.ant-select:has(.ant-select-selection-item:has-text("Stage"))',
      clientFilter: '.ant-select:has(.ant-select-selection-item:has-text("Client"))',
      locationFilter: '.ant-select:has(.ant-select-selection-item:has-text("Location"))',
      applyButton: '.ant-btn:has-text("Apply")',
      clearButton: '.ant-btn:has-text("Clear")',
      emptyState: '.ant-empty',
      pagination: '.ant-pagination',
      nextPageButton: '.ant-pagination-next',
      prevPageButton: '.ant-pagination-prev'
    };
  }

  async navigateToJobOrders() {
    await this.goto('/');
    await this.page.waitForSelector(`${this.selectors.jobTable}, ${this.selectors.emptyState}`, { timeout: 10000 });
  }

  async getJobCount() {
    const jobRows = this.page.locator(this.selectors.jobRows);
    return await jobRows.count();
  }

  async searchJobs(searchTerm) {
    await this.fillInput(this.selectors.searchInput, searchTerm);
    await this.page.press(this.selectors.searchInput, 'Enter');
    await this.waitForSpinnerToDisappear();
  }

  async filterByStage(stage) {
    await this.selectOption(this.selectors.stageFilter, stage);
  }

  async filterByClient(client) {
    await this.selectOption(this.selectors.clientFilter, client);
  }

  async filterByLocation(location) {
    await this.selectOption(this.selectors.locationFilter, location);
  }

  async applyFilters() {
    await this.clickButton('Apply');
    await this.waitForSpinnerToDisappear();
  }

  async clearFilters() {
    await this.clickButton('Clear');
    await this.waitForSpinnerToDisappear();
  }

  async clickFirstJob() {
    const firstJob = this.page.locator(this.selectors.jobRows).first();
    await firstJob.click();
    await this.page.waitForURL('**/job/**');
  }

  async clickJobByIndex(index) {
    const job = this.page.locator(this.selectors.jobRows).nth(index);
    await job.click();
    await this.page.waitForURL('**/job/**');
  }

  async goToNextPage() {
    const nextButton = this.page.locator(this.selectors.nextPageButton);
    if (await nextButton.isEnabled()) {
      await nextButton.click();
      await this.waitForSpinnerToDisappear();
      return true;
    }
    return false;
  }

  async goToPreviousPage() {
    const prevButton = this.page.locator(this.selectors.prevPageButton);
    if (await prevButton.isEnabled()) {
      await prevButton.click();
      await this.waitForSpinnerToDisappear();
      return true;
    }
    return false;
  }

  async isEmptyState() {
    return await this.page.locator(this.selectors.emptyState).isVisible();
  }
}
```

### Job Details Page
```javascript
// job-details-page.js
import { BasePage } from './base-page.js';

export class JobDetailsPage extends BasePage {
  constructor(page) {
    super(page);
    
    this.selectors = {
      tabsContainer: '.ant-tabs',
      descriptionTab: '.ant-tabs-tab:has-text("Description")',
      matchingTab: '.ant-tabs-tab:has-text("Matching")',
      manualTab: '.ant-tabs-tab:has-text("Manual")',
      interviewTab: '.ant-tabs-tab:has-text("Interview")',
      activeTab: '.ant-tabs-tab-active',
      backButton: '.ant-btn:has(.anticon-arrow-left)',
      jobTitle: '[data-testid="job-title"], .job-title',
      jobDescription: '.job-description'
    };
  }

  async navigateToJobDetails(jobId) {
    await this.goto(`/job/${jobId}`);
    await this.page.waitForSelector(this.selectors.tabsContainer, { timeout: 10000 });
  }

  async clickDescriptionTab() {
    await this.page.click(this.selectors.descriptionTab);
    await this.waitForSpinnerToDisappear();
  }

  async clickMatchingTab() {
    await this.page.click(this.selectors.matchingTab);
    await this.waitForSpinnerToDisappear();
  }

  async clickManualTab() {
    await this.page.click(this.selectors.manualTab);
    await this.waitForSpinnerToDisappear();
  }

  async clickInterviewTab() {
    await this.page.click(this.selectors.interviewTab);
    await this.waitForSpinnerToDisappear();
  }

  async getActiveTabName() {
    const activeTab = this.page.locator(this.selectors.activeTab);
    return await activeTab.textContent();
  }

  async goBack() {
    await this.page.click(this.selectors.backButton);
    await this.page.waitForURL('/');
  }

  async getJobTitle() {
    const titleElement = this.page.locator(this.selectors.jobTitle);
    if (await titleElement.isVisible()) {
      return await titleElement.textContent();
    }
    return null;
  }
}
```

## 🎤 Interview Generation Page Objects

### Interview Generation Component
```javascript
// interview-generation-page.js
import { BasePage } from './base-page.js';

export class InterviewGenerationPage extends BasePage {
  constructor(page) {
    super(page);
    
    this.selectors = {
      questionCountInput: '.ant-input-number input',
      skillCheckboxes: {
        technical: '.ant-checkbox:has-text("Technical")',
        softSkills: '.ant-checkbox:has-text("Soft Skills")',
        methodologies: '.ant-checkbox:has-text("Methodologies")',
        language: '.ant-checkbox:has-text("Language")'
      },
      generateButton: 'button:has-text("Generate"), .ant-btn:has-text("Generate")',
      regenerateButton: 'button:has-text("Regenerate")',
      questionsContainer: '[data-testid="questions-container"], .questions-list',
      questionItems: '.question-item, [data-testid="question"]',
      candidatesContainer: '.candidates-container, [data-testid="candidates-list"]',
      candidateCards: '.candidate-card, .ant-card:has([data-testid="candidate-card"])'
    };
  }

  async setQuestionCount(count) {
    const input = this.page.locator(this.selectors.questionCountInput);
    if (await input.isVisible()) {
      await input.clear();
      await input.fill(count.toString());
    }
  }

  async selectSkillCategory(category) {
    const checkbox = this.page.locator(this.selectors.skillCheckboxes[category]);
    if (await checkbox.isVisible() && !await checkbox.locator('input').isChecked()) {
      await checkbox.click();
    }
  }

  async deselectSkillCategory(category) {
    const checkbox = this.page.locator(this.selectors.skillCheckboxes[category]);
    if (await checkbox.isVisible() && await checkbox.locator('input').isChecked()) {
      await checkbox.click();
    }
  }

  async selectAllSkillCategories() {
    for (const category of Object.keys(this.selectors.skillCheckboxes)) {
      await this.selectSkillCategory(category);
    }
  }

  async generateQuestions() {
    const generateButton = this.page.locator(this.selectors.generateButton);
    if (await generateButton.isVisible()) {
      await generateButton.click();
      await this.waitForSpinnerToDisappear();
    }
  }

  async regenerateQuestions() {
    const regenerateButton = this.page.locator(this.selectors.regenerateButton);
    if (await regenerateButton.isVisible()) {
      await regenerateButton.click();
      await this.waitForSpinnerToDisappear();
    }
  }

  async getGeneratedQuestionCount() {
    const questions = this.page.locator(this.selectors.questionItems);
    return await questions.count();
  }

  async getQuestionText(index) {
    const question = this.page.locator(this.selectors.questionItems).nth(index);
    return await question.textContent();
  }

  async getCandidateCount() {
    const candidates = this.page.locator(this.selectors.candidateCards);
    return await candidates.count();
  }

  async clickCandidateByIndex(index) {
    const candidate = this.page.locator(this.selectors.candidateCards).nth(index);
    await candidate.click();
    await this.page.waitForSelector('.ant-drawer, .ant-modal', { timeout: 10000 });
  }

  async isQuestionsGenerated() {
    const questionsContainer = this.page.locator(this.selectors.questionsContainer);
    return await questionsContainer.isVisible();
  }
}
```

## 📝 Interview Feedback Page Objects

### Interview Feedback Component
```javascript
// interview-feedback-page.js
import { BasePage } from './base-page.js';

export class InterviewFeedbackPage extends BasePage {
  constructor(page) {
    super(page);
    
    this.selectors = {
      feedbackDrawer: '.ant-drawer, .ant-modal',
      hrTab: '.ant-tabs-tab:has-text("HR")',
      technicalTab: '.ant-tabs-tab:has-text("Technical")',
      closeButton: '.ant-drawer-close, .ant-modal-close',
      form: '.ant-form',
      recruiterInput: 'input[name*="recruiter"], input[placeholder*="Recruiter"]',
      interviewDatePicker: '.ant-picker:has-text("Interview Date")',
      feedbackDatePicker: '.ant-picker:has-text("Feedback Date")',
      scheduledByInput: 'input[name*="scheduled"], input[placeholder*="Scheduled"]',
      statusSelect: '.ant-select:has(.ant-select-selection-item:has-text("Status"))',
      recommendationSelect: '.ant-select:has(.ant-select-selection-item:has-text("Recommendation"))',
      commentsTextArea: 'textarea[name*="comments"], textarea[placeholder*="Comments"]',
      transcriptTextArea: 'textarea[name*="transcript"], textarea[placeholder*="Transcript"]',
      saveButton: 'button:has-text("Save"), .ant-btn-primary:has-text("Save")',
      todayButton: '.ant-picker-today-btn'
    };
  }

  async openFeedbackDrawer() {
    await this.page.waitForSelector(this.selectors.feedbackDrawer, { timeout: 10000 });
  }

  async closeFeedbackDrawer() {
    const closeButton = this.page.locator(this.selectors.closeButton);
    if (await closeButton.isVisible()) {
      await closeButton.click();
      await this.page.waitForSelector(this.selectors.feedbackDrawer, { state: 'detached' });
    }
  }

  async switchToHRTab() {
    await this.page.click(this.selectors.hrTab);
    await this.page.waitForSelector(this.selectors.form);
  }

  async switchToTechnicalTab() {
    await this.page.click(this.selectors.technicalTab);
    await this.page.waitForSelector(this.selectors.form);
  }

  async fillRecruiterName(name) {
    await this.fillInput(this.selectors.recruiterInput, name);
  }

  async setInterviewDate() {
    const datePicker = this.page.locator(this.selectors.interviewDatePicker);
    if (await datePicker.isVisible()) {
      await datePicker.click();
      const todayButton = this.page.locator(this.selectors.todayButton);
      if (await todayButton.isVisible()) {
        await todayButton.click();
      }
    }
  }

  async setFeedbackDate() {
    const datePicker = this.page.locator(this.selectors.feedbackDatePicker);
    if (await datePicker.isVisible()) {
      await datePicker.click();
      const todayButton = this.page.locator(this.selectors.todayButton);
      if (await todayButton.isVisible()) {
        await todayButton.click();
      }
    }
  }

  async fillScheduledBy(name) {
    await this.fillInput(this.selectors.scheduledByInput, name);
  }

  async setStatus(status) {
    await this.selectOption(this.selectors.statusSelect, status);
  }

  async setRecommendation(recommendation) {
    await this.selectOption(this.selectors.recommendationSelect, recommendation);
  }

  async fillComments(comments) {
    await this.fillInput(this.selectors.commentsTextArea, comments);
  }

  async fillTranscript(transcript) {
    await this.fillInput(this.selectors.transcriptTextArea, transcript);
  }

  async submitFeedback() {
    const saveButton = this.page.locator(this.selectors.saveButton);
    await saveButton.click();
    await this.page.waitForSelector('.ant-notification-success, .ant-notification-error', { timeout: 10000 });
  }

  async fillCompleteFeedback(feedbackData) {
    await this.fillRecruiterName(feedbackData.recruiter);
    await this.setInterviewDate();
    await this.setFeedbackDate();
    
    if (feedbackData.scheduledBy) {
      await this.fillScheduledBy(feedbackData.scheduledBy);
    }
    
    await this.setStatus(feedbackData.status);
    await this.setRecommendation(feedbackData.recommendation);
    await this.fillComments(feedbackData.comments);
    
    if (feedbackData.transcript) {
      await this.fillTranscript(feedbackData.transcript);
    }
  }

  async isSuccessNotificationVisible() {
    const notification = await this.showSuccessNotification();
    return await notification.isVisible();
  }

  async isErrorNotificationVisible() {
    const notification = await this.showErrorNotification();
    return await notification.isVisible();
  }
}
```

## 👥 Candidate Management Page Objects

### Candidates Page
```javascript
// candidates-page.js
import { BasePage } from './base-page.js';

export class CandidatesPage extends BasePage {
  constructor(page) {
    super(page);
    
    this.selectors = {
      candidateTable: '.ant-table',
      candidateRows: '.ant-table-row',
      searchInput: 'input[placeholder*="Search"]',
      roleFilter: '.ant-select:has(.ant-select-selection-item:has-text("Role"))',
      countryFilter: '.ant-select:has(.ant-select-selection-item:has-text("Country"))',
      statusFilter: '.ant-select:has(.ant-select-selection-item:has-text("Status"))',
      applyButton: '.ant-btn:has-text("Apply")',
      clearButton: '.ant-btn:has-text("Clear")',
      emptyState: '.ant-empty'
    };
  }

  async navigateToCandidates() {
    await this.goto('/candidates');
    await this.page.waitForSelector(`${this.selectors.candidateTable}, ${this.selectors.emptyState}`, { timeout: 10000 });
  }

  async getCandidateCount() {
    const candidateRows = this.page.locator(this.selectors.candidateRows);
    return await candidateRows.count();
  }

  async searchCandidates(searchTerm) {
    await this.fillInput(this.selectors.searchInput, searchTerm);
    await this.page.press(this.selectors.searchInput, 'Enter');
    await this.waitForSpinnerToDisappear();
  }

  async filterByRole(role) {
    await this.selectOption(this.selectors.roleFilter, role);
  }

  async filterByCountry(country) {
    await this.selectOption(this.selectors.countryFilter, country);
  }

  async filterByStatus(status) {
    await this.selectOption(this.selectors.statusFilter, status);
  }

  async applyFilters() {
    await this.clickButton('Apply');
    await this.waitForSpinnerToDisappear();
  }

  async clearFilters() {
    await this.clickButton('Clear');
    await this.waitForSpinnerToDisappear();
  }

  async clickFirstCandidate() {
    const firstCandidate = this.page.locator(this.selectors.candidateRows).first();
    await firstCandidate.click();
    await this.page.waitForURL('**/candidates/**');
  }

  async clickCandidateByIndex(index) {
    const candidate = this.page.locator(this.selectors.candidateRows).nth(index);
    await candidate.click();
    await this.page.waitForURL('**/candidates/**');
  }

  async isEmptyState() {
    return await this.page.locator(this.selectors.emptyState).isVisible();
  }
}
```

## 🧪 Usage Examples

### Complete Test Using Page Objects
```javascript
// example-test.spec.js
import { test, expect } from '@playwright/test';
import { JobOrdersPage } from './page-objects/job-orders-page.js';
import { JobDetailsPage } from './page-objects/job-details-page.js';
import { InterviewGenerationPage } from './page-objects/interview-generation-page.js';
import { InterviewFeedbackPage } from './page-objects/interview-feedback-page.js';

test('Complete interview workflow using page objects', async ({ page }) => {
  const jobOrdersPage = new JobOrdersPage(page);
  const jobDetailsPage = new JobDetailsPage(page);
  const interviewPage = new InterviewGenerationPage(page);
  const feedbackPage = new InterviewFeedbackPage(page);

  // Navigate to job orders and select a job
  await jobOrdersPage.navigateToJobOrders();
  const jobCount = await jobOrdersPage.getJobCount();
  
  if (jobCount > 0) {
    await jobOrdersPage.clickFirstJob();
    
    // Navigate to interview tab
    await jobDetailsPage.clickInterviewTab();
    
    // Generate interview questions
    await interviewPage.setQuestionCount(10);
    await interviewPage.selectSkillCategory('technical');
    await interviewPage.selectSkillCategory('softSkills');
    await interviewPage.generateQuestions();
    
    // Verify questions were generated
    const questionCount = await interviewPage.getGeneratedQuestionCount();
    expect(questionCount).toBeGreaterThan(0);
    
    // Submit feedback for first candidate
    const candidateCount = await interviewPage.getCandidateCount();
    if (candidateCount > 0) {
      await interviewPage.clickCandidateByIndex(0);
      await feedbackPage.openFeedbackDrawer();
      
      // Submit HR feedback
      await feedbackPage.switchToHRTab();
      await feedbackPage.fillCompleteFeedback({
        recruiter: 'John Smith',
        status: 'completed',
        recommendation: 'Yes',
        comments: 'Excellent candidate'
      });
      await feedbackPage.submitFeedback();
      
      expect(await feedbackPage.isSuccessNotificationVisible()).toBeTruthy();
    }
  }
});
```

This comprehensive page object model documentation provides maintainable and reusable test components that align with the SmartHR application structure.
