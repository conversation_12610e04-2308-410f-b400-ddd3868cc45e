# Interview Question Generation Testing Guide

This guide demonstrates how to use the comprehensive Playwright testing documentation and automated tests for the SmartHR interview question generation workflow.

## 📁 Documentation Structure

The `playwright/` folder contains comprehensive documentation for AI agents to run automated tests:

```
playwright/
├── README.md                           # Main overview and quick start
├── workflows/                          # Step-by-step workflow documentation
│   ├── job-application-workflow.md     # Job management workflows
│   ├── interview-generation-workflow.md # Interview question generation
│   ├── candidate-management-workflow.md # Candidate management
│   └── interview-feedback-workflow.md  # Interview feedback forms
├── ui-elements/                        # UI interaction patterns
│   ├── selectors-reference.md          # Comprehensive UI selectors
│   ├── navigation-patterns.md          # Navigation and routing
│   └── form-interactions.md            # Form handling patterns
├── test-scenarios/                     # Test case documentation
│   ├── advanced-scenarios.md           # Complex test scenarios
│   └── README.md                       # Test scenario structure
├── examples/                           # Code examples and templates
│   ├── basic-test-templates.md         # Ready-to-use test examples
│   ├── page-object-models.md           # Page object patterns
│   └── testing-best-practices.md       # Best practices guide
└── api-integration/                    # Backend integration
    ├── backend-endpoints.md            # API endpoint reference
    └── data-flow-patterns.md           # Data flow documentation
```

## 🧪 Automated Tests Created

### 1. Basic Interview Question Generation Test
**File:** `tests/e2e/interview-question-generation.spec.ts`

Features:
- Custom question count configuration
- Skill category selection (Technical, Soft Skills, Methodologies)
- Question generation process validation
- Content quality validation
- Error handling scenarios
- Network error simulation

### 2. Comprehensive Interview Workflow Test
**File:** `tests/e2e/comprehensive-interview-workflow.spec.ts`

Features:
- Complete end-to-end workflow automation
- Advanced page object model usage
- Performance monitoring
- Question regeneration testing
- Candidate interaction testing
- Boundary condition validation
- Recovery testing

### 3. Enhanced Page Object Model
**File:** `tests/pages/InterviewGenerationPage.ts`

Features:
- Comprehensive selector strategies
- Workflow automation methods
- Quality validation functions
- Error handling capabilities
- Performance monitoring
- Reusable configuration patterns

## 🚀 Running the Tests

### Prerequisites

1. **SmartHR Application Running**
   ```bash
   # Start the backend
   cd smarthr-be
   uvicorn main:app --reload

   # Start the frontend
   cd smarthr-fe
   npm run dev
   ```

2. **Playwright Installation**
   ```bash
   npm install @playwright/test
   npx playwright install
   ```

### Test Execution Options

#### Option 1: Using the Test Runner Script
```bash
# Run basic interview question generation tests
node run-interview-tests.js basic

# Run comprehensive workflow tests
node run-interview-tests.js comprehensive

# Run all interview-related tests
node run-interview-tests.js all

# Show help
node run-interview-tests.js help
```

#### Option 2: Direct Playwright Commands
```bash
# Run specific test file
npx playwright test tests/e2e/interview-question-generation.spec.ts

# Run comprehensive workflow test
npx playwright test tests/e2e/comprehensive-interview-workflow.spec.ts

# Run all tests with UI mode
npx playwright test --ui

# Run tests in headed mode (visible browser)
npx playwright test --headed

# Run tests with debug mode
npx playwright test --debug
```

#### Option 3: Individual Test Cases
```bash
# Run specific test case
npx playwright test -g "should generate interview questions with custom configuration"

# Run tests matching pattern
npx playwright test -g "interview.*generation"
```

## 📋 Test Scenarios Covered

### Basic Functionality
- ✅ Navigate to job details page
- ✅ Switch to interview tab
- ✅ Configure question count (1-20)
- ✅ Select skill categories
- ✅ Generate interview questions
- ✅ Validate question content quality
- ✅ Verify success notifications

### Advanced Features
- ✅ Question regeneration
- ✅ Different skill category combinations
- ✅ Boundary condition testing
- ✅ Performance monitoring
- ✅ Candidate interaction
- ✅ Category-based question filtering

### Error Handling
- ✅ Network error simulation
- ✅ Invalid input validation
- ✅ Recovery from failures
- ✅ Graceful error messaging
- ✅ Application stability

### Quality Validation
- ✅ Question uniqueness
- ✅ Content length validation
- ✅ Question format validation
- ✅ Category distribution
- ✅ Performance benchmarks

## 🎯 Key Features Demonstrated

### 1. Workflow Automation
The tests demonstrate complete automation of the interview question generation workflow:

```typescript
const questions = await interviewPage.executeCompleteQuestionGenerationWorkflow({
  questionCount: 10,
  skillCategories: {
    technical: true,
    softSkills: true,
    methodologies: true
  }
});
```

### 2. Comprehensive Validation
Each test includes multiple validation layers:

```typescript
// Validate question count
expect(questions.length).toBeGreaterThan(0);

// Validate question quality
const isQualityValid = await interviewPage.validateQuestionQuality(questions);
expect(isQualityValid).toBeTruthy();

// Validate uniqueness
const uniqueQuestions = new Set(questions.map(q => q.text));
expect(uniqueQuestions.size).toBe(questions.length);
```

### 3. Error Recovery
Tests demonstrate robust error handling:

```typescript
// Simulate network failure
await loggedInPage.route('**/api/interview/**/questions', route => route.abort());

// Attempt generation and handle error
await interviewPage.expectErrorNotification();

// Restore network and retry
await loggedInPage.unroute('**/api/interview/**/questions');
const recoveredQuestions = await interviewPage.executeCompleteQuestionGenerationWorkflow(config);
```

## 📊 Test Results and Reporting

### Console Output
The tests provide detailed console logging:
```
🚀 Starting comprehensive interview question generation workflow
📍 Step 1: Navigation and initial setup
✅ Successfully navigated to job details page
⚙️ Step 2: Executing complete question generation workflow
⏱️ Workflow completed in 3247ms
🔍 Step 3: Comprehensive validation of generated questions
✅ Generated 8 questions within expected range
✅ All questions are unique
📊 Category distribution: { Technical: 4, Soft Skills: 2, Methodologies: 2 }
```

### Performance Metrics
- Workflow execution time monitoring
- API response time tracking
- UI interaction performance
- Memory usage validation

## 🔧 Customization and Extension

### Adding New Test Scenarios
1. Create new test file in `tests/e2e/`
2. Import the `InterviewGenerationPage` class
3. Use the documented workflow patterns
4. Follow the validation patterns

### Extending Page Objects
1. Add new methods to `InterviewGenerationPage.ts`
2. Follow the selector patterns from `playwright/ui-elements/`
3. Implement comprehensive error handling
4. Add validation methods

### Custom Configurations
```typescript
const customConfig = {
  questionCount: 15,
  skillCategories: {
    technical: true,
    softSkills: false,
    methodologies: true,
    languageTools: true
  }
};
```

## 🐛 Troubleshooting

### Common Issues

1. **Application Not Running**
   - Ensure both frontend and backend are running
   - Check ports: frontend (5173), backend (8000)

2. **Database Issues**
   - Verify database is initialized
   - Check test data fixtures

3. **Playwright Issues**
   - Run `npx playwright install`
   - Check browser compatibility

4. **Network Timeouts**
   - Increase timeout values in test configuration
   - Check API response times

### Debug Mode
```bash
# Run with debug mode
npx playwright test --debug tests/e2e/interview-question-generation.spec.ts

# Run with trace
npx playwright test --trace on
```

## 📈 Success Metrics

The tests validate:
- ✅ 100% workflow automation
- ✅ Comprehensive error handling
- ✅ Performance within acceptable limits
- ✅ Quality validation of generated content
- ✅ UI interaction reliability
- ✅ Cross-browser compatibility

## 🎉 Conclusion

This comprehensive testing suite demonstrates how the playwright documentation can be used to create robust, automated tests for the SmartHR interview question generation workflow. The tests cover all major functionality, edge cases, and error scenarios while providing detailed validation and reporting.

The combination of detailed documentation and practical implementation serves as a complete reference for AI agents to understand and test the SmartHR application's interview management features.
