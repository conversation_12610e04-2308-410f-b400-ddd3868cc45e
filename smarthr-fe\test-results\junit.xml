<testsuites id="" name="" tests="1" failures="0" skipped="0" errors="0" time="16.195597">
<testsuite name="generate-new-questions.spec.ts" timestamp="2025-09-11T22:05:33.115Z" hostname="chromium" tests="1" failures="0" skipped="0" time="13.697" errors="0">
<testcase name="Generate New Questions Workflow › should generate new questions using the Generate New Questions button" classname="generate-new-questions.spec.ts" time="13.697">
<system-out>
<![CDATA[🚀 Starting Generate New Questions test on chromium
📍 Step 1: Navigating to SmartHR homepage
📋 Step 2: Navigating to jobs page
🎯 Step 3: Looking for Salesforce QA job position
✅ Found Salesforce QA position
📝 Selected position: Salesforce QA (ID: f59c7df9-4551-4d1c-b8b9-3f96a5ed70a9)
🎭 Step 4: Clicking "Generate AI Interview" button
✅ Found "Generate AI Interview" button
✅ Clicked "Generate AI Interview" button
🔄 Step 5: Looking for "Generate New Questions" button in modal
✅ Found "Generate New Questions" button in modal!
⚙️ Checking for configuration options
✅ Set question count to 10
Found 8 skill category options
🚀 Clicking "Generate New Questions" button
✅ Clicked "Generate New Questions" button
⏰ Step 6: Waiting for "Generate New Questions" button to stop loading...
🔄 "Generate AI Interview" button is loading - waiting for generation to complete...
✅ Button loading completed, interview generation should be finished
✅ Question generation completed in 3727ms
📝 Step 7: Extracting generated questions
✅ Found 1 questions with selector: .ant-card:has-text("?")
✅ Extracted 10 questions
📄 Step 8: Exporting results to markdown file and generated questions folder
✅ Questions and answers saved to: generated-questions\Salesforce-QA-questions-1757628347717.md
✅ Test results exported to: test-results\new-questions-chromium-1757628347718.md
🎉 Test completed successfully! Generated 1 questions for position: Salesforce QA
]]>
</system-out>
</testcase>
</testsuite>
</testsuites>