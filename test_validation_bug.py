#!/usr/bin/env python3
"""
Test script to reproduce the 100% scoring bug with incomplete responses.
"""

import sys
import os

# Add the smarthr-be directory to the path
sys.path.insert(0, os.path.join(os.path.dirname(__file__), 'smarthr-be'))

try:
    from controllers.interview_controller import _validate_evaluation_result
    from models.interview import EvaluationResult, QuestionEvaluation, Seniority
    
    print("✅ Successfully imported required modules")
    
    # Test case 1: Reproduce the user's issue
    print("\n🔍 Testing problematic responses from user example...")
    
    result = EvaluationResult(
        overall_seniority=Seniority.JUNIOR,
        per_question=[
            QuestionEvaluation(
                question_number=1,
                expected_seniority=Seniority.SENIOR,
                detected_seniority=Seniority.JUNIOR,
                explanation="<PERSON><PERSON><PERSON> responded 'I could¿ve know' - incomplete/garbled response"
            ),
            QuestionEvaluation(
                question_number=2,
                expected_seniority=Seniority.SENIOR,
                detected_seniority=Seniority.JUNIOR,
                explanation="Candidate said 'I don´t know' - honest admission but very brief"
            ),
            QuestionEvaluation(
                question_number=3,
                expected_seniority=Seniority.SENIOR,
                detected_seniority=Seniority.JUNIOR,
                explanation="Candi<PERSON> responded 'I don´t know' - no experience indicated"
            )
        ],
        percentage_of_match=100.0,  # This is the bug - should not be 100%
        explanation="Overall junior level due to limited experience in most areas"
    )
    
    # Validate the result
    issues = _validate_evaluation_result(result, 3, "test")
    
    print(f"📊 Validation issues found: {len(issues)}")
    for i, issue in enumerate(issues, 1):
        print(f"  {i}. {issue}")
    
    if len(issues) == 0:
        print("❌ BUG CONFIRMED: Validation did not catch the 100% scoring issue with incomplete responses")
    else:
        print("✅ Validation caught the issue - working as expected")
    
    # Test case 2: User's full example
    print("\n🔍 Testing full user example with 10 technical questions...")
    
    questions_and_responses = [
        ("Power Platform experience", "I don´t know"),
        ("Stay current with Power Platform", "I don´t know"),
        ("Azure experience", "I don´t know"),
        ("JavaScript experience", "I don´t know"),
        ("C# experience", "I don´t know"),
        ("SQL experience", "I don´t know"),
        ("Power BI experience", "I could¿ve know"),  # Garbled response
        ("Custom connectors experience", "I don´t know"),
        ("Power Pages experience", "I don´t know"),
        ("PL-400/PL-600 certification", "I don´t know")
    ]
    
    per_question_evals = []
    for i, (question, response) in enumerate(questions_and_responses, 1):
        per_question_evals.append(QuestionEvaluation(
            question_number=i,
            expected_seniority=Seniority.SENIOR,
            detected_seniority=Seniority.JUNIOR,
            explanation=f"Question about '{question}' - Candidate responded '{response}'"
        ))
    
    full_result = EvaluationResult(
        overall_seniority=Seniority.JUNIOR,
        per_question=per_question_evals,
        percentage_of_match=100.0,  # This is the bug the user is reporting
        explanation="Candidate showed limited knowledge across all technical areas"
    )
    
    full_issues = _validate_evaluation_result(full_result, len(questions_and_responses), "test")
    
    print(f"📊 Full example validation issues: {len(full_issues)}")
    for i, issue in enumerate(full_issues, 1):
        print(f"  {i}. {issue}")
    
    if len(full_issues) == 0:
        print("❌ BUG CONFIRMED: Validation did not catch the 100% scoring with mostly 'I don't know' responses")
    else:
        print("✅ Validation caught the issue in full example")
    
    # Test case 3: What should happen with honest admissions
    print("\n🔍 Testing honest admissions (should be valid)...")
    
    honest_result = EvaluationResult(
        overall_seniority=Seniority.JUNIOR,
        per_question=[
            QuestionEvaluation(
                question_number=1,
                expected_seniority=Seniority.SENIOR,
                detected_seniority=Seniority.JUNIOR,
                explanation="Candidate said 'I don't know' - honest admission of lack of knowledge"
            )
        ],
        percentage_of_match=100.0,  # Should be valid for honest admissions
        explanation="Junior level due to limited experience"
    )
    
    honest_issues = _validate_evaluation_result(honest_result, 1, "test")
    
    print(f"📊 Honest admission validation issues: {len(honest_issues)}")
    for i, issue in enumerate(honest_issues, 1):
        print(f"  {i}. {issue}")
    
    if len(honest_issues) == 0:
        print("✅ Honest admissions are correctly handled")
    else:
        print("❌ Issue: Honest admissions are being flagged incorrectly")

except ImportError as e:
    print(f"❌ Import error: {e}")
    print("Make sure you're running this from the correct directory")
except Exception as e:
    print(f"❌ Error: {e}")
    import traceback
    traceback.print_exc()
