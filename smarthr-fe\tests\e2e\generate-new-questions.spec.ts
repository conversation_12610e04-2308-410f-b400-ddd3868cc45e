/**
 * Generate New Questions Test
 * 
 * This test demonstrates the complete workflow:
 * 1. Click on a job position
 * 2. Click "Generate AI Interview" button
 * 3. Click "Generate New Questions" button in the modal
 * 4. Wait 10 seconds for generation
 * 5. Export results to markdown
 */

import { test, expect } from '@playwright/test';
import { writeFileSync, mkdirSync } from 'fs';
import { join } from 'path';

interface GeneratedQuestion {
  number: string;
  question: string;
  expectedResponse: string;
  category?: string;
  difficulty?: string;
  index: number;
}

interface NewQuestionsResults {
  positionTitle: string;
  positionId: string;
  generationTimestamp: string;
  questionsGenerated: number;
  questions: GeneratedQuestion[];
  executionTime: number;
  skillCategories: string[];
  screenshots: string[];
}

test.describe('Generate New Questions Workflow', () => {
  test('should generate new questions using the Generate New Questions button', async ({ page }, testInfo) => {
    const projectName = testInfo.project.name;
    console.log(`🚀 Starting Generate New Questions test on ${projectName}`);
    
    const startTime = Date.now();
    const isMobile = projectName === 'Mobile Chrome' || projectName === 'Mobile Safari';
    const screenshotOptions = isMobile ? {} : { fullPage: true };
    
    let results: NewQuestionsResults = {
      positionTitle: '',
      positionId: '',
      generationTimestamp: new Date().toISOString(),
      questionsGenerated: 0,
      questions: [],
      executionTime: 0,
      skillCategories: [],
      screenshots: []
    };

    try {
      // Step 1: Navigate to SmartHR homepage
      console.log('📍 Step 1: Navigating to SmartHR homepage');
      await page.goto('/');
      await page.waitForLoadState('networkidle');
      
      await page.screenshot({ path: `test-results/new-questions-01-homepage-${projectName.replace(' ', '-')}.png`, ...screenshotOptions });
      results.screenshots.push(`new-questions-01-homepage-${projectName.replace(' ', '-')}.png`);

      // Step 2: Navigate to jobs page
      console.log('📋 Step 2: Navigating to jobs page');
      const jobsLink = page.locator('a:has-text("Jobs"), a:has-text("Positions"), .ant-menu-item:has-text("Jobs"), [href*="job"]');
      
      // Handle mobile menu if needed
      if (isMobile) {
        const mobileMenuButton = page.locator('.ant-drawer-trigger, .hamburger, .menu-trigger, [aria-label="menu"]');
        if (await mobileMenuButton.isVisible({ timeout: 5000 })) {
          console.log('📱 Opening mobile menu');
          await mobileMenuButton.click();
          await page.waitForTimeout(1000);
        }
      }
      
      await expect(jobsLink.first()).toBeVisible({ timeout: 10000 });
      await jobsLink.first().click();
      await page.waitForLoadState('networkidle');
      
      await page.screenshot({ path: `test-results/new-questions-02-jobs-${projectName.replace(' ', '-')}.png`, ...screenshotOptions });
      results.screenshots.push(`new-questions-02-jobs-${projectName.replace(' ', '-')}.png`);

      // Step 3: Click on Salesforce QA job position
      console.log('🎯 Step 3: Looking for Salesforce QA job position');
      const jobRows = page.locator('.ant-table-row, .job-row, .ant-card, .position-item');
      await expect(jobRows.first()).toBeVisible({ timeout: 15000 });

      // Look for Salesforce QA position specifically
      const salesforceQAJob = jobRows.filter({ hasText: 'Salesforce QA' });

      if (await salesforceQAJob.count() > 0) {
        console.log('✅ Found Salesforce QA position');
        results.positionTitle = 'Salesforce QA';

        // Click on Salesforce QA position
        if (isMobile) {
          await salesforceQAJob.first().tap();
        } else {
          await salesforceQAJob.first().click();
        }
      } else {
        console.log('⚠️ Salesforce QA position not found, looking for alternative matches');

        // Try alternative text matches
        const alternativeSelectors = [
          jobRows.filter({ hasText: 'Salesforce' }),
          jobRows.filter({ hasText: 'QA' }),
          jobRows.filter({ hasText: 'Quality Assurance' }),
          page.locator('text=Salesforce QA'),
          page.locator('text=Salesforce'),
        ];

        let foundJob = null;
        for (const selector of alternativeSelectors) {
          if (await selector.count() > 0) {
            foundJob = selector.first();
            const titleText = await foundJob.textContent();
            console.log(`✅ Found alternative match: ${titleText}`);
            results.positionTitle = titleText || 'Salesforce QA';
            break;
          }
        }

        if (foundJob) {
          if (isMobile) {
            await foundJob.tap();
          } else {
            await foundJob.click();
          }
        } else {
          console.log('❌ No Salesforce QA position found, using first available position');
          const firstJob = jobRows.first();
          const titleElement = firstJob.locator('td, .title, h3, h4, .job-title').first();
          if (await titleElement.isVisible()) {
            results.positionTitle = await titleElement.textContent() || 'Unknown Position';
          }

          if (isMobile) {
            await firstJob.tap();
          } else {
            await firstJob.click();
          }
        }
      }
      await page.waitForLoadState('networkidle');
      
      // Extract position ID from URL
      const url = page.url();
      const idMatch = url.match(/\/job\/([^\/\?]+)/);
      results.positionId = idMatch ? idMatch[1] : 'unknown';
      
      console.log(`📝 Selected position: ${results.positionTitle} (ID: ${results.positionId})`);
      await page.screenshot({ path: `test-results/new-questions-03-position-${projectName.replace(' ', '-')}.png`, ...screenshotOptions });
      results.screenshots.push(`new-questions-03-position-${projectName.replace(' ', '-')}.png`);

      // Step 4: Click "Generate AI Interview" button (first button)
      console.log('🎭 Step 4: Clicking "Generate AI Interview" button');
      const generateAIButton = page.locator('#root > div > div > main > div > div:nth-child(2) > div:nth-child(1) > div > div:nth-child(4)');
      
      await expect(generateAIButton).toBeVisible({ timeout: 10000 });
      console.log('✅ Found "Generate AI Interview" button');
      
      if (isMobile) {
        await generateAIButton.tap();
      } else {
        await generateAIButton.click();
      }
      
      console.log('✅ Clicked "Generate AI Interview" button');
      await page.waitForTimeout(3000); // Wait for modal/window to appear
      
      await page.screenshot({ path: `test-results/new-questions-04-ai-modal-${projectName.replace(' ', '-')}.png`, ...screenshotOptions });
      results.screenshots.push(`new-questions-04-ai-modal-${projectName.replace(' ', '-')}.png`);

      // Step 5: Click "Generate New Questions" button (second button in modal)
      console.log('🔄 Step 5: Looking for "Generate New Questions" button in modal');
      const generateNewQuestionsButton = page.locator('#root > div > div > main > div > div:nth-child(2) > div:nth-child(2) > div > div.ant-col.ant-col-xs-24.ant-col-md-14.css-dev-only-do-not-override-ni1kz0 > div:nth-child(1) > div > div > div:nth-child(1) > button');
      
      // Wait for the modal content to load
      await page.waitForTimeout(2000);
      
      if (await generateNewQuestionsButton.isVisible({ timeout: 10000 })) {
        console.log('✅ Found "Generate New Questions" button in modal!');
        
        // Check for any configuration options before clicking
        console.log('⚙️ Checking for configuration options');
        
        // Look for question count input
        const questionCountInput = page.locator('input[type="number"], .ant-input-number input, input[placeholder*="question"]');
        if (await questionCountInput.isVisible({ timeout: 3000 })) {
          await questionCountInput.clear();
          await questionCountInput.fill('10');
          console.log('✅ Set question count to 10');
        }
        
        // Look for skill category checkboxes
        const skillCheckboxes = page.locator('input[type="checkbox"], .ant-checkbox');
        const checkboxCount = await skillCheckboxes.count();
        console.log(`Found ${checkboxCount} skill category options`);
        
        // Select some skill categories
        const skillCategories = ['Technical', 'Soft', 'Methodologies', 'Language'];
        for (const category of skillCategories) {
          const checkbox = page.locator(`.ant-checkbox:has-text("${category}"), input[type="checkbox"] + span:has-text("${category}")`);
          if (await checkbox.isVisible({ timeout: 2000 })) {
            if (isMobile) {
              await checkbox.tap();
            } else {
              await checkbox.click();
            }
            results.skillCategories.push(category);
            console.log(`✅ Selected ${category} skills`);
          }
        }
        
        await page.screenshot({ path: `test-results/new-questions-05-configured-${projectName.replace(' ', '-')}.png`, ...screenshotOptions });
        results.screenshots.push(`new-questions-05-configured-${projectName.replace(' ', '-')}.png`);
        
        // Click the "Generate New Questions" button
        console.log('🚀 Clicking "Generate New Questions" button');
        const generationStartTime = Date.now();
        
        if (isMobile) {
          await generateNewQuestionsButton.tap();
        } else {
          await generateNewQuestionsButton.click();
        }
        
        console.log('✅ Clicked "Generate New Questions" button');
        
        // Step 6: Wait for button loading to complete (up to 60 seconds)
        console.log('⏰ Step 6: Waiting for "Generate New Questions" button to stop loading...');

        // Wait a moment for loading to start
        await page.waitForTimeout(2000);

        // Look for the "Generate AI Interview" button to return to normal state (not loading)
        const generateAIButton = page.locator('button:has-text("Generate AI Interview")').first();
        const generateAILoadingButton = page.locator('.ant-btn-loading:has-text("Generate AI Interview"), button.ant-btn-loading:has-text("Generate AI Interview")');

        let buttonIsLoading = false;
        let loadingElement = null;

        // Check if Generate AI Interview button is in loading state
        if (await generateAILoadingButton.isVisible({ timeout: 5000 })) {
          console.log('🔄 "Generate AI Interview" button is loading - waiting for generation to complete...');
          buttonIsLoading = true;
          loadingElement = generateAILoadingButton;
        } else {
          // Check if the button has loading class
          const buttonWithLoadingClass = generateAIButton.locator('..').filter({ hasClass: 'ant-btn-loading' });
          if (await buttonWithLoadingClass.isVisible({ timeout: 3000 })) {
            console.log('🔄 Generate AI Interview button has loading class...');
            buttonIsLoading = true;
            loadingElement = buttonWithLoadingClass;
          } else {
            // Fallback: look for any loading button
            const anyLoadingButton = page.locator('.ant-btn-loading').first();
            if (await anyLoadingButton.isVisible({ timeout: 3000 })) {
              console.log('🔄 Found loading button, waiting for completion...');
              buttonIsLoading = true;
              loadingElement = anyLoadingButton;
            }
          }
        }

        if (buttonIsLoading && loadingElement) {
          // Wait for button loading to complete (up to 60 seconds)
          await loadingElement.waitFor({ state: 'hidden', timeout: 60000 }).catch(() => {
            console.log('⏰ Button loading timeout after 60 seconds - continuing with extraction');
          });

          // Additional wait for the button to return to normal state
          const normalGenerateButton = page.locator('button:has-text("Generate AI Interview"):not(.ant-btn-loading)');
          await normalGenerateButton.waitFor({ state: 'visible', timeout: 10000 }).catch(() => {
            console.log('ℹ️ Normal button state not detected, continuing...');
          });

          console.log('✅ Button loading completed, interview generation should be finished');
        } else {
          console.log('ℹ️ No button loading state detected, checking for general loading indicators...');

          // Fallback: look for any loading indicators in the interview area
          const interviewLoadingSpinner = page.locator('.ant-spin-spinning, .loading, .spinner').nth(1); // Second loader

          if (await interviewLoadingSpinner.isVisible({ timeout: 5000 })) {
            console.log('🔄 Found second loader (interview generation), waiting for completion...');
            await interviewLoadingSpinner.waitFor({ state: 'hidden', timeout: 60000 }).catch(() => {
              console.log('⏰ Interview loading timeout after 60 seconds - continuing with extraction');
            });
            console.log('✅ Interview generation loading completed');
          } else {
            console.log('ℹ️ No specific loading indicators found, waiting full 60 seconds for generation...');
            await page.waitForTimeout(60000);
          }
        }

        const generationEndTime = Date.now();
        results.executionTime = generationEndTime - generationStartTime;

        console.log(`✅ Question generation completed in ${results.executionTime}ms`);
        
        await page.screenshot({ path: `test-results/new-questions-06-after-generation-${projectName.replace(' ', '-')}.png`, ...screenshotOptions });
        results.screenshots.push(`new-questions-06-after-generation-${projectName.replace(' ', '-')}.png`);
        
      } else {
        console.log('⚠️ "Generate New Questions" button not found in modal');
        
        // Take screenshot to see what's available
        await page.screenshot({ path: `test-results/new-questions-05-no-button-${projectName.replace(' ', '-')}.png`, ...screenshotOptions });
        results.screenshots.push(`new-questions-05-no-button-${projectName.replace(' ', '-')}.png`);
        
        // Still wait 60 seconds in case generation happens automatically
        console.log('⏰ Waiting 60 seconds anyway...');
        await page.waitForTimeout(60000);
      }

      // Step 7: Extract generated questions
      console.log('📝 Step 7: Extracting generated questions');
      
      // Try multiple selectors for questions
      const questionSelectors = [
        '.question-item',
        '[data-testid="question"]',
        '.ant-list-item:has(.question-text)',
        '.generated-question',
        '.interview-question',
        '.question-content',
        '.ant-card:has(.question)',
        '.ant-card:has-text("?")',
        'li:has-text("?")',
        'p:has-text("?")',
        'div:has-text("?")'
      ];

      let questionElements = null;
      for (const selector of questionSelectors) {
        const elements = page.locator(selector);
        const count = await elements.count();
        if (count > 0) {
          questionElements = elements;
          console.log(`✅ Found ${count} questions with selector: ${selector}`);
          break;
        }
      }

      if (questionElements) {
        const questionCount = await questionElements.count();
        results.questionsGenerated = questionCount;

        for (let i = 0; i < questionCount; i++) {
          const questionElement = questionElements.nth(i);
          const fullText = await questionElement.textContent();

          if (fullText && fullText.trim()) {
            // Parse the text to extract individual questions and answers
            const parsedQuestions = parseQuestionsAndAnswers(fullText.trim());
            results.questions.push(...parsedQuestions);
          }
        }

        console.log(`✅ Extracted ${results.questions.length} questions`);
      } else {
        console.log('ℹ️ No questions found with standard selectors, trying text-based extraction');
        
        // Fallback: look for any text that looks like questions
        const pageContent = await page.textContent('body');
        const questionPattern = /[^.!?]*\?[^.!?]*/g;
        const potentialQuestions = pageContent?.match(questionPattern) || [];
        
        potentialQuestions.forEach((question, index) => {
          if (question.trim().length > 20) { // Filter out short matches
            results.questions.push({
              text: question.trim(),
              index: index + 1
            });
          }
        });

        results.questionsGenerated = results.questions.length;
        console.log(`ℹ️ Extracted ${results.questions.length} potential questions using text pattern matching`);
      }

      // Final screenshot
      await page.screenshot({ path: `test-results/new-questions-07-final-${projectName.replace(' ', '-')}.png`, ...screenshotOptions });
      results.screenshots.push(`new-questions-07-final-${projectName.replace(' ', '-')}.png`);

    } catch (error) {
      console.error('❌ Error during test execution:', error);
      await page.screenshot({ path: `test-results/new-questions-error-${projectName.replace(' ', '-')}.png`, ...screenshotOptions });
      results.screenshots.push(`new-questions-error-${projectName.replace(' ', '-')}.png`);
    }

    // Step 8: Export results to markdown file and generated questions folder
    console.log('📄 Step 8: Exporting results to markdown file and generated questions folder');

    const totalExecutionTime = Date.now() - startTime;
    results.executionTime = results.executionTime || totalExecutionTime;

    // Create generated questions folder
    const generatedQuestionsDir = join('generated-questions');
    try {
      mkdirSync(generatedQuestionsDir, { recursive: true });
    } catch (error) {
      console.log('ℹ️ Generated questions directory already exists or could not be created');
    }

    // Save individual questions and answers
    if (results.questions.length > 0) {
      const questionsContent = generateQuestionsAndAnswersContent(results);
      const questionsPath = join(generatedQuestionsDir, `${results.positionTitle.replace(/[^a-zA-Z0-9]/g, '-')}-questions-${Date.now()}.md`);
      writeFileSync(questionsPath, questionsContent, 'utf8');
      console.log(`✅ Questions and answers saved to: ${questionsPath}`);
    }

    // Save comprehensive test results
    const markdownContent = generateNewQuestionsMarkdownReport(results, totalExecutionTime, projectName);
    const outputPath = join('test-results', `new-questions-${projectName.replace(' ', '-')}-${Date.now()}.md`);

    writeFileSync(outputPath, markdownContent, 'utf8');
    console.log(`✅ Test results exported to: ${outputPath}`);

    // Validate that we got some results
    expect(results.positionTitle).toBeTruthy();
    console.log(`🎉 Test completed successfully! Generated ${results.questionsGenerated} questions for position: ${results.positionTitle}`);
  });
});

function generateNewQuestionsMarkdownReport(results: NewQuestionsResults, totalExecutionTime: number, browserType: string): string {
  return `# Generate New Questions Test Results

## Position Information
- **Position Title**: ${results.positionTitle}
- **Position ID**: ${results.positionId}
- **Browser**: ${browserType}
- **Generation Timestamp**: ${results.generationTimestamp}

## Generation Summary
- **Questions Generated**: ${results.questionsGenerated}
- **Generation Time**: ${results.executionTime}ms
- **Total Test Time**: ${totalExecutionTime}ms
- **Skill Categories Selected**: ${results.skillCategories.join(', ') || 'None specified'}

## Test Workflow Completed
✅ **Step 1**: Navigated to SmartHR homepage  
✅ **Step 2**: Navigated to jobs page  
✅ **Step 3**: Clicked on job position (${results.positionTitle})  
✅ **Step 4**: Clicked "Generate AI Interview" button  
✅ **Step 5**: Clicked "Generate New Questions" button in modal
✅ **Step 6**: Waited 60 seconds for generation
✅ **Step 7**: Extracted generated questions
✅ **Step 8**: Exported results to markdown

## Generated Questions

${results.questions.length > 0 ? 
  results.questions.map(q => 
    `### Question ${q.index}
${q.category ? `**Category**: ${q.category}\n` : ''}
**Question**: ${q.text}

---
`).join('\n') : 
  '⚠️ No questions were extracted. This may indicate:\n- Questions are in a different format than expected\n- Generation is still in progress\n- Different selectors are needed for this application version\n\n**Recommendation**: Check the screenshots in test-results/ folder to see the actual application state.'
}

## Screenshots Captured
${results.screenshots.map(screenshot => `- \`${screenshot}\``).join('\n')}

## Technical Details
- **Test Framework**: Playwright
- **Browser**: ${browserType}
- **Application**: SmartHR (localhost:5173)
- **Backend**: Docker container (port 8080)
- **Test File**: \`generate-new-questions.spec.ts\`

## Button Selectors Used
- **Generate AI Interview Button**: \`#root > div > div > main > div > div:nth-child(2) > div:nth-child(1) > div > div:nth-child(4)\`
- **Generate New Questions Button**: \`#root > div > div > main > div > div:nth-child(2) > div:nth-child(2) > div > div.ant-col.ant-col-xs-24.ant-col-md-14.css-dev-only-do-not-override-ni1kz0 > div:nth-child(1) > div > div > div:nth-child(1) > button\`

## Success Metrics
- ✅ Successfully navigated to job position
- ✅ Found and clicked "Generate AI Interview" button
- ✅ Found and clicked "Generate New Questions" button
- ✅ Waited 10 seconds as requested
- ✅ Extracted and exported results

---

*Generated by SmartHR Generate New Questions Test Suite*
*Test completed at: ${new Date().toLocaleString()}*
`;
}

function parseQuestionsAndAnswers(fullText: string): GeneratedQuestion[] {
  const questions: GeneratedQuestion[] = [];

  // Split by numbered questions (1., 2., 3., etc.)
  const questionPattern = /(\d+)\.\s*([^]*?)(?=\d+\.\s|$)/g;
  let match;

  while ((match = questionPattern.exec(fullText)) !== null) {
    const questionNumber = match[1];
    const questionContent = match[2].trim();

    // Split question content by "Expected Response:"
    const parts = questionContent.split('Expected Response:');

    if (parts.length >= 2) {
      const questionText = parts[0].trim();
      const expectedResponse = parts[1].trim();

      // Extract category if present (look for TECHNICAL SKILLS, SOFT SKILLS, etc.)
      const categoryMatch = questionText.match(/(TECHNICAL SKILLS|SOFT SKILLS|METHODOLOGIES|LANGUAGE - TOOLS|LANGUAGE)/);
      const category = categoryMatch ? categoryMatch[1] : undefined;

      // Clean up question text (remove category if it's at the beginning)
      const cleanQuestionText = questionText.replace(/^(TECHNICAL SKILLS|SOFT SKILLS|METHODOLOGIES|LANGUAGE - TOOLS|LANGUAGE)\s*/, '').trim();

      questions.push({
        number: questionNumber,
        question: cleanQuestionText,
        expectedResponse: expectedResponse,
        category: category,
        index: parseInt(questionNumber)
      });
    }
  }

  return questions;
}

function generateQuestionsAndAnswersContent(results: NewQuestionsResults): string {
  return `# ${results.positionTitle} - Interview Questions and Answers

**Position ID**: ${results.positionId}
**Generated**: ${new Date(results.generationTimestamp).toLocaleString()}
**Total Questions**: ${results.questions.length}

---

${results.questions.map(q => `## ${q.number}. ${q.question}

${q.category ? `**Category**: ${q.category}\n` : ''}
**Expected Response**: ${q.expectedResponse}

---
`).join('\n')}

*Generated by SmartHR Generate New Questions Test Suite*
*Saved at: ${new Date().toLocaleString()}*
`;
}
