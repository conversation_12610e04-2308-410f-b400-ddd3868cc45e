{"config": {"configFile": "C:\\Users\\<USER>\\OneDrive\\Desktop\\ArroyoConsulting\\smarthr\\smarthr-fe\\playwright.config.ts", "rootDir": "C:/Users/<USER>/OneDrive/Desktop/ArroyoConsulting/smarthr/smarthr-fe/tests/e2e", "forbidOnly": false, "fullyParallel": true, "globalSetup": "C:\\Users\\<USER>\\OneDrive\\Desktop\\ArroyoConsulting\\smarthr\\smarthr-fe\\tests\\global-setup.ts", "globalTeardown": "C:\\Users\\<USER>\\OneDrive\\Desktop\\ArroyoConsulting\\smarthr\\smarthr-fe\\tests\\global-teardown.ts", "globalTimeout": 0, "grep": {}, "grepInvert": null, "maxFailures": 0, "metadata": {"actualWorkers": 1}, "preserveOutput": "always", "reporter": [["html", {"outputFolder": "playwright-report", "open": "never"}], ["json", {"outputFile": "test-results/results.json"}], ["junit", {"outputFile": "test-results/junit.xml"}], ["list", null]], "reportSlowTests": {"max": 5, "threshold": 300000}, "quiet": false, "projects": [{"outputDir": "C:/Users/<USER>/OneDrive/Desktop/ArroyoConsulting/smarthr/smarthr-fe/test-results", "repeatEach": 1, "retries": 0, "metadata": {"actualWorkers": 1}, "id": "chromium", "name": "chromium", "testDir": "C:/Users/<USER>/OneDrive/Desktop/ArroyoConsulting/smarthr/smarthr-fe/tests/e2e", "testIgnore": [], "testMatch": ["**/*.@(spec|test).?(c|m)[jt]s?(x)"], "timeout": 60000}, {"outputDir": "C:/Users/<USER>/OneDrive/Desktop/ArroyoConsulting/smarthr/smarthr-fe/test-results", "repeatEach": 1, "retries": 0, "metadata": {"actualWorkers": 1}, "id": "firefox", "name": "firefox", "testDir": "C:/Users/<USER>/OneDrive/Desktop/ArroyoConsulting/smarthr/smarthr-fe/tests/e2e", "testIgnore": [], "testMatch": ["**/*.@(spec|test).?(c|m)[jt]s?(x)"], "timeout": 60000}, {"outputDir": "C:/Users/<USER>/OneDrive/Desktop/ArroyoConsulting/smarthr/smarthr-fe/test-results", "repeatEach": 1, "retries": 0, "metadata": {"actualWorkers": 1}, "id": "webkit", "name": "webkit", "testDir": "C:/Users/<USER>/OneDrive/Desktop/ArroyoConsulting/smarthr/smarthr-fe/tests/e2e", "testIgnore": [], "testMatch": ["**/*.@(spec|test).?(c|m)[jt]s?(x)"], "timeout": 60000}, {"outputDir": "C:/Users/<USER>/OneDrive/Desktop/ArroyoConsulting/smarthr/smarthr-fe/test-results", "repeatEach": 1, "retries": 0, "metadata": {"actualWorkers": 1}, "id": "Mobile Chrome", "name": "Mobile Chrome", "testDir": "C:/Users/<USER>/OneDrive/Desktop/ArroyoConsulting/smarthr/smarthr-fe/tests/e2e", "testIgnore": [], "testMatch": ["**/*.@(spec|test).?(c|m)[jt]s?(x)"], "timeout": 60000}, {"outputDir": "C:/Users/<USER>/OneDrive/Desktop/ArroyoConsulting/smarthr/smarthr-fe/test-results", "repeatEach": 1, "retries": 0, "metadata": {"actualWorkers": 1}, "id": "Mobile Safari", "name": "Mobile Safari", "testDir": "C:/Users/<USER>/OneDrive/Desktop/ArroyoConsulting/smarthr/smarthr-fe/tests/e2e", "testIgnore": [], "testMatch": ["**/*.@(spec|test).?(c|m)[jt]s?(x)"], "timeout": 60000}, {"outputDir": "C:/Users/<USER>/OneDrive/Desktop/ArroyoConsulting/smarthr/smarthr-fe/test-results", "repeatEach": 1, "retries": 0, "metadata": {"actualWorkers": 1}, "id": "Microsoft Edge", "name": "Microsoft Edge", "testDir": "C:/Users/<USER>/OneDrive/Desktop/ArroyoConsulting/smarthr/smarthr-fe/tests/e2e", "testIgnore": [], "testMatch": ["**/*.@(spec|test).?(c|m)[jt]s?(x)"], "timeout": 60000}, {"outputDir": "C:/Users/<USER>/OneDrive/Desktop/ArroyoConsulting/smarthr/smarthr-fe/test-results", "repeatEach": 1, "retries": 0, "metadata": {"actualWorkers": 1}, "id": "Google Chrome", "name": "Google Chrome", "testDir": "C:/Users/<USER>/OneDrive/Desktop/ArroyoConsulting/smarthr/smarthr-fe/tests/e2e", "testIgnore": [], "testMatch": ["**/*.@(spec|test).?(c|m)[jt]s?(x)"], "timeout": 60000}, {"outputDir": "C:/Users/<USER>/OneDrive/Desktop/ArroyoConsulting/smarthr/smarthr-fe/test-results", "repeatEach": 1, "retries": 0, "metadata": {"actualWorkers": 1}, "id": "visual-regression", "name": "visual-regression", "testDir": "C:/Users/<USER>/OneDrive/Desktop/ArroyoConsulting/smarthr/smarthr-fe/tests/e2e", "testIgnore": [], "testMatch": ["**/visual/**/*.spec.ts"], "timeout": 60000}], "shard": null, "updateSnapshots": "missing", "updateSourceMethod": "patch", "version": "1.55.0", "workers": 8, "webServer": null}, "suites": [{"title": "generate-new-questions.spec.ts", "file": "generate-new-questions.spec.ts", "column": 0, "line": 0, "specs": [], "suites": [{"title": "Generate New Questions Workflow", "file": "generate-new-questions.spec.ts", "line": 36, "column": 6, "specs": [{"title": "should generate new questions using the Generate New Questions button", "ok": true, "tags": [], "tests": [{"timeout": 60000, "annotations": [], "expectedStatus": "passed", "projectId": "chromium", "projectName": "chromium", "results": [{"workerIndex": 0, "parallelIndex": 0, "status": "passed", "duration": 13697, "errors": [], "stdout": [{"text": "🚀 Starting Generate New Questions test on chromium\n"}, {"text": "📍 Step 1: Navigating to SmartHR homepage\n"}, {"text": "📋 Step 2: Navigating to jobs page\n"}, {"text": "🎯 Step 3: Looking for Salesforce QA job position\n"}, {"text": "✅ Found Salesforce QA position\n"}, {"text": "📝 Selected position: Salesforce QA (ID: f59c7df9-4551-4d1c-b8b9-3f96a5ed70a9)\n"}, {"text": "🎭 Step 4: Clicking \"Generate AI Interview\" button\n"}, {"text": "✅ Found \"Generate AI Interview\" button\n"}, {"text": "✅ Clicked \"Generate AI Interview\" button\n"}, {"text": "🔄 Step 5: Looking for \"Generate New Questions\" button in modal\n"}, {"text": "✅ Found \"Generate New Questions\" button in modal!\n"}, {"text": "⚙️ Checking for configuration options\n"}, {"text": "✅ Set question count to 10\n"}, {"text": "Found 8 skill category options\n"}, {"text": "🚀 Clicking \"Generate New Questions\" button\n"}, {"text": "✅ Clicked \"Generate New Questions\" button\n"}, {"text": "⏰ Step 6: Waiting for \"Generate New Questions\" button to stop loading...\n"}, {"text": "🔄 \"Generate AI Interview\" button is loading - waiting for generation to complete...\n"}, {"text": "✅ Button loading completed, interview generation should be finished\n"}, {"text": "✅ Question generation completed in 3727ms\n"}, {"text": "📝 Step 7: Extracting generated questions\n"}, {"text": "✅ Found 1 questions with selector: .ant-card:has-text(\"?\")\n"}, {"text": "✅ Extracted 10 questions\n"}, {"text": "📄 Step 8: Exporting results to markdown file and generated questions folder\n"}, {"text": "✅ Questions and answers saved to: generated-questions\\Salesforce-QA-questions-1757628347717.md\n"}, {"text": "✅ Test results exported to: test-results\\new-questions-chromium-1757628347718.md\n"}, {"text": "🎉 Test completed successfully! Generated 1 questions for position: Salesforce QA\n"}], "stderr": [], "retry": 0, "startTime": "2025-09-11T22:05:33.947Z", "annotations": [], "attachments": []}], "status": "expected"}], "id": "96cdcf7b02a2407f2afa-8faf5a04af714eefac07", "file": "generate-new-questions.spec.ts", "line": 37, "column": 3}]}]}], "errors": [], "stats": {"startTime": "2025-09-11T22:05:31.982Z", "duration": 16195.597, "expected": 1, "skipped": 0, "unexpected": 0, "flaky": 0}}