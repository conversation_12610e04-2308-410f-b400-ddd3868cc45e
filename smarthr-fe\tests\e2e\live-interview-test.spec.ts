/**
 * Live Interview Question Generation Test
 * 
 * This test demonstrates the interview question generation workflow
 * using the live SmartHR application with Docker backend.
 * It uses existing data instead of creating new test data.
 */

import { test, expect } from '@playwright/test';
import { InterviewGenerationPage } from '../pages/InterviewGenerationPage';

test.describe('Live Interview Question Generation', () => {
  test('should demonstrate interview workflow with existing job positions', async ({ page }) => {
    console.log('🚀 Starting live interview question generation test');
    
    // Navigate to the SmartHR application
    await page.goto('/');
    
    // Wait for the application to load
    await page.waitForLoadState('networkidle');
    
    // Take a screenshot to see the current state
    await page.screenshot({ path: 'test-results/app-homepage.png', fullPage: true });
    
    console.log('📍 Step 1: Navigated to SmartHR homepage');
    
    // Look for job positions or navigation to jobs
    const jobsLink = page.locator('a:has-text("Jobs"), a:has-text("Positions"), .ant-menu-item:has-text("Jobs")');
    
    if (await jobsLink.isVisible()) {
      console.log('📋 Found jobs navigation link');
      await jobsLink.click();
      await page.waitForLoadState('networkidle');
      
      // Take screenshot of jobs page
      await page.screenshot({ path: 'test-results/jobs-page.png', fullPage: true });
      
      // Look for existing job positions
      const jobRows = page.locator('.ant-table-row, .job-row, .ant-card');
      const jobCount = await jobRows.count();
      
      console.log(`📊 Found ${jobCount} job positions`);
      
      if (jobCount > 0) {
        // Click on the first job position
        const firstJob = jobRows.first();
        await firstJob.click();
        await page.waitForLoadState('networkidle');
        
        console.log('🎯 Clicked on first job position');
        
        // Take screenshot of job details
        await page.screenshot({ path: 'test-results/job-details.png', fullPage: true });
        
        // Look for interview tab
        const interviewTab = page.locator('.ant-tabs-tab:has-text("Interview"), [data-testid="interview-tab"]');
        
        if (await interviewTab.isVisible()) {
          console.log('🎭 Found interview tab');
          await interviewTab.click();
          await page.waitForLoadState('networkidle');
          
          // Take screenshot of interview tab
          await page.screenshot({ path: 'test-results/interview-tab.png', fullPage: true });
          
          // Look for question generation elements
          const generateButton = page.locator('button:has-text("Generate"), .ant-btn:has-text("Generate")');
          const questionCountInput = page.locator('.ant-input-number input, input[type="number"]');
          const skillCheckboxes = page.locator('.ant-checkbox');
          
          console.log('🔍 Checking for interview generation elements:');
          console.log(`- Generate button visible: ${await generateButton.isVisible()}`);
          console.log(`- Question count input visible: ${await questionCountInput.isVisible()}`);
          console.log(`- Skill checkboxes count: ${await skillCheckboxes.count()}`);
          
          if (await generateButton.isVisible()) {
            console.log('⚙️ Configuring question generation');
            
            // Set question count if input is available
            if (await questionCountInput.isVisible()) {
              await questionCountInput.fill('5');
              console.log('✅ Set question count to 5');
            }
            
            // Select skill categories if available
            const skillCategories = ['Technical', 'Soft', 'Methodologies'];
            for (const category of skillCategories) {
              const checkbox = page.locator(`.ant-checkbox:has-text("${category}")`);
              if (await checkbox.isVisible()) {
                await checkbox.click();
                console.log(`✅ Selected ${category} skills`);
              }
            }
            
            console.log('🚀 Generating interview questions');
            
            // Click generate button
            await generateButton.click();
            
            // Wait for loading to start
            const loadingSpinner = page.locator('.ant-spin-spinning');
            if (await loadingSpinner.isVisible({ timeout: 5000 })) {
              console.log('⏳ Question generation in progress...');
              
              // Wait for loading to complete (up to 30 seconds for AI generation)
              await loadingSpinner.waitFor({ state: 'hidden', timeout: 30000 });
              console.log('✅ Question generation completed');
            }
            
            // Take screenshot after generation
            await page.screenshot({ path: 'test-results/after-generation.png', fullPage: true });
            
            // Look for generated questions
            const questionItems = page.locator('.question-item, [data-testid="question"], .ant-list-item:has(.question-text)');
            const generatedCount = await questionItems.count();
            
            console.log(`📝 Generated ${generatedCount} questions`);
            
            if (generatedCount > 0) {
              console.log('🎉 SUCCESS: Interview questions generated successfully!');
              
              // Validate question content
              for (let i = 0; i < Math.min(generatedCount, 3); i++) {
                const questionElement = questionItems.nth(i);
                const questionText = await questionElement.textContent();
                console.log(`Question ${i + 1}: ${questionText?.substring(0, 80)}...`);
                
                // Basic validation
                expect(questionText?.trim()).toBeTruthy();
                expect(questionText?.trim().length).toBeGreaterThan(10);
              }
              
              console.log('✅ Question quality validation passed');
            } else {
              console.log('ℹ️ No questions visible - may need different selectors or more time');
            }
            
            // Look for success notification
            const successNotification = page.locator('.ant-notification-success, .ant-message-success');
            if (await successNotification.isVisible({ timeout: 5000 })) {
              console.log('✅ Success notification displayed');
            }
            
          } else {
            console.log('ℹ️ Generate button not found - may need different navigation or permissions');
          }
          
        } else {
          console.log('ℹ️ Interview tab not found - checking available tabs');
          const allTabs = page.locator('.ant-tabs-tab');
          const tabCount = await allTabs.count();
          console.log(`Found ${tabCount} tabs:`);
          
          for (let i = 0; i < tabCount; i++) {
            const tabText = await allTabs.nth(i).textContent();
            console.log(`- Tab ${i + 1}: ${tabText}`);
          }
        }
        
      } else {
        console.log('ℹ️ No job positions found - may need to create test data first');
      }
      
    } else {
      console.log('ℹ️ Jobs navigation not found - exploring available navigation options');
      
      // Look for navigation menu items
      const menuItems = page.locator('.ant-menu-item, .nav-item, a[href*="job"]');
      const menuCount = await menuItems.count();
      
      console.log(`Found ${menuCount} navigation items:`);
      for (let i = 0; i < Math.min(menuCount, 10); i++) {
        const itemText = await menuItems.nth(i).textContent();
        console.log(`- Menu item ${i + 1}: ${itemText}`);
      }
    }
    
    console.log('🏁 Live interview test completed');
    
    // Final screenshot
    await page.screenshot({ path: 'test-results/final-state.png', fullPage: true });
  });

  test('should explore SmartHR application structure', async ({ page }) => {
    console.log('🔍 Exploring SmartHR application structure');
    
    await page.goto('/');
    await page.waitForLoadState('networkidle');
    
    // Get page title
    const title = await page.title();
    console.log(`📄 Page title: ${title}`);
    
    // Look for main navigation elements
    const navElements = await page.locator('nav, .ant-menu, .navigation, header').count();
    console.log(`🧭 Found ${navElements} navigation elements`);
    
    // Look for main content areas
    const contentAreas = await page.locator('main, .content, .ant-layout-content, .main-content').count();
    console.log(`📋 Found ${contentAreas} content areas`);
    
    // Look for buttons and interactive elements
    const buttons = await page.locator('button, .ant-btn').count();
    console.log(`🔘 Found ${buttons} buttons`);
    
    // Look for forms and inputs
    const inputs = await page.locator('input, textarea, select').count();
    console.log(`📝 Found ${inputs} form inputs`);
    
    // Look for tables and lists
    const tables = await page.locator('table, .ant-table, .ant-list').count();
    console.log(`📊 Found ${tables} tables/lists`);
    
    // Take a comprehensive screenshot
    await page.screenshot({ path: 'test-results/app-structure.png', fullPage: true });
    
    console.log('✅ Application structure exploration completed');
  });

  test('should test page object model instantiation', async ({ page }) => {
    console.log('🧪 Testing page object model instantiation');
    
    await page.goto('/');
    
    // Test our InterviewGenerationPage class
    const interviewPage = new InterviewGenerationPage(page, 'test-position-id');
    
    // Verify methods are available
    const methods = [
      'navigateToInterviewTab',
      'setQuestionCount',
      'selectSkillCategory',
      'generateQuestions',
      'getGeneratedQuestions',
      'validateQuestionQuality',
      'executeCompleteQuestionGenerationWorkflow'
    ];
    
    console.log('🔧 Verifying page object methods:');
    for (const method of methods) {
      const hasMethod = typeof (interviewPage as any)[method] === 'function';
      console.log(`- ${method}: ${hasMethod ? '✅' : '❌'}`);
      expect(hasMethod).toBeTruthy();
    }
    
    console.log('✅ Page object model validation completed');
  });
});
