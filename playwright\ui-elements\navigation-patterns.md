# SmartHR Navigation Patterns and Routing

This document provides comprehensive guidance on navigation patterns, routing, and state management in the SmartHR application for automated testing purposes.

## 📋 Application Routing Structure

### Main Routes
```javascript
// Primary application routes
const routes = {
  home: '/',                           // Job Orders listing
  jobDetails: '/job/:id',              // Job details with tabs
  candidates: '/candidates',           // Candidates listing
  candidateDetails: '/candidates/:id'  // Individual candidate profile
};

// Route patterns for testing
const routePatterns = {
  home: /^\/$/,
  jobDetails: /^\/job\/[^\/]+/,
  candidates: /^\/candidates$/,
  candidateDetails: /^\/candidates\/[^\/]+$/
};
```

### URL Structure Examples
```
Home Page:           http://localhost:5173/
Job Details:         http://localhost:5173/job/abc123-def456-ghi789
Candidates:          http://localhost:5173/candidates
Candidate Details:   http://localhost:5173/candidates/xyz789-abc123-def456
```

## 🔄 Navigation Patterns

### Primary Navigation Flow
```
Home (Job Orders) → Job Details → Interview Generation → Candidate Selection
                 ↓
              Candidates → Candidate Details → Interview Assignment
```

### Tab-based Navigation (Job Details)
```
Job Details Page:
├── Description Tab (default)
├── Matching Candidates Tab
├── Manual Matching Tab
└── Generate AI Interview Tab
    └── Candidate List (right panel)
```

## 🧪 Navigation Testing Patterns

### Basic Navigation Testing
```javascript
// Navigate to home page
await page.goto('/');
await page.waitForLoadState('networkidle');
await expect(page).toHaveURL('/');

// Navigate to job details
await page.click('.ant-table-row:first-child');
await page.waitForURL('**/job/**');
await expect(page).toHaveURL(/\/job\/[^\/]+/);

// Navigate to candidates
await page.goto('/candidates');
await page.waitForLoadState('networkidle');
await expect(page).toHaveURL('/candidates');
```

### Tab Navigation Testing
```javascript
// Navigate between job detail tabs
const tabs = [
  { name: 'Description', selector: '.ant-tabs-tab:has-text("Description")' },
  { name: 'Matching', selector: '.ant-tabs-tab:has-text("Matching")' },
  { name: 'Manual', selector: '.ant-tabs-tab:has-text("Manual")' },
  { name: 'Interview', selector: '.ant-tabs-tab:has-text("Interview")' }
];

for (const tab of tabs) {
  await page.click(tab.selector);
  await page.waitForSelector('.ant-spin-spinning', { state: 'detached' });
  await expect(page.locator('.ant-tabs-tab-active')).toContainText(tab.name);
}
```

### Back Navigation Testing
```javascript
// Test back button functionality
const backButton = page.locator('.ant-btn:has(.anticon-arrow-left)');
if (await backButton.isVisible()) {
  await backButton.click();
  await page.waitForLoadState('networkidle');
  // Verify navigation occurred
}

// Test browser back navigation
await page.goBack();
await page.waitForLoadState('networkidle');
```

## 🏗️ State Management Patterns

### Navigation Context State
```javascript
// The application uses NavigationContext for state management
// Key state properties:
const navigationState = {
  previousPath: string | null,
  candidatesTableState: {
    searchTerm: string | null,
    page: number,
    limit: number,
    status: string,
    country: string,
    role: string,
    createdFrom: string,
    createdTo: string
  },
  jobOrdersState: {
    searchTerm: string,
    page: number,
    limit: number,
    stage: string,
    clientName: string,
    location: string,
    createdFrom: string,
    createdTo: string
  }
};
```

### Testing State Persistence
```javascript
// Test that navigation state is preserved
// 1. Set filters on job orders page
await page.goto('/');
await page.fill('input[placeholder*="Search"]', 'Software Engineer');
await page.click('.ant-btn:has-text("Apply")');

// 2. Navigate to job details
await page.click('.ant-table-row:first-child');
await page.waitForURL('**/job/**');

// 3. Navigate back
await page.click('.ant-btn:has(.anticon-arrow-left)');
await page.waitForURL('/');

// 4. Verify search state is preserved
const searchValue = await page.inputValue('input[placeholder*="Search"]');
expect(searchValue).toBe('Software Engineer');
```

## 🎯 Route-Specific Testing Patterns

### Home Page (Job Orders) Navigation
```javascript
// Test job orders page navigation
await page.goto('/');
await page.waitForSelector('.ant-table', { timeout: 10000 });

// Test search functionality
await page.fill('input[placeholder*="Search"]', 'Developer');
await page.press('input[placeholder*="Search"]', 'Enter');
await page.waitForSelector('.ant-spin-spinning', { state: 'detached' });

// Test pagination navigation
const pagination = page.locator('.ant-pagination');
if (await pagination.isVisible()) {
  const nextButton = pagination.locator('.ant-pagination-next');
  if (await nextButton.isEnabled()) {
    await nextButton.click();
    await page.waitForSelector('.ant-spin-spinning', { state: 'detached' });
  }
}

// Test job row click navigation
const jobRows = page.locator('.ant-table-row');
if (await jobRows.count() > 0) {
  await jobRows.first().click();
  await page.waitForURL('**/job/**');
}
```

### Job Details Page Navigation
```javascript
// Test job details page with specific job ID
const jobId = 'test-job-id';
await page.goto(`/job/${jobId}`);
await page.waitForSelector('.ant-tabs', { timeout: 10000 });

// Test tab navigation with state management
const interviewTab = page.locator('.ant-tabs-tab:has-text("Interview")');
await interviewTab.click();
await page.waitForSelector('.ant-spin-spinning', { state: 'detached' });

// Verify URL doesn't change for tab navigation (SPA behavior)
await expect(page).toHaveURL(`/job/${jobId}`);

// Test deep linking to specific tab (if supported)
await page.goto(`/job/${jobId}#interview`);
await expect(page.locator('.ant-tabs-tab-active')).toContainText('Interview');
```

### Candidates Page Navigation
```javascript
// Test candidates page navigation
await page.goto('/candidates');
await page.waitForSelector('.ant-table', { timeout: 10000 });

// Test candidate search and filtering
await page.fill('input[placeholder*="Search"]', 'John');
await page.selectOption('.ant-select:has(.ant-select-selection-item:has-text("Role"))', 'Developer');
await page.click('.ant-btn:has-text("Apply")');
await page.waitForSelector('.ant-spin-spinning', { state: 'detached' });

// Test candidate details navigation
const candidateRows = page.locator('.ant-table-row');
if (await candidateRows.count() > 0) {
  await candidateRows.first().click();
  await page.waitForURL('**/candidates/**');
}
```

## 🔄 Dynamic Navigation Patterns

### Conditional Navigation
```javascript
// Navigation that depends on data availability
const hasJobs = await page.locator('.ant-table-row').count() > 0;
const hasEmptyState = await page.locator('.ant-empty').isVisible();

if (hasJobs) {
  // Test job selection navigation
  await page.click('.ant-table-row:first-child');
  await page.waitForURL('**/job/**');
} else if (hasEmptyState) {
  // Test empty state navigation (e.g., create new job button)
  const createButton = page.locator('button:has-text("Create")');
  if (await createButton.isVisible()) {
    await createButton.click();
  }
}
```

### Modal and Drawer Navigation
```javascript
// Test modal navigation patterns
await page.click('button:has-text("Create Interview")');
await page.waitForSelector('.ant-modal', { timeout: 5000 });

// Navigate within modal
const modalTabs = page.locator('.ant-modal .ant-tabs-tab');
if (await modalTabs.count() > 0) {
  await modalTabs.nth(1).click();
}

// Close modal navigation
await page.click('.ant-modal-close');
await page.waitForSelector('.ant-modal', { state: 'detached' });

// Test drawer navigation
await page.click('.candidate-card:first-child');
await page.waitForSelector('.ant-drawer', { timeout: 5000 });

// Navigate within drawer
const drawerTabs = page.locator('.ant-drawer .ant-tabs-tab');
if (await drawerTabs.count() > 0) {
  for (let i = 0; i < await drawerTabs.count(); i++) {
    await drawerTabs.nth(i).click();
    await page.waitForTimeout(500); // Allow tab content to load
  }
}
```

## 🚨 Navigation Error Handling

### 404 and Error Page Testing
```javascript
// Test navigation to non-existent job
await page.goto('/job/non-existent-id');
await page.waitForLoadState('networkidle');

// Check for error handling
const hasError = await page.locator('.ant-result-404, .not-found, .error').isVisible();
const redirected = !page.url().includes('non-existent-id');

expect(hasError || redirected).toBeTruthy();

// Test navigation to non-existent candidate
await page.goto('/candidates/non-existent-id');
await page.waitForLoadState('networkidle');

// Verify error handling or redirect
const candidateError = await page.locator('.ant-result-404, .error').isVisible();
const candidateRedirected = page.url() === '/candidates';

expect(candidateError || candidateRedirected).toBeTruthy();
```

### Network Error Navigation
```javascript
// Test navigation with network issues
await page.route('**/api/**', route => route.abort());

await page.goto('/');
await page.waitForLoadState('networkidle');

// Check for error state or retry mechanisms
const errorMessage = page.locator('.ant-notification-error, .error-message');
const retryButton = page.locator('button:has-text("Retry")');

const hasErrorHandling = await errorMessage.isVisible() || await retryButton.isVisible();
expect(hasErrorHandling).toBeTruthy();

// Restore network
await page.unroute('**/api/**');
```

## 📱 Responsive Navigation Testing

### Mobile Navigation Patterns
```javascript
// Test mobile navigation (if applicable)
await page.setViewportSize({ width: 375, height: 667 });

// Test mobile menu
const mobileMenu = page.locator('.ant-drawer, .mobile-menu');
const menuButton = page.locator('.ant-btn:has(.anticon-menu)');

if (await menuButton.isVisible()) {
  await menuButton.click();
  await expect(mobileMenu).toBeVisible();
  
  // Test mobile navigation items
  const mobileNavItems = mobileMenu.locator('.ant-menu-item');
  if (await mobileNavItems.count() > 0) {
    await mobileNavItems.first().click();
    await page.waitForLoadState('networkidle');
  }
}
```

## 🔍 Navigation Performance Testing

### Page Load Performance
```javascript
// Test navigation performance
const startTime = Date.now();

await page.goto('/');
await page.waitForLoadState('networkidle');

const loadTime = Date.now() - startTime;
expect(loadTime).toBeLessThan(3000); // 3 second budget

console.log(`Home page loaded in ${loadTime}ms`);

// Test subsequent navigation performance
const navStartTime = Date.now();

await page.click('.ant-table-row:first-child');
await page.waitForURL('**/job/**');
await page.waitForSelector('.ant-tabs', { timeout: 10000 });

const navTime = Date.now() - navStartTime;
expect(navTime).toBeLessThan(2000); // 2 second budget for navigation

console.log(`Job details navigation took ${navTime}ms`);
```

This comprehensive navigation patterns documentation provides detailed guidance for testing all aspects of navigation and routing in the SmartHR application.
