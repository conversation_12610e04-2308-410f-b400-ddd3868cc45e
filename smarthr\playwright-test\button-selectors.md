# SmartHR Button Selectors Reference

## Critical CSS Selectors for SmartHR Testing

This document contains the exact CSS selectors required for SmartHR automated testing. These selectors are essential for the Generate New Questions workflow.

## Primary Buttons

### 1. Generate AI Interview But<PERSON> (<PERSON> Button)
**Location**: Job position details page  
**Purpose**: Opens the AI Interview modal/window  
**Selector**:
```css
#root > div > div > main > div > div:nth-child(2) > div:nth-child(1) > div > div:nth-child(4)
```

**HTML Structure**:
```html
<button type="button" class="ant-btn css-dev-only-do-not-override-ni1kz0 ant-btn-primary ant-btn-color-primary ant-btn-variant-solid" style="background-color: rgb(103, 58, 183); width: 50%;">
  <span>Generate AI Interview</span>
</button>
```

**Usage Example**:
```typescript
const generateAIButton = page.locator('#root > div > div > main > div > div:nth-child(2) > div:nth-child(1) > div > div:nth-child(4)');
await generateAIButton.click();
```

### 2. Generate New Questions Button (Second Button)
**Location**: Inside AI Interview modal  
**Purpose**: Triggers new question generation  
**Selector**:
```css
#root > div > div > main > div > div:nth-child(2) > div:nth-child(2) > div > div.ant-col.ant-col-xs-24.ant-col-md-14.css-dev-only-do-not-override-ni1kz0 > div:nth-child(1) > div > div > div:nth-child(1) > button
```

**Usage Example**:
```typescript
const generateNewQuestionsButton = page.locator('#root > div > div > main > div > div:nth-child(2) > div:nth-child(2) > div > div.ant-col.ant-col-xs-24.ant-col-md-14.css-dev-only-do-not-override-ni1kz0 > div:nth-child(1) > div > div > div:nth-child(1) > button');
await generateNewQuestionsButton.click();
```

## Loading State Selectors

### Generate AI Interview Button Loading States
**Normal State**:
```css
button:has-text("Generate AI Interview"):not(.ant-btn-loading)
```

**Loading State**:
```css
.ant-btn-loading:has-text("Generate AI Interview")
button:has-text("loading Generate AI Interview")
```

**Usage for Loading Detection**:
```typescript
// Wait for button to start loading
const loadingButton = page.locator('.ant-btn-loading:has-text("Generate AI Interview")');
if (await loadingButton.isVisible({ timeout: 5000 })) {
  // Wait for loading to complete
  await loadingButton.waitFor({ state: 'hidden', timeout: 60000 });
}
```

## Navigation Selectors

### Jobs/Positions Navigation
**Jobs Link Options**:
```css
a:has-text("Jobs")
a:has-text("Positions")
.ant-menu-item:has-text("Jobs")
[href*="job"]
```

**Usage**:
```typescript
const jobsLink = page.locator('a:has-text("Jobs"), a:has-text("Positions"), .ant-menu-item:has-text("Jobs"), [href*="job"]');
await jobsLink.first().click();
```

### Job Position Rows
**Position Row Selectors**:
```css
.ant-table-row
.job-row
.ant-card
.position-item
```

**Position Selection by Text**:
```typescript
const jobRows = page.locator('.ant-table-row, .job-row, .ant-card, .position-item');
const specificJob = jobRows.filter({ hasText: 'Salesforce QA' });
```

## Modal and Form Selectors

### Question Count Input
**Input Field Selectors**:
```css
input[type="number"]
.ant-input-number input
input[placeholder*="question"]
```

**Usage**:
```typescript
const questionCountInput = page.locator('input[type="number"], .ant-input-number input');
await questionCountInput.fill('10');
```

### Skill Category Checkboxes
**Checkbox Selectors**:
```css
input[type="checkbox"]
.ant-checkbox
```

**Specific Category Selection**:
```typescript
const technicalCheckbox = page.locator('.ant-checkbox:has-text("Technical"), input[type="checkbox"] + span:has-text("Technical")');
await technicalCheckbox.click();
```

## Question Content Selectors

### Question Elements
**Primary Question Selectors** (in order of preference):
```css
.question-item
[data-testid="question"]
.ant-list-item:has(.question-text)
.generated-question
.interview-question
.question-content
.ant-card:has(.question)
.ant-card:has-text("?")
li:has-text("?")
p:has-text("?")
div:has-text("?")
```

**Usage for Question Extraction**:
```typescript
const questionSelectors = [
  '.question-item',
  '[data-testid="question"]',
  '.ant-card:has-text("?")'
];

for (const selector of questionSelectors) {
  const elements = page.locator(selector);
  const count = await elements.count();
  if (count > 0) {
    // Found questions with this selector
    break;
  }
}
```

### Category Tags
**Category Tag Selectors**:
```css
.skill-tag
.question-category
.ant-tag
.category
```

**Usage**:
```typescript
const categoryElement = questionElement.locator('.skill-tag, .question-category, .ant-tag, .category').first();
const category = await categoryElement.textContent();
```

## Mobile-Specific Selectors

### Mobile Menu
**Mobile Menu Trigger**:
```css
.ant-drawer-trigger
.hamburger
.menu-trigger
[aria-label="menu"]
```

**Usage**:
```typescript
if (isMobile) {
  const mobileMenuButton = page.locator('.ant-drawer-trigger, .hamburger, .menu-trigger');
  if (await mobileMenuButton.isVisible({ timeout: 5000 })) {
    await mobileMenuButton.click();
  }
}
```

## Loading and Spinner Selectors

### General Loading Indicators
**Loading Spinner Selectors**:
```css
.ant-spin-spinning
.loading
.spinner
[aria-busy="true"]
```

**Specific Loading States**:
```css
.ant-btn-loading                    /* Loading button */
.ant-spin-lg                        /* Large spinner */
.ant-spin.ant-spin-spinning         /* Active spinner */
```

**Usage for Loading Detection**:
```typescript
// Wait for any loading to complete
const loadingSpinner = page.locator('.ant-spin-spinning, .loading, .spinner').first();
await loadingSpinner.waitFor({ state: 'hidden', timeout: 30000 });
```

## Selector Best Practices

### 1. Selector Priority
1. **ID selectors** - Most specific and reliable
2. **Data attributes** - Designed for testing
3. **Class combinations** - Stable across updates
4. **Text-based selectors** - Fallback option

### 2. Robust Selector Strategies
```typescript
// Multiple selector approach
const button = page.locator([
  '#specific-id',                    // Primary
  '[data-testid="button"]',         // Secondary
  '.ant-btn:has-text("Button")'     // Fallback
].join(', '));
```

### 3. Dynamic Content Handling
```typescript
// Wait for element to be stable
await page.locator(selector).waitFor({ state: 'visible', timeout: 10000 });

// Ensure element is actionable
await expect(page.locator(selector)).toBeEnabled();
```

## Troubleshooting Selectors

### Common Issues
1. **Element not found**: Check if page has loaded completely
2. **Multiple elements**: Use `.first()` or more specific selector
3. **Element not clickable**: Wait for loading states to complete
4. **Stale selectors**: Re-query elements after page changes

### Debug Commands
```typescript
// Check if selector matches elements
const count = await page.locator(selector).count();
console.log(`Found ${count} elements with selector: ${selector}`);

// Get element information
const element = page.locator(selector);
console.log('Visible:', await element.isVisible());
console.log('Enabled:', await element.isEnabled());
console.log('Text:', await element.textContent());
```

### Selector Validation
```typescript
// Validate selector before use
async function validateSelector(page, selector) {
  const elements = page.locator(selector);
  const count = await elements.count();
  
  if (count === 0) {
    throw new Error(`No elements found with selector: ${selector}`);
  }
  
  if (count > 1) {
    console.warn(`Multiple elements (${count}) found with selector: ${selector}`);
  }
  
  return elements.first();
}
```

---

**Important Notes:**
- These selectors are specific to the current SmartHR UI implementation
- Always test selectors after UI updates
- Use multiple fallback selectors for robustness
- Consider using data-testid attributes for more stable testing

*Last updated: November 2025*
