# Interview Question Generation Workflow

This document provides comprehensive step-by-step instructions for testing the AI-powered interview question generation functionality in the SmartHR application.

## 📋 Overview

### Purpose
Test the complete interview question generation workflow including configuration, generation, and management of AI-powered interview questions.

### Scope
- Navigate to interview generation interface
- Configure question parameters and skill categories
- Generate customized interview questions
- Review and manage generated questions
- Handle generation errors and edge cases

### Prerequisites
- Application running on `http://localhost:5173`
- Backend API available on `http://localhost:8080`
- Valid job position with ID available
- AI question generation service operational
- Authentication bypassed (local development mode)

### Expected Outcomes
- Successfully access interview generation interface
- Configure question generation parameters
- Generate AI-powered interview questions
- View and validate generated questions

## 🔄 Step-by-Step Workflow

### Phase 1: Navigate to Interview Generation

#### Step 1.1: Access Job Details Page
```javascript
// Navigate to specific job details (replace with actual job ID)
const jobId = 'your-job-id-here';
await page.goto(`http://localhost:5173/job/${jobId}`);
await page.waitForLoadState('networkidle');

// Wait for job details to load
await page.waitForSelector('.ant-tabs', { timeout: 10000 });
```

**UI Elements:**
- **URL Pattern**: `/job/{jobId}`
- **Tab Container**: `.ant-tabs`
- **Loading Indicator**: `.ant-spin-spinning`

**Validation Points:**
- Job details page loads successfully
- Tab navigation is available
- Job ID is valid and accessible

#### Step 1.2: Navigate to Interview Generation Tab
```javascript
// Click on the Generate AI Interview tab
const interviewTab = page.locator('.ant-tabs-tab:has-text("Interview"), .ant-tabs-tab:has-text("Generate")');
await expect(interviewTab).toBeVisible();
await interviewTab.click();

// Wait for tab content to load
await page.waitForSelector('.ant-spin-spinning', { state: 'detached' });
await page.waitForTimeout(1000); // Allow for content rendering
```

**UI Elements:**
- **Interview Tab**: `.ant-tabs-tab:has-text("Interview")`
- **Tab Content**: `.ant-tabs-tabpane-active`

**Validation Points:**
- Interview tab is clickable and accessible
- Tab content loads without errors
- Interview generation interface is displayed

### Phase 2: Configure Question Parameters

#### Step 2.1: Set Number of Questions
```javascript
// Locate question count input
const questionCountInput = page.locator('input[type="number"], .ant-input-number input');
await expect(questionCountInput).toBeVisible();

// Set desired number of questions (1-20 range)
await questionCountInput.clear();
await questionCountInput.fill('10');

// Verify input value
const inputValue = await questionCountInput.inputValue();
expect(inputValue).toBe('10');
```

**UI Elements:**
- **Question Count Input**: `input[type="number"], .ant-input-number input`
- **Input Label**: Text indicating "Number of Questions" or similar
- **Validation Message**: Error message for invalid ranges

**Validation Points:**
- Input accepts numeric values
- Range validation (1-20 questions)
- Input value is properly set and retained

#### Step 2.2: Select Skill Categories
```javascript
// Technical Skills checkbox
const technicalSkillsCheckbox = page.locator('.ant-checkbox:has-text("Technical"), input[type="checkbox"] + span:has-text("Technical")');
if (await technicalSkillsCheckbox.isVisible()) {
  await technicalSkillsCheckbox.check();
  expect(await technicalSkillsCheckbox.isChecked()).toBeTruthy();
}

// Soft Skills checkbox
const softSkillsCheckbox = page.locator('.ant-checkbox:has-text("Soft"), input[type="checkbox"] + span:has-text("Soft")');
if (await softSkillsCheckbox.isVisible()) {
  await softSkillsCheckbox.check();
}

// Methodologies checkbox
const methodologiesCheckbox = page.locator('.ant-checkbox:has-text("Methodologies"), input[type="checkbox"] + span:has-text("Methodologies")');
if (await methodologiesCheckbox.isVisible()) {
  await methodologiesCheckbox.check();
}

// Language-Tools checkbox
const languageToolsCheckbox = page.locator('.ant-checkbox:has-text("Language"), input[type="checkbox"] + span:has-text("Language")');
if (await languageToolsCheckbox.isVisible()) {
  await languageToolsCheckbox.check();
}
```

**UI Elements:**
- **Technical Skills**: `.ant-checkbox:has-text("Technical")`
- **Soft Skills**: `.ant-checkbox:has-text("Soft")`
- **Methodologies**: `.ant-checkbox:has-text("Methodologies")`
- **Language-Tools**: `.ant-checkbox:has-text("Language")`

**Validation Points:**
- All skill category checkboxes are available
- Checkboxes can be selected and deselected
- At least one category must be selected
- Selection state is properly maintained

#### Step 2.3: Verify Configuration Summary
```javascript
// Check if configuration summary is displayed
const configSummary = page.locator('.config-summary, .generation-config');
if (await configSummary.isVisible()) {
  const summaryText = await configSummary.textContent();
  console.log('Configuration Summary:', summaryText);
}

// Verify generate button is enabled
const generateButton = page.locator('.ant-btn:has-text("Generate"), button:has-text("Generate")');
await expect(generateButton).toBeVisible();
await expect(generateButton).toBeEnabled();
```

**Validation Points:**
- Configuration is properly summarized
- Generate button is available and enabled
- All required parameters are set

### Phase 3: Generate Interview Questions

#### Step 3.1: Initiate Question Generation
```javascript
// Click generate button
const generateButton = page.locator('.ant-btn:has-text("Generate"), button:has-text("Generate")');
await generateButton.click();

// Wait for loading state to appear
await page.waitForSelector('.ant-spin-spinning', { timeout: 5000 });
console.log('Question generation started...');
```

**UI Elements:**
- **Generate Button**: `.ant-btn:has-text("Generate")`
- **Loading Spinner**: `.ant-spin-spinning`
- **Progress Indicator**: `.ant-progress` (if available)

**Validation Points:**
- Generate button triggers the generation process
- Loading state is displayed immediately
- UI provides feedback that generation is in progress

#### Step 3.2: Wait for Generation Completion
```javascript
// Wait for generation to complete (may take 30-60 seconds)
await page.waitForSelector('.ant-spin-spinning', { 
  state: 'detached', 
  timeout: 120000 // 2 minutes timeout for AI generation
});

// Wait for success notification
const successNotification = page.locator('.ant-notification-success, .ant-message-success');
await expect(successNotification).toBeVisible({ timeout: 10000 });

console.log('Question generation completed successfully');
```

**UI Elements:**
- **Success Notification**: `.ant-notification-success`
- **Error Notification**: `.ant-notification-error`
- **Generation Status**: Status messages or indicators

**Validation Points:**
- Generation completes within reasonable time
- Success notification is displayed
- No error notifications appear
- Loading state is properly cleared

#### Step 3.3: Handle Generation Errors (if any)
```javascript
// Check for error notifications
const errorNotification = page.locator('.ant-notification-error, .ant-message-error');
if (await errorNotification.isVisible()) {
  const errorMessage = await errorNotification.textContent();
  console.error('Generation Error:', errorMessage);

  // Handle specific error types
  if (errorMessage.includes('maximum')) {
    console.log('Question limit exceeded - reduce question count');
  } else if (errorMessage.includes('network')) {
    console.log('Network error - retry generation');
  }
}
```

**Error Handling:**
- Network connectivity issues
- AI service unavailability
- Invalid parameter combinations
- Question limit exceeded (>20 questions)

### Phase 4: View Selected Candidates

#### Step 4.1: Access Candidate List
```javascript
// Look for candidate list in the interview tab
const candidateList = page.locator('.candidates-container, [data-testid="candidates-list"]');
if (await candidateList.isVisible()) {
  console.log('Candidate list is available');

  // Count selected candidates
  const candidateCards = page.locator('.candidate-card, .ant-card:has([data-testid="candidate-card"])');
  const candidateCount = await candidateCards.count();
  console.log(`Found ${candidateCount} selected candidates`);
}
```

**UI Elements:**
- **Candidate Container**: `.candidates-container, [data-testid="candidates-list"]`
- **Candidate Cards**: `.candidate-card, .ant-card`
- **Candidate Count Badge**: `.count-badge, .ant-badge`

**Validation Points:**
- Candidate list is accessible from interview tab
- Selected candidates are displayed
- Candidate count is accurate

### Phase 5: Review Generated Questions

#### Step 5.1: Verify Questions Display
```javascript
// Wait for questions to be displayed
const questionsContainer = page.locator('[data-testid="questions-container"], .questions-list');
await expect(questionsContainer).toBeVisible({ timeout: 10000 });

// Count generated questions
const questionItems = page.locator('.question-item, [data-testid="question"], .ant-list-item');
const questionCount = await questionItems.count();

console.log(`Generated ${questionCount} questions`);
expect(questionCount).toBeGreaterThan(0);
```

**UI Elements:**
- **Questions Container**: `[data-testid="questions-container"]`
- **Question Items**: `.question-item, [data-testid="question"]`
- **Question List**: `.ant-list, .questions-list`

**Validation Points:**
- Questions are displayed in a structured format
- Question count matches requested number
- Each question has proper formatting

#### Step 5.2: Validate Question Content
```javascript
// Examine first few questions for content quality
const maxQuestionsToCheck = Math.min(questionCount, 5);

for (let i = 0; i < maxQuestionsToCheck; i++) {
  const question = questionItems.nth(i);
  const questionText = await question.textContent();
  
  // Validate question content
  expect(questionText.trim()).toBeTruthy();
  expect(questionText.length).toBeGreaterThan(10);
  
  // Check for question indicators
  const hasQuestionMarkers = questionText.includes('?') || 
                           questionText.toLowerCase().includes('what') ||
                           questionText.toLowerCase().includes('how') ||
                           questionText.toLowerCase().includes('describe') ||
                           questionText.toLowerCase().includes('explain');
  
  expect(hasQuestionMarkers).toBeTruthy();
  
  console.log(`Question ${i + 1}: ${questionText.substring(0, 100)}...`);
}
```

**Validation Points:**
- Questions contain meaningful content
- Questions are properly formatted
- Questions include appropriate question words or punctuation
- Questions are relevant to selected skill categories

#### Step 5.3: Check Question Categories
```javascript
// Verify questions are categorized by skill type
const categoryLabels = page.locator('.question-category, .skill-tag, .ant-tag');
if (await categoryLabels.count() > 0) {
  const categories = [];
  const categoryCount = await categoryLabels.count();
  
  for (let i = 0; i < categoryCount; i++) {
    const categoryText = await categoryLabels.nth(i).textContent();
    categories.push(categoryText);
  }
  
  console.log('Question Categories:', [...new Set(categories)]);
  
  // Verify categories match selected skill types
  const expectedCategories = ['Technical Skills', 'Soft Skills', 'Methodologies', 'Language - Tools'];
  const hasExpectedCategories = categories.some(cat => 
    expectedCategories.some(expected => cat.includes(expected))
  );
  
  expect(hasExpectedCategories).toBeTruthy();
}
```

**UI Elements:**
- **Category Tags**: `.question-category, .skill-tag, .ant-tag`
- **Category Labels**: Text indicating skill category

### Phase 6: Question Management (Optional)

#### Step 6.1: Edit Questions (if available)
```javascript
// Look for edit functionality
const editButtons = page.locator('button:has-text("Edit"), .ant-btn:has(.anticon-edit)');
if (await editButtons.count() > 0) {
  const firstEditButton = editButtons.first();
  await firstEditButton.click();
  
  // Handle edit modal/form
  const editModal = page.locator('.ant-modal, .ant-drawer');
  if (await editModal.isVisible()) {
    const textArea = editModal.locator('textarea, .ant-input');
    if (await textArea.isVisible()) {
      await textArea.fill('Modified question for testing purposes');
      
      const saveButton = editModal.locator('button:has-text("Save"), .ant-btn-primary');
      if (await saveButton.isVisible()) {
        await saveButton.click();
      }
    }
  }
}
```

#### Step 6.2: Regenerate Questions (if available)
```javascript
// Look for regenerate functionality
const regenerateButton = page.locator('button:has-text("Regenerate"), .ant-btn:has-text("Regenerate")');
if (await regenerateButton.isVisible()) {
  await regenerateButton.click();
  
  // Wait for regeneration process
  await page.waitForSelector('.ant-spin-spinning', { timeout: 5000 });
  await page.waitForSelector('.ant-spin-spinning', { 
    state: 'detached', 
    timeout: 120000 
  });
  
  // Verify new questions are generated
  await expect(successNotification).toBeVisible();
}
```

## 🚨 Common Issues and Solutions

### Issue 1: Generation Timeout
**Symptoms**: Loading spinner doesn't disappear, no questions generated
**Solutions**:
- Increase timeout to 2-3 minutes
- Check AI service availability
- Verify backend API connectivity
- Retry with fewer questions

### Issue 2: Invalid Parameter Error
**Symptoms**: Error notification about invalid parameters
**Solutions**:
- Ensure question count is between 1-20
- Select at least one skill category
- Verify job position has sufficient data
- Check parameter validation logic

### Issue 3: Empty or Poor Quality Questions
**Symptoms**: Questions are generated but content is poor
**Solutions**:
- Verify job description has sufficient detail
- Check AI service configuration
- Try different skill category combinations
- Report to development team for AI model improvement

### Issue 4: UI Not Updating After Generation
**Symptoms**: Generation completes but questions don't appear
**Solutions**:
- Refresh the page and retry
- Check browser console for JavaScript errors
- Verify API response contains question data
- Test with different browsers

## 📊 Performance Considerations

### Generation Time Expectations
- **Small Request (1-5 questions)**: 15-30 seconds
- **Medium Request (6-15 questions)**: 30-60 seconds
- **Large Request (16-20 questions)**: 60-120 seconds

### Optimization Strategies
- Use appropriate timeouts for AI generation
- Provide clear loading feedback to users
- Implement retry mechanisms for failures
- Cache generated questions when possible

This workflow documentation provides comprehensive guidance for testing the AI-powered interview question generation functionality in the SmartHR application.
