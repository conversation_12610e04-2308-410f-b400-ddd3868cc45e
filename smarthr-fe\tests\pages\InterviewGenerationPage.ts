/**
 * Enhanced Page Object Model for Interview Question Generation
 * 
 * This page object demonstrates the patterns documented in the playwright folder
 * and provides comprehensive methods for testing interview question generation.
 * 
 * Based on documentation from:
 * - playwright/examples/page-object-models.md
 * - playwright/workflows/interview-generation-workflow.md
 * - playwright/ui-elements/selectors-reference.md
 */

import { Page, Locator, expect } from '@playwright/test';
import { BasePage } from './BasePage';
import { waitForApiCalls } from '../utils/test-helpers';

export interface QuestionGenerationConfig {
  questionCount?: number;
  skillCategories?: {
    technical?: boolean;
    softSkills?: boolean;
    methodologies?: boolean;
    languageTools?: boolean;
  };
}

export interface GeneratedQuestion {
  text: string;
  category?: string;
  difficulty?: string;
  index: number;
}

/**
 * Page Object Model for Interview Question Generation functionality
 * Follows the patterns documented in playwright/examples/page-object-models.md
 */
export class InterviewGenerationPage extends BasePage {
  private positionId: string;

  constructor(page: Page, positionId?: string) {
    super(page);
    this.positionId = positionId || '';
  }

  getUrl(): string {
    return `/job/${this.positionId}`;
  }

  async waitForPageLoad(): Promise<void> {
    await this.page.waitForSelector('.ant-tabs', { timeout: 30000 });
    await this.waitForLoading();
  }

  // Selectors following playwright/ui-elements/selectors-reference.md patterns
  get interviewTab() {
    return this.page.locator('.ant-tabs-tab:has-text("Interview"), [data-testid="interview-tab"]');
  }

  get questionCountInput() {
    return this.page.locator('.ant-input-number input, input[type="number"]');
  }

  get skillCheckboxes() {
    return {
      technical: this.page.locator('.ant-checkbox:has-text("Technical"), .ant-checkbox:has-text("Technical Skills")'),
      softSkills: this.page.locator('.ant-checkbox:has-text("Soft"), .ant-checkbox:has-text("Soft Skills")'),
      methodologies: this.page.locator('.ant-checkbox:has-text("Methodologies")'),
      languageTools: this.page.locator('.ant-checkbox:has-text("Language"), .ant-checkbox:has-text("Language Tools")')
    };
  }

  get generateButton() {
    return this.page.locator('button:has-text("Generate"), .ant-btn:has-text("Generate")');
  }

  get regenerateButton() {
    return this.page.locator('button:has-text("Regenerate"), .ant-btn:has-text("Regenerate")');
  }

  get questionsContainer() {
    return this.page.locator('[data-testid="questions-container"], .questions-list, .questions-section');
  }

  get questionItems() {
    return this.page.locator('.question-item, [data-testid="question"], .ant-list-item:has(.question-text)');
  }

  get loadingSpinner() {
    return this.page.locator('.ant-spin-spinning');
  }

  get successNotification() {
    return this.page.locator('.ant-notification-success, .ant-message-success');
  }

  get errorNotification() {
    return this.page.locator('.ant-notification-error, .ant-message-error');
  }

  get candidatesContainer() {
    return this.page.locator('.candidates-container, [data-testid="candidates-list"]');
  }

  get candidateCards() {
    return this.page.locator('.candidate-card, .ant-card:has([data-testid="candidate-card"])');
  }

  /**
   * Navigation methods following workflow documentation patterns
   */
  async navigateToInterviewTab(): Promise<void> {
    console.log('Navigating to interview tab');
    await this.interviewTab.click();
    await waitForApiCalls(this.page);
    await this.waitForLoading();
  }

  /**
   * Question generation configuration methods
   */
  async setQuestionCount(count: number): Promise<void> {
    console.log(`Setting question count to ${count}`);
    
    const input = this.questionCountInput;
    await expect(input).toBeVisible();
    
    await input.clear();
    await input.fill(count.toString());
    
    // Verify the value was set
    const inputValue = await input.inputValue();
    expect(inputValue).toBe(count.toString());
  }

  async selectSkillCategory(category: keyof typeof this.skillCheckboxes, select: boolean = true): Promise<void> {
    console.log(`${select ? 'Selecting' : 'Deselecting'} skill category: ${category}`);
    
    const checkbox = this.skillCheckboxes[category];
    if (await checkbox.isVisible()) {
      const isCurrentlyChecked = await checkbox.isChecked();
      
      if (isCurrentlyChecked !== select) {
        await checkbox.click();
      }
      
      // Verify the state
      expect(await checkbox.isChecked()).toBe(select);
    } else {
      console.warn(`Skill category checkbox not found: ${category}`);
    }
  }

  async selectAllSkillCategories(): Promise<void> {
    console.log('Selecting all skill categories');
    
    for (const category of Object.keys(this.skillCheckboxes) as Array<keyof typeof this.skillCheckboxes>) {
      await this.selectSkillCategory(category, true);
    }
  }

  async deselectAllSkillCategories(): Promise<void> {
    console.log('Deselecting all skill categories');
    
    for (const category of Object.keys(this.skillCheckboxes) as Array<keyof typeof this.skillCheckboxes>) {
      await this.selectSkillCategory(category, false);
    }
  }

  async configureQuestionGeneration(config: QuestionGenerationConfig): Promise<void> {
    console.log('Configuring question generation with:', config);
    
    // Set question count
    if (config.questionCount) {
      await this.setQuestionCount(config.questionCount);
    }
    
    // Configure skill categories
    if (config.skillCategories) {
      for (const [category, selected] of Object.entries(config.skillCategories)) {
        if (selected !== undefined) {
          await this.selectSkillCategory(category as keyof typeof this.skillCheckboxes, selected);
        }
      }
    }
  }

  /**
   * Question generation execution methods
   */
  async generateQuestions(): Promise<void> {
    console.log('Initiating question generation');
    
    const generateBtn = this.generateButton;
    await expect(generateBtn).toBeVisible();
    await expect(generateBtn).toBeEnabled();
    
    await generateBtn.click();
    
    // Wait for loading to start
    await this.page.waitForSelector('.ant-spin-spinning', { timeout: 5000 });
    
    // Wait for generation to complete (up to 30 seconds for AI processing)
    await this.waitForLoading(30000);
    
    console.log('Question generation completed');
  }

  async regenerateQuestions(): Promise<void> {
    console.log('Regenerating questions');
    
    const regenerateBtn = this.regenerateButton;
    if (await regenerateBtn.isVisible()) {
      await regenerateBtn.click();
      await this.page.waitForSelector('.ant-spin-spinning', { timeout: 5000 });
      await this.waitForLoading(30000);
      console.log('Question regeneration completed');
    } else {
      throw new Error('Regenerate button not available');
    }
  }

  /**
   * Question validation and retrieval methods
   */
  async getGeneratedQuestions(): Promise<GeneratedQuestion[]> {
    console.log('Retrieving generated questions');
    
    await expect(this.questionsContainer).toBeVisible();
    
    const questionElements = this.questionItems;
    const count = await questionElements.count();
    const questions: GeneratedQuestion[] = [];

    for (let i = 0; i < count; i++) {
      const questionElement = questionElements.nth(i);
      const text = await questionElement.textContent();
      
      // Try to extract category if available
      const categoryElement = questionElement.locator('.skill-tag, .question-category, .ant-tag');
      let category: string | undefined;
      
      if (await categoryElement.isVisible()) {
        category = await categoryElement.textContent() || undefined;
      }

      if (text) {
        questions.push({
          text: text.trim(),
          category: category?.trim(),
          index: i
        });
      }
    }

    console.log(`Retrieved ${questions.length} questions`);
    return questions;
  }

  async getQuestionCount(): Promise<number> {
    const questions = this.questionItems;
    return await questions.count();
  }

  async validateQuestionQuality(questions: GeneratedQuestion[]): Promise<boolean> {
    console.log('Validating question quality');
    
    for (const question of questions) {
      // Question should not be empty
      if (!question.text || question.text.length === 0) {
        console.error(`Question ${question.index} is empty`);
        return false;
      }
      
      // Question should be reasonably long
      if (question.text.length < 10) {
        console.error(`Question ${question.index} is too short: "${question.text}"`);
        return false;
      }
      
      // Question should contain question indicators
      const hasQuestionIndicators = question.text.includes('?') || 
                                  question.text.toLowerCase().includes('what') ||
                                  question.text.toLowerCase().includes('how') ||
                                  question.text.toLowerCase().includes('why') ||
                                  question.text.toLowerCase().includes('describe') ||
                                  question.text.toLowerCase().includes('explain');
      
      if (!hasQuestionIndicators) {
        console.error(`Question ${question.index} doesn't appear to be a proper question: "${question.text}"`);
        return false;
      }
    }
    
    console.log('All questions passed quality validation');
    return true;
  }

  /**
   * Candidate management methods
   */
  async getCandidateCount(): Promise<number> {
    const candidates = this.candidateCards;
    return await candidates.count();
  }

  async clickCandidateByIndex(index: number): Promise<void> {
    console.log(`Clicking candidate at index ${index}`);
    
    const candidate = this.candidateCards.nth(index);
    await expect(candidate).toBeVisible();
    await candidate.click();
    
    // Wait for candidate drawer/modal to open
    await this.page.waitForSelector('.ant-drawer, .ant-modal', { timeout: 10000 });
  }

  /**
   * Assertion methods
   */
  async expectQuestionsGenerated(minCount: number = 1): Promise<void> {
    console.log(`Expecting at least ${minCount} questions to be generated`);
    
    await expect(this.questionsContainer).toBeVisible();
    const questionCount = await this.getQuestionCount();
    expect(questionCount).toBeGreaterThanOrEqual(minCount);
    
    console.log(`✅ Found ${questionCount} generated questions`);
  }

  async expectSuccessNotification(): Promise<void> {
    await expect(this.successNotification).toBeVisible({ timeout: 10000 });
    console.log('✅ Success notification displayed');
  }

  async expectErrorNotification(): Promise<void> {
    await expect(this.errorNotification).toBeVisible({ timeout: 10000 });
    console.log('⚠️ Error notification displayed');
  }

  async expectLoadingComplete(): Promise<void> {
    await expect(this.loadingSpinner).not.toBeVisible({ timeout: 30000 });
    console.log('✅ Loading completed');
  }

  /**
   * Complete workflow method combining all steps
   */
  async executeCompleteQuestionGenerationWorkflow(config: QuestionGenerationConfig): Promise<GeneratedQuestion[]> {
    console.log('Executing complete question generation workflow');
    
    // Step 1: Navigate to interview tab
    await this.navigateToInterviewTab();
    
    // Step 2: Configure generation parameters
    await this.configureQuestionGeneration(config);
    
    // Step 3: Generate questions
    await this.generateQuestions();
    
    // Step 4: Verify success
    await this.expectSuccessNotification();
    await this.expectLoadingComplete();
    
    // Step 5: Retrieve and validate questions
    const questions = await this.getGeneratedQuestions();
    await this.expectQuestionsGenerated(1);
    
    // Step 6: Validate question quality
    const isQualityValid = await this.validateQuestionQuality(questions);
    expect(isQualityValid).toBeTruthy();
    
    console.log('✅ Complete question generation workflow executed successfully');
    return questions;
  }
}
