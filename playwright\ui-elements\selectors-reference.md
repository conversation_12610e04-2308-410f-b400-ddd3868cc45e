# SmartHR UI Selectors Reference

This document provides a comprehensive reference of UI element selectors for the SmartHR application, organized by component type and functionality. These selectors are essential for reliable automated testing.

## 📋 Selector Priority Guidelines

### Priority Order (Most to Least Reliable)
1. **Data-testid attributes** - `[data-testid="element-name"]`
2. **Ant Design component classes** - `.ant-btn-primary`, `.ant-table-row`
3. **Text content selectors** - `button:has-text("Save")`, `.ant-btn:has-text("Generate")`
4. **CSS selectors** - `input[type="text"]`, `.custom-class`

### Best Practices
- Always prefer data-testid when available
- Use Ant Design classes for component identification
- Combine selectors for specificity when needed
- Avoid position-based selectors (`:nth-child()`)

## 🏗️ Layout and Navigation Selectors

### Header and Navigation
```javascript
// Main header
'.ant-layout-header'

// Navigation menu
'.ant-menu-horizontal'
'.ant-menu-item'

// User menu dropdown
'.ant-dropdown-menu'
'.ant-dropdown-menu-item'

// Breadcrumbs
'.ant-breadcrumb'
'.ant-breadcrumb-link'

// Back button
'.ant-btn:has(.anticon-arrow-left)'
'button:has-text("Back")'
```

### Tab Navigation
```javascript
// Tab container
'.ant-tabs'
'.ant-tabs-nav'

// Individual tabs
'.ant-tabs-tab'
'.ant-tabs-tab-active'

// Specific tabs by text
'.ant-tabs-tab:has-text("Description")'
'.ant-tabs-tab:has-text("Interview")'
'.ant-tabs-tab:has-text("Candidates")'
'.ant-tabs-tab:has-text("Matching")'

// Tab content
'.ant-tabs-tabpane'
'.ant-tabs-tabpane-active'
```

### Sidebar and Layout
```javascript
// Main layout
'.ant-layout'
'.ant-layout-content'
'.ant-layout-sider'

// Sidebar navigation
'.ant-menu-vertical'
'.ant-menu-item-selected'
```

## 📊 Data Display Selectors

### Tables
```javascript
// Table container
'.ant-table'
'.ant-table-wrapper'

// Table structure
'.ant-table-thead'
'.ant-table-tbody'
'.ant-table-row'
'.ant-table-cell'

// Table headers
'.ant-table-column-title'
'.ant-table-column-sorter'

// Table actions
'.ant-table-row-selection'
'.ant-table-selection-column'

// Empty state
'.ant-empty'
'.ant-table-placeholder'

// Pagination
'.ant-pagination'
'.ant-pagination-prev'
'.ant-pagination-next'
'.ant-pagination-item'
```

### Cards and Lists
```javascript
// Card components
'.ant-card'
'.ant-card-head'
'.ant-card-body'
'.ant-card-actions'

// List components
'.ant-list'
'.ant-list-item'
'.ant-list-item-meta'

// Candidate cards (custom)
'.candidate-card'
'[data-testid="candidate-card"]'

// Job cards (custom)
'.job-card'
'[data-testid="job-card"]'
```

## 📝 Form and Input Selectors

### Form Structure
```javascript
// Form container
'.ant-form'
'.ant-form-item'
'.ant-form-item-label'
'.ant-form-item-control'

// Form validation
'.ant-form-item-has-error'
'.ant-form-item-explain-error'
'.ant-form-item-required'
```

### Input Fields
```javascript
// Text inputs
'.ant-input'
'input[type="text"]'
'input[placeholder*="Search"]'

// Text areas
'.ant-input-textarea'
'textarea'

// Number inputs
'.ant-input-number'
'input[type="number"]'

// Password inputs
'.ant-input-password'
'input[type="password"]'

// Search inputs
'.ant-input-search'
'.ant-input-search-button'
```

### Select and Dropdown
```javascript
// Select components
'.ant-select'
'.ant-select-selector'
'.ant-select-selection-item'

// Dropdown options
'.ant-select-dropdown'
'.ant-select-item'
'.ant-select-item-option'
'.ant-select-item-option-selected'

// Multi-select
'.ant-select-multiple'
'.ant-select-selection-overflow'
```

### Date and Time Pickers
```javascript
// Date picker
'.ant-picker'
'.ant-picker-input'
'.ant-picker-suffix'

// Date picker dropdown
'.ant-picker-dropdown'
'.ant-picker-panel'
'.ant-picker-cell'
'.ant-picker-cell-today'
'.ant-picker-today-btn'

// Time picker
'.ant-time-picker'
'.ant-time-picker-panel'
```

### Checkboxes and Radio Buttons
```javascript
// Checkboxes
'.ant-checkbox'
'.ant-checkbox-input'
'.ant-checkbox-checked'
'.ant-checkbox-wrapper'

// Radio buttons
'.ant-radio'
'.ant-radio-input'
'.ant-radio-checked'
'.ant-radio-group'

// Specific skill checkboxes
'.ant-checkbox:has-text("Technical")'
'.ant-checkbox:has-text("Soft Skills")'
'.ant-checkbox:has-text("Methodologies")'
'.ant-checkbox:has-text("Language")'
```

## 🔘 Button and Action Selectors

### Button Types
```javascript
// Primary buttons
'.ant-btn-primary'
'button.ant-btn-primary'

// Secondary buttons
'.ant-btn-default'
'.ant-btn-secondary'

// Danger buttons
'.ant-btn-danger'

// Link buttons
'.ant-btn-link'

// Icon buttons
'.ant-btn-icon'
```

### Specific Action Buttons
```javascript
// Common actions
'button:has-text("Save")'
'button:has-text("Cancel")'
'button:has-text("Submit")'
'button:has-text("Apply")'
'button:has-text("Clear")'

// Interview-specific buttons
'button:has-text("Generate")'
'.ant-btn:has-text("Generate")'
'button:has-text("Regenerate")'

// CRUD operations
'button:has-text("Create")'
'button:has-text("Edit")'
'button:has-text("Delete")'
'button:has-text("Update")'

// Navigation buttons
'button:has-text("Back")'
'button:has-text("Next")'
'button:has-text("Previous")'
```

## 🔔 Feedback and Notification Selectors

### Notifications
```javascript
// Success notifications
'.ant-notification-success'
'.ant-message-success'

// Error notifications
'.ant-notification-error'
'.ant-message-error'

// Warning notifications
'.ant-notification-warning'
'.ant-message-warning'

// Info notifications
'.ant-notification-info'
'.ant-message-info'

// Notification content
'.ant-notification-notice-content'
'.ant-notification-notice-message'
```

### Loading States
```javascript
// Loading spinners
'.ant-spin'
'.ant-spin-spinning'
'.ant-spin-container'

// Loading dots
'.ant-spin-dot'
'.ant-spin-dot-spin'

// Progress indicators
'.ant-progress'
'.ant-progress-line'
'.ant-progress-circle'
```

### Modal and Drawer Selectors
```javascript
// Modal components
'.ant-modal'
'.ant-modal-content'
'.ant-modal-header'
'.ant-modal-body'
'.ant-modal-footer'
'.ant-modal-close'

// Drawer components
'.ant-drawer'
'.ant-drawer-content'
'.ant-drawer-header'
'.ant-drawer-body'
'.ant-drawer-close'

// Modal masks
'.ant-modal-mask'
'.ant-drawer-mask'
```

## 🎯 Application-Specific Selectors

### Job Management
```javascript
// Job listings
'[data-testid="job-orders"]'
'.job-table'
'.job-row'

// Job details
'[data-testid="job-details"]'
'.job-description'
'.job-requirements'
'[data-testid="job-title"]'

// Job filters
'.stage-filter'
'.client-filter'
'.location-filter'
```

### Interview Generation
```javascript
// Question generation
'[data-testid="questions-container"]'
'.questions-list'
'.question-item'
'[data-testid="question"]'

// Question configuration
'.question-count-input'
'.skill-categories'
'.generation-config'

// Generated content
'.question-text'
'[data-testid="question-text"]'
'.question-category'
'.skill-tag'
```

### Candidate Management
```javascript
// Candidate lists
'[data-testid="candidates-list"]'
'.candidates-container'
'.candidate-list'

// Candidate cards
'.candidate-card'
'[data-testid="candidate-card"]'
'.candidate-info'

// Candidate details
'[data-testid="candidate-details"]'
'.candidate-profile'
'.candidate-skills'
'.candidate-experience'
```

### Interview Feedback
```javascript
// Feedback forms
'.feedback-form'
'[data-testid="hr-feedback"]'
'[data-testid="tech-feedback"]'

// Feedback tabs
'.ant-tabs-tab:has-text("HR")'
'.ant-tabs-tab:has-text("Technical")'

// Feedback fields
'input[name*="recruiter"]'
'input[name*="scheduled"]'
'textarea[name*="comments"]'
'textarea[name*="transcript"]'

// Status selectors
'.ant-select:has(.ant-select-selection-item:has-text("Status"))'
'.ant-select:has(.ant-select-selection-item:has-text("Recommendation"))'
```

## 🔍 Search and Filter Selectors

### Search Components
```javascript
// Search inputs
'input[placeholder*="Search"]'
'.ant-input-search'
'[data-testid="search-input"]'

// Search buttons
'.ant-input-search-button'
'button:has-text("Search")'

// Clear search
'.ant-input-clear-icon'
'button:has-text("Clear")'
```

### Filter Components
```javascript
// Filter containers
'.filters-container'
'.filter-section'

// Filter dropdowns
'.ant-select:has(.ant-select-selection-item:has-text("Stage"))'
'.ant-select:has(.ant-select-selection-item:has-text("Client"))'
'.ant-select:has(.ant-select-selection-item:has-text("Location"))'

// Filter actions
'button:has-text("Apply Filters")'
'button:has-text("Clear Filters")'
'.ant-btn:has-text("Apply")'
'.ant-btn:has-text("Clear")'
```

## 📱 Responsive and State Selectors

### Responsive Elements
```javascript
// Mobile navigation
'.ant-drawer-content'
'.mobile-menu'

// Responsive tables
'.ant-table-scroll'
'.ant-table-scroll-horizontal'

// Collapsed elements
'.ant-menu-collapsed'
'.ant-layout-sider-collapsed'
```

### State-based Selectors
```javascript
// Active states
'.ant-tabs-tab-active'
'.ant-menu-item-selected'
'.ant-btn-loading'

// Disabled states
'.ant-btn-disabled'
'.ant-input-disabled'
'.ant-select-disabled'

// Error states
'.ant-form-item-has-error'
'.ant-input-status-error'

// Loading states
'.ant-spin-spinning'
'.ant-btn-loading'
```

This comprehensive selector reference provides reliable element identification patterns for automated testing of the SmartHR application across all major components and workflows.
