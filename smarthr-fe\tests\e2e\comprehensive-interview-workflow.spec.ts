/**
 * Comprehensive Interview Question Generation Workflow Test
 * 
 * This test demonstrates the complete interview question generation workflow
 * using the enhanced page object model and following all the patterns
 * documented in the playwright folder.
 * 
 * Features demonstrated:
 * - Complete workflow automation
 * - Advanced page object model usage
 * - Error handling and edge cases
 * - Performance monitoring
 * - Comprehensive validation
 * - Best practices implementation
 */

import { test, expect } from '../fixtures';
import { InterviewGenerationPage } from '../pages/InterviewGenerationPage';
import { createAssertions } from '../utils/assertions';

test.describe('Comprehensive Interview Question Generation Workflow', () => {
  test('should execute complete question generation workflow with all features', async ({ 
    loggedInPage, 
    createdPositionId 
  }) => {
    const assertions = createAssertions(loggedInPage);
    const interviewPage = new InterviewGenerationPage(loggedInPage, createdPositionId);
    
    console.log('🚀 Starting comprehensive interview question generation workflow');
    
    // Step 1: Navigate to the page and verify initial state
    console.log('📍 Step 1: Navigation and initial setup');
    await interviewPage.goto();
    await interviewPage.waitForPageLoad();
    
    // Verify we're on the correct page
    await expect(loggedInPage).toHaveURL(new RegExp(`/job/${createdPositionId}`));
    console.log('✅ Successfully navigated to job details page');
    
    // Step 2: Execute complete workflow with comprehensive configuration
    console.log('⚙️ Step 2: Executing complete question generation workflow');
    
    const generationConfig = {
      questionCount: 10,
      skillCategories: {
        technical: true,
        softSkills: true,
        methodologies: true,
        languageTools: false
      }
    };
    
    const startTime = Date.now();
    const generatedQuestions = await interviewPage.executeCompleteQuestionGenerationWorkflow(generationConfig);
    const executionTime = Date.now() - startTime;
    
    console.log(`⏱️ Workflow completed in ${executionTime}ms`);
    
    // Step 3: Comprehensive validation of results
    console.log('🔍 Step 3: Comprehensive validation of generated questions');
    
    // Validate question count
    expect(generatedQuestions.length).toBeGreaterThan(0);
    expect(generatedQuestions.length).toBeLessThanOrEqual(10);
    console.log(`✅ Generated ${generatedQuestions.length} questions within expected range`);
    
    // Validate question diversity
    const uniqueQuestions = new Set(generatedQuestions.map(q => q.text));
    expect(uniqueQuestions.size).toBe(generatedQuestions.length);
    console.log('✅ All questions are unique');
    
    // Validate question categories if available
    const questionsWithCategories = generatedQuestions.filter(q => q.category);
    if (questionsWithCategories.length > 0) {
      console.log(`✅ Found ${questionsWithCategories.length} questions with categories`);
      
      // Log category distribution
      const categoryDistribution = questionsWithCategories.reduce((acc, q) => {
        const category = q.category || 'Unknown';
        acc[category] = (acc[category] || 0) + 1;
        return acc;
      }, {} as Record<string, number>);
      
      console.log('📊 Category distribution:', categoryDistribution);
    }
    
    // Step 4: Test question regeneration if available
    console.log('🔄 Step 4: Testing question regeneration');
    
    try {
      const initialQuestionTexts = generatedQuestions.map(q => q.text);
      await interviewPage.regenerateQuestions();
      await interviewPage.expectSuccessNotification();
      
      const regeneratedQuestions = await interviewPage.getGeneratedQuestions();
      const regeneratedTexts = regeneratedQuestions.map(q => q.text);
      
      // At least some questions should be different
      const hasChanges = regeneratedTexts.some((text, index) => text !== initialQuestionTexts[index]);
      expect(hasChanges).toBeTruthy();
      
      console.log('✅ Question regeneration successful with changes detected');
    } catch (error) {
      console.log('ℹ️ Question regeneration not available or failed:', error);
    }
    
    // Step 5: Test candidate interaction if candidates are available
    console.log('👥 Step 5: Testing candidate interaction');
    
    const candidateCount = await interviewPage.getCandidateCount();
    console.log(`Found ${candidateCount} candidates`);
    
    if (candidateCount > 0) {
      try {
        await interviewPage.clickCandidateByIndex(0);
        
        // Verify candidate drawer/modal opened
        const candidateDrawer = loggedInPage.locator('.ant-drawer, .ant-modal');
        await expect(candidateDrawer).toBeVisible({ timeout: 10000 });
        
        console.log('✅ Successfully opened candidate details');
        
        // Close the drawer/modal
        const closeButton = candidateDrawer.locator('.ant-drawer-close, .ant-modal-close');
        if (await closeButton.isVisible()) {
          await closeButton.click();
          await expect(candidateDrawer).not.toBeVisible();
        }
      } catch (error) {
        console.log('ℹ️ Candidate interaction not available:', error);
      }
    }
    
    // Step 6: Performance validation
    console.log('⚡ Step 6: Performance validation');
    
    // Workflow should complete within reasonable time (2 minutes)
    expect(executionTime).toBeLessThan(120000);
    console.log(`✅ Workflow performance acceptable: ${executionTime}ms`);
    
    // Step 7: Final state verification
    console.log('🏁 Step 7: Final state verification');
    
    // Questions should still be visible
    await interviewPage.expectQuestionsGenerated(1);
    
    // Page should be in stable state
    await interviewPage.expectLoadingComplete();
    
    console.log('🎉 Comprehensive interview question generation workflow completed successfully!');
    
    // Log summary
    console.log('\n📋 WORKFLOW SUMMARY:');
    console.log(`- Questions generated: ${generatedQuestions.length}`);
    console.log(`- Execution time: ${executionTime}ms`);
    console.log(`- Candidates available: ${candidateCount}`);
    console.log(`- Categories found: ${questionsWithCategories.length > 0 ? 'Yes' : 'No'}`);
  });

  test('should handle different skill category combinations', async ({ 
    loggedInPage, 
    createdPositionId 
  }) => {
    const interviewPage = new InterviewGenerationPage(loggedInPage, createdPositionId);
    
    await interviewPage.goto();
    await interviewPage.waitForPageLoad();
    
    // Test different skill combinations
    const skillCombinations = [
      { name: 'Technical Only', config: { technical: true, softSkills: false, methodologies: false, languageTools: false } },
      { name: 'Soft Skills Only', config: { technical: false, softSkills: true, methodologies: false, languageTools: false } },
      { name: 'Technical + Methodologies', config: { technical: true, softSkills: false, methodologies: true, languageTools: false } },
      { name: 'All Skills', config: { technical: true, softSkills: true, methodologies: true, languageTools: true } }
    ];
    
    for (const combination of skillCombinations) {
      console.log(`Testing skill combination: ${combination.name}`);
      
      const questions = await interviewPage.executeCompleteQuestionGenerationWorkflow({
        questionCount: 5,
        skillCategories: combination.config
      });
      
      expect(questions.length).toBeGreaterThan(0);
      console.log(`✅ ${combination.name}: Generated ${questions.length} questions`);
    }
  });

  test('should validate question count boundaries', async ({ 
    loggedInPage, 
    createdPositionId 
  }) => {
    const interviewPage = new InterviewGenerationPage(loggedInPage, createdPositionId);
    
    await interviewPage.goto();
    await interviewPage.waitForPageLoad();
    await interviewPage.navigateToInterviewTab();
    
    // Test minimum valid count
    console.log('Testing minimum question count (1)');
    const minQuestions = await interviewPage.executeCompleteQuestionGenerationWorkflow({
      questionCount: 1,
      skillCategories: { technical: true }
    });
    
    expect(minQuestions.length).toBe(1);
    console.log('✅ Minimum count validation passed');
    
    // Test maximum valid count
    console.log('Testing maximum question count (20)');
    const maxQuestions = await interviewPage.executeCompleteQuestionGenerationWorkflow({
      questionCount: 20,
      skillCategories: { technical: true, softSkills: true }
    });
    
    expect(maxQuestions.length).toBeGreaterThan(0);
    expect(maxQuestions.length).toBeLessThanOrEqual(20);
    console.log(`✅ Maximum count validation passed: ${maxQuestions.length} questions`);
    
    // Test invalid count (should show error)
    console.log('Testing invalid question count (25)');
    
    await interviewPage.navigateToInterviewTab();
    await interviewPage.setQuestionCount(25);
    await interviewPage.selectSkillCategory('technical', true);
    
    try {
      await interviewPage.generateQuestions();
      // If generation succeeds, it should be capped at maximum
      const invalidCountQuestions = await interviewPage.getGeneratedQuestions();
      expect(invalidCountQuestions.length).toBeLessThanOrEqual(20);
    } catch (error) {
      // If generation fails, should show error notification
      await interviewPage.expectErrorNotification();
      console.log('✅ Invalid count properly rejected');
    }
  });

  test('should handle network errors and recovery', async ({ 
    loggedInPage, 
    createdPositionId 
  }) => {
    const interviewPage = new InterviewGenerationPage(loggedInPage, createdPositionId);
    
    await interviewPage.goto();
    await interviewPage.waitForPageLoad();
    await interviewPage.navigateToInterviewTab();
    
    // Simulate network failure
    console.log('Simulating network failure');
    await loggedInPage.route('**/api/interview/**/questions', route => route.abort());
    
    // Configure and attempt generation
    await interviewPage.configureQuestionGeneration({
      questionCount: 5,
      skillCategories: { technical: true }
    });
    
    try {
      await interviewPage.generateQuestions();
    } catch (error) {
      console.log('Expected error during network failure');
    }
    
    // Should show error notification
    await interviewPage.expectErrorNotification();
    
    // Application should remain functional
    await expect(loggedInPage.locator('body')).toBeVisible();
    
    // Restore network and retry
    console.log('Restoring network and retrying');
    await loggedInPage.unroute('**/api/interview/**/questions');
    
    // Should now succeed
    const recoveredQuestions = await interviewPage.executeCompleteQuestionGenerationWorkflow({
      questionCount: 3,
      skillCategories: { technical: true }
    });
    
    expect(recoveredQuestions.length).toBeGreaterThan(0);
    console.log('✅ Successfully recovered from network error');
  });
});
