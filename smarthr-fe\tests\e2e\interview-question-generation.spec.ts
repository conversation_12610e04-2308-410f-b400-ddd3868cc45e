/**
 * Automated Test for Interview Question Generation
 * 
 * This test demonstrates the complete interview question generation workflow
 * using the comprehensive documentation created in the playwright folder.
 * 
 * Test covers:
 * - Navigation to job details and interview tab
 * - Configuration of question generation parameters
 * - Skill category selection
 * - Question generation process
 * - Validation of generated questions
 * - Error handling scenarios
 */

import { test, expect } from '../fixtures';
import { createAssertions } from '../utils/assertions';
import { JobDetailsPage } from '../pages';

test.describe('Interview Question Generation Workflow', () => {
  test('should generate interview questions with custom configuration', async ({ 
    loggedInPage, 
    createdPositionId 
  }) => {
    const assertions = createAssertions(loggedInPage);
    const jobDetailsPage = new JobDetailsPage(loggedInPage, createdPositionId);
    
    // Step 1: Navigate to job details page
    console.log('Step 1: Navigating to job details page');
    await jobDetailsPage.goto();
    await jobDetailsPage.waitForPageLoad();
    
    // Verify we're on the correct page
    await expect(loggedInPage).toHaveURL(new RegExp(`/job/${createdPositionId}`));
    
    // Step 2: Navigate to interview tab
    console.log('Step 2: Navigating to interview tab');
    await jobDetailsPage.navigateToTab('interview');
    await jobDetailsPage.expectTabToBeActive('Interview');
    
    // Step 3: Configure question generation parameters
    console.log('Step 3: Configuring question generation parameters');
    
    // Set question count to 8
    const questionCountInput = jobDetailsPage.questionCountInput;
    if (await questionCountInput.isVisible()) {
      await questionCountInput.clear();
      await questionCountInput.fill('8');
      
      // Verify the input value
      const inputValue = await questionCountInput.inputValue();
      expect(inputValue).toBe('8');
    }
    
    // Step 4: Select skill categories
    console.log('Step 4: Selecting skill categories');
    
    // Select Technical Skills
    const technicalCheckbox = jobDetailsPage.technicalSkillsCheckbox;
    if (await technicalCheckbox.isVisible()) {
      const isChecked = await technicalCheckbox.isChecked();
      if (!isChecked) {
        await technicalCheckbox.click();
      }
      expect(await technicalCheckbox.isChecked()).toBeTruthy();
    }
    
    // Select Soft Skills
    const softSkillsCheckbox = jobDetailsPage.softSkillsCheckbox;
    if (await softSkillsCheckbox.isVisible()) {
      const isChecked = await softSkillsCheckbox.isChecked();
      if (!isChecked) {
        await softSkillsCheckbox.click();
      }
      expect(await softSkillsCheckbox.isChecked()).toBeTruthy();
    }
    
    // Select Methodologies
    const methodologiesCheckbox = jobDetailsPage.methodologiesCheckbox;
    if (await methodologiesCheckbox.isVisible()) {
      const isChecked = await methodologiesCheckbox.isChecked();
      if (!isChecked) {
        await methodologiesCheckbox.click();
      }
      expect(await methodologiesCheckbox.isChecked()).toBeTruthy();
    }
    
    // Step 5: Generate questions
    console.log('Step 5: Generating interview questions');
    
    const generateButton = jobDetailsPage.generateQuestionsButton;
    await expect(generateButton).toBeVisible();
    await expect(generateButton).toBeEnabled();
    
    // Click generate button
    await generateButton.click();
    
    // Step 6: Wait for generation process
    console.log('Step 6: Waiting for question generation to complete');
    
    // Should show loading state
    await loggedInPage.waitForSelector('.ant-spin-spinning', { timeout: 5000 });
    
    // Wait for loading to complete (up to 30 seconds for AI generation)
    await assertions.expectLoadingComplete();
    
    // Should show success notification
    await assertions.expectSuccessNotification();
    
    // Step 7: Validate generated questions
    console.log('Step 7: Validating generated questions');
    
    // Questions container should be visible
    const questionsContainer = jobDetailsPage.questionsContainer;
    await expect(questionsContainer).toBeVisible({ timeout: 10000 });
    
    // Should have generated questions
    const questionItems = loggedInPage.locator('.question-item, [data-testid="question"], .ant-list-item');
    const questionCount = await questionItems.count();
    
    console.log(`Generated ${questionCount} questions`);
    expect(questionCount).toBeGreaterThan(0);
    expect(questionCount).toBeLessThanOrEqual(8); // Should not exceed requested count
    
    // Step 8: Validate question content quality
    console.log('Step 8: Validating question content quality');
    
    for (let i = 0; i < Math.min(questionCount, 5); i++) {
      const questionElement = questionItems.nth(i);
      const questionText = await questionElement.textContent();
      
      // Question should not be empty
      expect(questionText?.trim()).toBeTruthy();
      
      // Question should be reasonably long
      expect(questionText?.trim().length).toBeGreaterThan(10);
      
      // Question should contain question indicators
      const hasQuestionIndicators = questionText?.includes('?') || 
                                  questionText?.toLowerCase().includes('what') ||
                                  questionText?.toLowerCase().includes('how') ||
                                  questionText?.toLowerCase().includes('why') ||
                                  questionText?.toLowerCase().includes('describe') ||
                                  questionText?.toLowerCase().includes('explain');
      
      expect(hasQuestionIndicators).toBeTruthy();
      
      console.log(`Question ${i + 1}: ${questionText?.substring(0, 50)}...`);
    }
    
    // Step 9: Verify questions are categorized
    console.log('Step 9: Verifying question categorization');
    
    // Look for category indicators or tags
    const categoryTags = loggedInPage.locator('.skill-tag, .question-category, .ant-tag');
    if (await categoryTags.count() > 0) {
      const firstTag = await categoryTags.first().textContent();
      console.log(`Found question category: ${firstTag}`);
      expect(firstTag?.trim()).toBeTruthy();
    }
    
    console.log('✅ Interview question generation test completed successfully');
  });

  test('should handle question generation with different skill combinations', async ({ 
    loggedInPage, 
    createdPositionId 
  }) => {
    const assertions = createAssertions(loggedInPage);
    const jobDetailsPage = new JobDetailsPage(loggedInPage, createdPositionId);
    
    await jobDetailsPage.goto();
    await jobDetailsPage.navigateToTab('interview');
    
    // Test with only Technical Skills selected
    console.log('Testing with only Technical Skills');
    
    // Uncheck all first
    const checkboxes = [
      jobDetailsPage.technicalSkillsCheckbox,
      jobDetailsPage.softSkillsCheckbox,
      jobDetailsPage.methodologiesCheckbox,
      jobDetailsPage.languageToolsCheckbox
    ];
    
    for (const checkbox of checkboxes) {
      if (await checkbox.isVisible() && await checkbox.isChecked()) {
        await checkbox.click();
      }
    }
    
    // Select only Technical Skills
    if (await jobDetailsPage.technicalSkillsCheckbox.isVisible()) {
      await jobDetailsPage.technicalSkillsCheckbox.click();
      expect(await jobDetailsPage.technicalSkillsCheckbox.isChecked()).toBeTruthy();
    }
    
    // Set question count
    if (await jobDetailsPage.questionCountInput.isVisible()) {
      await jobDetailsPage.questionCountInput.fill('5');
    }
    
    // Generate questions
    await jobDetailsPage.generateQuestionsButton.click();
    await assertions.expectLoadingComplete();
    await assertions.expectSuccessNotification();
    
    // Verify questions were generated
    const questionItems = loggedInPage.locator('.question-item, [data-testid="question"]');
    const questionCount = await questionItems.count();
    expect(questionCount).toBeGreaterThan(0);
    
    console.log(`Generated ${questionCount} technical questions`);
  });

  test('should validate question count limits', async ({ 
    loggedInPage, 
    createdPositionId 
  }) => {
    const assertions = createAssertions(loggedInPage);
    const jobDetailsPage = new JobDetailsPage(loggedInPage, createdPositionId);
    
    await jobDetailsPage.goto();
    await jobDetailsPage.navigateToTab('interview');
    
    // Test with invalid question count (too high)
    console.log('Testing with invalid question count (25)');
    
    const questionCountInput = jobDetailsPage.questionCountInput;
    if (await questionCountInput.isVisible()) {
      await questionCountInput.fill('25');
      
      // Select at least one skill category
      if (await jobDetailsPage.technicalSkillsCheckbox.isVisible()) {
        await jobDetailsPage.technicalSkillsCheckbox.click();
      }
      
      // Try to generate
      await jobDetailsPage.generateQuestionsButton.click();
      
      // Should show error notification or validation error
      const hasErrorNotification = await loggedInPage.locator('.ant-notification-error').isVisible();
      const hasValidationError = await loggedInPage.locator('.ant-form-item-has-error').isVisible();
      
      expect(hasErrorNotification || hasValidationError).toBeTruthy();
      
      if (hasErrorNotification) {
        const errorText = await loggedInPage.locator('.ant-notification-error').textContent();
        console.log(`Error message: ${errorText}`);
        expect(errorText).toMatch(/maximum|limit|range/i);
      }
    }
  });

  test('should handle network errors gracefully', async ({ 
    loggedInPage, 
    createdPositionId 
  }) => {
    const assertions = createAssertions(loggedInPage);
    const jobDetailsPage = new JobDetailsPage(loggedInPage, createdPositionId);
    
    await jobDetailsPage.goto();
    await jobDetailsPage.navigateToTab('interview');
    
    // Simulate network error for question generation
    await loggedInPage.route('**/api/interview/**/questions', route => route.abort());
    
    // Configure and attempt generation
    if (await jobDetailsPage.questionCountInput.isVisible()) {
      await jobDetailsPage.questionCountInput.fill('5');
    }
    
    if (await jobDetailsPage.technicalSkillsCheckbox.isVisible()) {
      await jobDetailsPage.technicalSkillsCheckbox.click();
    }
    
    await jobDetailsPage.generateQuestionsButton.click();
    
    // Should show error notification
    await assertions.expectErrorNotification();
    
    // Application should remain functional
    await expect(loggedInPage.locator('body')).toBeVisible();
    
    // Restore network
    await loggedInPage.unroute('**/api/interview/**/questions');
    
    console.log('✅ Network error handling test completed');
  });

  test('should regenerate questions when requested', async ({ 
    loggedInPage, 
    createdPositionId 
  }) => {
    const assertions = createAssertions(loggedInPage);
    const jobDetailsPage = new JobDetailsPage(loggedInPage, createdPositionId);
    
    await jobDetailsPage.goto();
    await jobDetailsPage.navigateToTab('interview');
    
    // First, generate initial questions
    if (await jobDetailsPage.questionCountInput.isVisible()) {
      await jobDetailsPage.questionCountInput.fill('3');
    }
    
    if (await jobDetailsPage.technicalSkillsCheckbox.isVisible()) {
      await jobDetailsPage.technicalSkillsCheckbox.click();
    }
    
    await jobDetailsPage.generateQuestionsButton.click();
    await assertions.expectLoadingComplete();
    await assertions.expectSuccessNotification();
    
    // Get initial questions
    const initialQuestions = await jobDetailsPage.getGeneratedQuestions();
    expect(initialQuestions.length).toBeGreaterThan(0);
    
    // Look for regenerate button
    const regenerateButton = loggedInPage.locator('button:has-text("Regenerate"), .ant-btn:has-text("Regenerate")');
    
    if (await regenerateButton.isVisible()) {
      console.log('Testing question regeneration');
      
      await regenerateButton.click();
      await assertions.expectLoadingComplete();
      await assertions.expectSuccessNotification();
      
      // Get new questions
      const newQuestions = await jobDetailsPage.getGeneratedQuestions();
      expect(newQuestions.length).toBeGreaterThan(0);
      
      // At least some questions should be different
      const hasChanges = newQuestions.some((q, i) => q !== initialQuestions[i]);
      expect(hasChanges).toBeTruthy();
      
      console.log('✅ Question regeneration test completed');
    } else {
      console.log('Regenerate functionality not available in current UI');
    }
  });
});
