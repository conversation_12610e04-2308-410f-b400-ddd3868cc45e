# Standard library imports
import json
import traceback
from contextlib import contextmanager
from typing import List, Optional
import logging

# Third-party imports
from datetime import datetime
import psycopg2
from psycopg2.extras import Json
from fastapi import HTTPException
from langchain_core.messages import HumanMessage
from typing import Union

# Internal imports
from config.config import MODELS_CONFIG
from core.config import settings
from models.enums import StatusInterview
from models.llm import inference_with_fallback, get_related_class_definitions
from models.interview import (
    InterviewCreate,
    InterviewProcessingRequest,
    ExtractedAnswers,
    ParaphrasedAnswers,
    ProcessType,
    QA_model,
    EvaluationResult,
    EvaluateInterviewNoQA,
    Interview,
    InterviewHr,
    InterviewTec,
    TranscriptQuestions,
    TranscriptQuestion,
)
from typing import List, Optional
from models.models import SingleQuestions
from controllers.positions_controller import get_position_by_id
from controllers.candidates_controller import get_candidate_by_id

# Telemetry Section
logging.basicConfig(level=logging.DEBUG)
logger = logging.getLogger(__name__)
logger.setLevel(logging.DEBUG)


def _validate_evaluation_result(result: EvaluationResult, expected_questions_count: int, evaluation_type: str) -> List[str]:
    """
    Validate evaluation results to catch potential misclassifications.

    Args:
        result: The evaluation result to validate
        expected_questions_count: Expected number of questions
        evaluation_type: Type of evaluation for logging ("transcript-based" or "predefined-questions")

    Returns:
        List of validation issues found (empty if no issues)
    """
    issues = []

    # Check if per_question array has correct length
    if len(result.per_question) != expected_questions_count:
        issues.append(f"Expected {expected_questions_count} question evaluations, got {len(result.per_question)}")

    # Count answered vs unanswered questions
    answered_count = 0
    unanswered_count = 0
    valid_responses = 0  # Responses that should count toward percentage
    invalid_responses = 0  # Wrong answers that don't count toward percentage
    junior_responses = 0

    for q_eval in result.per_question:
        # Check if this appears to be an answered question based on explanation
        explanation_lower = q_eval.explanation.lower()

        # Look for indicators of valid responses (including honest admissions of lack of knowledge)
        valid_response_indicators = [
            "basic understanding", "limited experience", "learning",
            "honest admission", "limited knowledge", "lack of knowledge"
        ]

        # Look for indicators of invalid/wrong responses
        invalid_response_indicators = [
            "incorrect", "wrong", "confused", "misunderstood", "invalid",
            "completely wrong", "fundamental misunderstanding", "nonsensical"
        ]

        # Look for indicators of truly unanswered questions (complete silence/no response)
        unanswered_indicators = [
            "no response found", "not answered", "no answer provided",
            "question not addressed", "skipped", "missing response",
            "remained silent", "no verbal response", "candidate did not respond"
        ]

        has_valid_indicator = any(indicator in explanation_lower for indicator in valid_response_indicators)
        has_invalid_indicator = any(indicator in explanation_lower for indicator in invalid_response_indicators)
        has_unanswered_indicator = any(indicator in explanation_lower for indicator in unanswered_indicators)

        # Determine if question was answered based on explanation content
        is_answered = not has_unanswered_indicator

        if is_answered:
            answered_count += 1
            if q_eval.detected_seniority == 'junior':
                junior_responses += 1
                if has_valid_indicator or not has_invalid_indicator:
                    # Count as valid unless explicitly marked as invalid
                    valid_responses += 1
                else:
                    invalid_responses += 1
            else:
                # Mid/senior responses are always valid
                valid_responses += 1
        else:
            unanswered_count += 1

    # Calculate expected percentage based on VALID responses only
    expected_percentage = (valid_responses / expected_questions_count) * 100 if expected_questions_count > 0 else 0
    actual_percentage = result.percentage_of_match

    # Allow for larger tolerance since invalid responses affect percentage calculation
    if abs(expected_percentage - actual_percentage) > 10:
        issues.append(f"Percentage mismatch: calculated {expected_percentage:.1f}% (valid responses) but got {actual_percentage:.1f}%")

    # Check for suspicious patterns
    if junior_responses > 0 and actual_percentage < (junior_responses / expected_questions_count * 100):
        issues.append(f"Possible misclassification: {junior_responses} junior responses but percentage suggests some were not counted")

    # Check for extraction failure patterns
    extraction_failure_indicators = [
        "no enough information has been provided",
        "invalid transcript",
        "all actual answers are \"invalid transcript\"",
        "no responses found in transcript",
        "extraction failed"
    ]

    explanation_lower = result.explanation.lower()
    has_extraction_failure = any(indicator in explanation_lower for indicator in extraction_failure_indicators)

    # Critical check: If all questions appear unanswered but we expected responses
    if unanswered_count == expected_questions_count and expected_questions_count > 0:
        issues.append(f"CRITICAL: All {expected_questions_count} questions marked as unanswered - likely extraction failure")
        logger.error(f"VALIDATION CRITICAL: All questions unanswered in {evaluation_type} evaluation - this suggests extraction failure")

    # Check for extraction failure in explanation
    if has_extraction_failure:
        issues.append(f"CRITICAL: Evaluation explanation indicates extraction failure: '{result.explanation[:100]}...'")
        logger.error(f"VALIDATION CRITICAL: Extraction failure detected in {evaluation_type} evaluation")

    # Check for suspiciously low response rate when we expect higher
    if valid_responses == 0 and expected_questions_count > 5:
        issues.append(f"CRITICAL: Zero valid responses found for {expected_questions_count} questions - likely extraction failure")
        logger.error(f"VALIDATION CRITICAL: Zero valid responses in {evaluation_type} evaluation with {expected_questions_count} questions")

    # Log detailed breakdown for debugging
    logger.info(f"{evaluation_type.upper()} VALIDATION: answered={answered_count}, unanswered={unanswered_count}, valid_responses={valid_responses}, invalid_responses={invalid_responses}, junior_responses={junior_responses}")
    logger.info(f"{evaluation_type.upper()} VALIDATION: expected_percentage={expected_percentage:.1f}% (based on valid responses), actual_percentage={actual_percentage:.1f}%")

    if issues:
        logger.warning(f"{evaluation_type.upper()} VALIDATION: Found {len(issues)} validation issues")
        for issue in issues:
            logger.warning(f"{evaluation_type.upper()} VALIDATION ISSUE: {issue}")

    return issues


# Database connection context manager
@contextmanager
def get_cursor():
    """
    Context manager for PostgreSQL cursor with error handling.
    """
    conn = None
    try:
        conn = psycopg2.connect(
            settings.DATABASE_URL,
            connect_timeout=120,
            options="-c statement_timeout=120000",
            keepalives_idle=30
        )
        with conn:
            with conn.cursor() as cur:
                yield cur
    except psycopg2.Error as e:
        raise HTTPException(status_code=500, detail=f"Database error: {str(e)}")
    finally:
        if conn:
            conn.close()


# Helper function to build text for LLM prompts
# This function constructs a text prompt based on the provided items.
def get_topics(include: str) -> str:
    """
    This function constructs a text prompt based on the provided items.

    Args:
        include (str): A comma-separated string of items to include in the prompt.

    Returns:
        str: A formatted string based on the provided items.
    """
    base = ""
    desired_order = ['Technical Skills', 'Methodologies', 'Soft Skills', 'Language - Tools']

    # Map lowercase to original case
    lower_to_original = {item.lower(): item for item in desired_order}

    # Normalize input items to lowercase
    input_items = [item.strip().lower() for item in include.split(",") if item.strip()]

    if not input_items:
        return f"{base}{', '.join(desired_order)}."

    # Keep the order from desired_order and match only those present
    ordered = [lower_to_original[item.lower()] for item in desired_order if item.lower() in input_items]

    return f"{base}{', '.join(ordered)}."


def _attempt_fallback_extraction(request: InterviewProcessingRequest) -> Optional[ExtractedAnswers]:
    """
    Attempt alternative extraction methods when primary extraction fails.

    Args:
        request: The original interview processing request

    Returns:
        ExtractedAnswers if successful, None if all fallback methods fail
    """
    logger.info("FALLBACK EXTRACTION: Starting alternative extraction approaches...")

    # Fallback 1: More aggressive pattern matching
    fallback_prompt_1 = (
        "EMERGENCY EXTRACTION MODE: The primary extraction failed. You must be more aggressive in finding responses.\n\n"
        "INSTRUCTIONS:\n"
        "- Look for ANY text that could be a candidate response, even if it's not clearly marked\n"
        "- Include responses that are:\n"
        "  * After question numbers (1., 2., etc.)\n"
        "  * Following question marks (?)\n"
        "  * Any text that seems like an answer to a technical question\n"
        "  * Responses in conversational format (Q: ... A: ...)\n"
        "  * Any admission of knowledge or lack thereof\n"
        "- If you find ANYTHING that looks like a response, extract it\n"
        "- Only use 'Invalid transcript' if you absolutely cannot find any response pattern\n"
        "- Be liberal in what you consider a response - err on the side of inclusion\n\n"
        f"Questions to find responses for:\n"
        + "\n".join(f"{i + 1}. {q}" for i, q in enumerate(request.questions))
        + f"\n\nTranscript to search:\n{request.transcript}"
    )

    try:
        user_msg = HumanMessage(content=fallback_prompt_1)
        schema_text = get_related_class_definitions(ExtractedAnswers)

        result = inference_with_fallback(
            task_prompt="Extract responses using aggressive pattern matching.",
            model_schema=ExtractedAnswers,
            user_messages=[user_msg],
            model_schema_text=schema_text,
            models_order=MODELS_CONFIG["default_models_order"],
        )

        if result and hasattr(result, 'answers'):
            valid_answers = [ans for ans in result.answers if ans != "Invalid transcript"]
            if len(valid_answers) > 0:
                logger.info(f"FALLBACK EXTRACTION: Method 1 succeeded - found {len(valid_answers)} valid responses")
                return result

    except Exception as e:
        logger.error(f"FALLBACK EXTRACTION: Method 1 failed with error: {str(e)}")

    # Fallback 2: Question-by-question extraction
    logger.info("FALLBACK EXTRACTION: Trying question-by-question approach...")
    try:
        extracted_answers = []

        for question in request.questions:
            # Simple string-based extraction for individual questions
            response_text = _extract_single_response(question, request.transcript)
            extracted_answers.append(response_text if response_text else "Invalid transcript")

        if extracted_answers:
            valid_count = len([ans for ans in extracted_answers if ans != "Invalid transcript"])
            if valid_count > 0:
                logger.info(f"FALLBACK EXTRACTION: Method 2 succeeded - found {valid_count} valid responses")
                return ExtractedAnswers(answers=extracted_answers)

    except Exception as e:
        logger.error(f"FALLBACK EXTRACTION: Method 2 failed with error: {str(e)}")

    logger.error("FALLBACK EXTRACTION: All fallback methods failed")
    return None


def _extract_single_response(question: str, transcript: str) -> Optional[str]:
    """
    Simple pattern-based extraction for a single question-response pair.

    Args:
        question: The question to find a response for
        transcript: The full transcript

    Returns:
        The extracted response or None if not found
    """
    # Simple heuristic-based extraction
    lines = transcript.split('\n')

    # Look for the question in the transcript
    question_words = question.lower().split()[:5]  # First 5 words of question

    for i, line in enumerate(lines):
        line_lower = line.lower()

        # If this line contains part of the question
        if any(word in line_lower for word in question_words if len(word) > 3):
            # Look for response in next few lines
            for j in range(i + 1, min(i + 5, len(lines))):
                response_line = lines[j].strip()
                if response_line and len(response_line) > 5:
                    # Check if this looks like a response
                    if (response_line.startswith(('Expected Response:', 'CANDIDATE:', 'A:', 'Answer:')) or
                        any(indicator in response_line.lower() for indicator in ['i think', 'i know', 'i don\'t', 'yes', 'no', 'maybe'])):
                        return response_line

    return None


# Core business logic for processing interviews
def process_interview(request: InterviewProcessingRequest):
    """
    This function processes an interview transcript based on the specified request type.

    Args:
        request (InterviewProcessingRequest): The request object containing questions, transcript, and process type.

    Returns:
        The processed result based on the request type.
    """
    if request.process_type == ProcessType.EXTRACT:
        schema = ExtractedAnswers
        task_prompt = (
            "Extract the candidate's actual responses from the interview transcript for each question. "
            "You MUST find and extract responses even if they are brief, incomplete, or indicate lack of knowledge. "
            "\n\n**TRANSCRIPT FORMAT RECOGNITION (try these patterns in order):**\n"
            "1. **'Expected Response:' format** - Text following 'Expected Response:' labels contains the candidate's ACTUAL responses\n"
            "2. **'CANDIDATE:' format** - Direct candidate responses marked with 'CANDIDATE:' prefix\n"
            "3. **Conversational format** - Look for interviewer questions followed by candidate responses\n"
            "4. **Mixed format** - Combination of the above patterns\n"
            "\n**RESPONSE EXTRACTION RULES:**\n"
            "- Extract ANY verbal response from the candidate, including:\n"
            "  * Complete technical explanations\n"
            "  * Brief answers like 'Yes', 'No', 'I think so'\n"
            "  * Admissions of lack of knowledge: 'I don't know', 'I'm not familiar with that'\n"
            "  * Partial responses: 'I have some experience with...'\n"
            "  * Wrong answers (still count as responses)\n"
            "- If a candidate provided ANY verbal response to a question, extract it fully\n"
            "- Only use 'Invalid transcript' for a specific question if:\n"
            "  * The question was never asked in the transcript, OR\n"
            "  * The candidate remained completely silent (no verbal response at all)\n"
            "- Do NOT use 'Invalid transcript' just because the answer is brief, wrong, or shows lack of knowledge\n"
            "\n**CRITICAL SUCCESS CRITERIA:**\n"
            "- If the transcript shows a candidate who answered most questions (even briefly), you should extract responses for most questions\n"
            "- A senior candidate who 'only didn't know one question' should have responses extracted for 11 out of 12 questions\n"
            "- Look beyond perfect answers - extract honest admissions, partial knowledge, and learning-oriented responses\n"
            "- Return responses in the same order as questions appear in the questions list"
        )

        # Enhanced debug logging for answer extraction
        transcript_length = len(request.transcript) if request.transcript else 0
        logger.info(f"EXTRACT DEBUG: transcript length = {transcript_length}")
        logger.info(f"EXTRACT DEBUG: transcript preview = {request.transcript[:200] if request.transcript else 'None'}...")
        logger.info(f"EXTRACT DEBUG: number of questions = {len(request.questions)}")

        # Log transcript format indicators
        if request.transcript:
            expected_response_count = request.transcript.count("Expected Response:")
            candidate_count = request.transcript.count("CANDIDATE:")
            logger.info(f"EXTRACT DEBUG: 'Expected Response:' patterns found = {expected_response_count}")
            logger.info(f"EXTRACT DEBUG: 'CANDIDATE:' patterns found = {candidate_count}")

            # Log first few questions for context
            for i, question in enumerate(request.questions[:3]):
                logger.info(f"EXTRACT DEBUG: Question {i+1}: {question[:100]}...")
        else:
            logger.warning("EXTRACT DEBUG: No transcript provided!")
    else:
        schema = ParaphrasedAnswers
        task_prompt = (
            "Paraphrase the candidate's answers using the full context of the transcript, ensuring that:\n"
            "- The paraphrased answer remains faithful to what was actually said.\n"
            "- If relevant details appear in other parts of the transcript, include a 'complement_from' field.\n"
            "- Do NOT introduce new information or modify qualifications.\n"
            "- Return JSON following the provided schema."
        )

    user_msg = HumanMessage(
        content="Questions:\n"
        + "\n".join(f"{i + 1}. {q}" for i, q in enumerate(request.questions))
        + "\n\nTranscript:\n"
        + request.transcript
    )

    schema_text = get_related_class_definitions(schema)
    result = inference_with_fallback(
        task_prompt=task_prompt,
        model_schema=schema,
        user_messages=[user_msg],
        model_schema_text=schema_text,
        models_order=MODELS_CONFIG["default_models_order"],
    )
    if not result:
        raise RuntimeError("All LLM providers failed")

    # Enhanced debug logging for AI response
    if request.process_type == ProcessType.EXTRACT:
        logger.info(f"EXTRACT DEBUG: AI result type = {type(result)}")
        logger.info(f"EXTRACT DEBUG: AI result = {str(result)[:500]}...")

        # Analyze extraction results
        if hasattr(result, 'answers') and result.answers:
            valid_answers = [ans for ans in result.answers if ans != "Invalid transcript"]
            invalid_count = len([ans for ans in result.answers if ans == "Invalid transcript"])

            logger.info(f"EXTRACT DEBUG: Total answers extracted = {len(result.answers)}")
            logger.info(f"EXTRACT DEBUG: Valid answers = {len(valid_answers)}")
            logger.info(f"EXTRACT DEBUG: Invalid transcript count = {invalid_count}")
            logger.info(f"EXTRACT DEBUG: Extraction success rate = {len(valid_answers)/len(result.answers)*100:.1f}%")

            # Log first few extracted answers for analysis
            for i, answer in enumerate(result.answers[:3]):
                logger.info(f"EXTRACT DEBUG: Answer {i+1}: {answer[:100]}...")

            # Critical warning if all answers are invalid
            if invalid_count == len(result.answers):
                logger.error("EXTRACT CRITICAL: ALL ANSWERS MARKED AS 'Invalid transcript' - This indicates extraction failure!")
                logger.error(f"EXTRACT CRITICAL: Questions count = {len(request.questions)}, Expected responses = {len(request.questions)}")

                # Attempt fallback extraction with alternative approach
                logger.info("EXTRACT FALLBACK: Attempting alternative extraction method...")
                fallback_result = _attempt_fallback_extraction(request)
                if fallback_result:
                    logger.info("EXTRACT FALLBACK: Alternative extraction succeeded!")
                    return fallback_result
                else:
                    logger.error("EXTRACT FALLBACK: Alternative extraction also failed")
        else:
            logger.error("EXTRACT DEBUG: No answers found in result or result is malformed")

    return result


def extract_questions_from_transcript(transcript: str) -> TranscriptQuestions:
    """
    Extract questions from an interview transcript using LLM analysis.

    Args:
        transcript (str): The interview transcript to analyze.

    Returns:
        TranscriptQuestions: The extracted questions with their categories.
    """
    task_prompt = """
        Analyze the interview transcript and extract ONLY the questions that were ACTUALLY ASKED AND ANSWERED during the interview.

        CRITICAL INSTRUCTIONS:
        1. Look for questions that have BOTH a question AND a candidate response
        2. Only include questions where you can find the candidate's actual verbal response in the transcript
        3. Do NOT include questions that appear to be templates or examples but weren't actually asked
        4. Do NOT include questions that were asked but the candidate didn't respond to
        5. Number the questions sequentially starting from 1 based on the order they appear in the interview

        WHAT TO EXTRACT:
        - Question text as it appears in the transcript
        - Only questions that have a corresponding candidate response
        - Category/topic if mentioned (e.g., "Technical Skills", "Soft Skills")

        WHAT TO IGNORE:
        - Template questions that weren't actually asked
        - Questions without candidate responses
        - Rhetorical questions or greetings
        - Questions that appear in lists but weren't part of the actual conversation

        EXAMPLE PATTERNS TO LOOK FOR:
        - Interviewer: "Can you explain your experience with Python?"
          Candidate: "I have 3 years of experience..."
        - Q1: "Describe your approach to data modeling"
          A1: "I typically start by..."

        IMPORTANT: If the transcript contains a list of 15-20 questions but only 3-6 have actual candidate responses,
        extract ONLY the 3-6 questions that have responses. Do not extract template questions.

        TYPICAL INTERVIEW LENGTH: Most interviews have 3-8 questions, not 15-20.

        Return a JSON object with ONLY the questions that were actually asked and answered.
    """

    logger.info(f"QUESTION EXTRACTION DEBUG: Starting extraction from transcript (length: {len(transcript)})")
    logger.info(f"QUESTION EXTRACTION DEBUG: Transcript preview: {transcript[:300]}...")

    user_msg = HumanMessage(content=f"Transcript:\n{transcript}")

    schema_text = get_related_class_definitions(TranscriptQuestions)
    result = inference_with_fallback(
        task_prompt=task_prompt,
        model_schema=TranscriptQuestions,
        user_messages=[user_msg],
        model_schema_text=schema_text,
        models_order=MODELS_CONFIG["default_models_order"],
    )

    if not result:
        logger.error(f"QUESTION EXTRACTION DEBUG: LLM inference failed - returning empty questions")
        # Return empty questions if extraction fails
        return TranscriptQuestions(questions=[])

    logger.info(f"QUESTION EXTRACTION DEBUG: Successfully extracted {len(result.questions)} questions")
    for i, q in enumerate(result.questions, 1):
        logger.info(f"QUESTION EXTRACTION DEBUG: Q{i}: {q.question_text[:100]}...")
        logger.info(f"QUESTION EXTRACTION DEBUG: Category{i}: {q.category if q.category else 'None'}")

    return result


# Helper function to run and persist interview processing
def run_and_persist_interview(interview_id: str, process_type: ProcessType):
    """
    This function runs the interview processing and persists the results in the database.

    Args:
        interview_id (str): The ID of the interview to process.
        process_type (ProcessType): The type of processing to perform.

    Returns:
        The processed result based on the request type.
    """
    with get_cursor() as cur:
        cur.execute(
            """
            SELECT iq.data, i.transcript_tec
            FROM interview_questions iq
            JOIN interviews i ON iq.position_id = i.position_id
            WHERE i.id = %s;
            """,
            (interview_id,)
        )
        row = cur.fetchone()
    if not row:
        # raise HTTPException(status_code=404, detail="Interview or questionnaire not found")
        return None

    questionnaire, transcript = row
    questions = [q["question"] for q in questionnaire["questions"]]

    req = InterviewProcessingRequest(
        questions=questions, transcript=transcript, process_type=process_type
    )
    result = process_interview(req)

    with get_cursor() as cur:
        cur.execute(
            """
            UPDATE interviews
            SET anwers_data = %s,
                updated_at = NOW()
            WHERE id = %s;
            """,
            (Json(result.model_dump()), interview_id)
        )
    return result


def evaluate_interview_with_no_qa(interview_id: str) -> EvaluateInterviewNoQA:
    """
    Evaluate the interview transcript, candidate info and position info.
    Args:
        interview_id (str): The ID of the interview to evaluate.
    Returns:
        EvaluateInterviewNoQA: The evaluation result without question-answer pairs.
    """
    # We should compare transcript, candidate info and position info
    # interviews has position_id, candidate_id, id, transcript_hr
    # For position info we need to retrieve it from positions_smarthr
    # For candidate info we need to retrieve it from candidates_smarthr
    try:
        with get_cursor() as cur:
            cur.execute(
                """
                SELECT p.position_info, c.candidate_info, i.transcript_hr
                FROM interviews i
                JOIN positions_smarthr p ON i.position_id = p.id
                JOIN candidates_smarthr c ON i.candidate_id = c.id
                WHERE i.id = %s;
                """,
                (interview_id,)
            )
            row = cur.fetchone()
            if not row:
                raise HTTPException(status_code=404, detail="Interview not found (evaluate_interview_with_no_qa)")
        position_info, candidate_info, transcript_hr = row

        # """ SCHEMA
        # class EvaluateInterviewNoQA(BaseModel):
        #     overall_seniority: Seniority
        #     percentage_of_match: float
        #     explanation: str
        # """
        task_prompt_no_questions = """
            Evaluate the interview transcript, candidate info and position info.
            Return JSON that matches the provided schema.
            IMPORTANT RULES:
            • Provide an overall_seniority (senior|mid|junior) based on the transcript and candidate info.
            • Look for senior-level indicators: strategic thinking, architectural decisions, best practices, leadership experience, optimization concerns, or deep technical insights
            **• Senior level requires evidence of advanced thinking, not perfection in all areas**
        """
        schema_text = get_related_class_definitions(EvaluateInterviewNoQA)
        result = inference_with_fallback(
            task_prompt=task_prompt_no_questions,
            model_schema=EvaluateInterviewNoQA,
            user_messages=[HumanMessage(content=json.dumps({'position_info': position_info, 'candidate_info': candidate_info, 'transcript': transcript_hr}, ensure_ascii=False))],
            model_schema_text=schema_text,
            models_order=MODELS_CONFIG["default_models_order"],
        )

        if not result:
            raise RuntimeError("LLM evaluation failed")

        with get_cursor() as cur:
            cur.execute(
                """
                UPDATE interviews
                SET interview_data = %s,
                    updated_at = NOW()
                WHERE id = %s;
                """,
                (Json(result.model_dump()), interview_id)
            )
        return result

    except Exception as e:
        # Log the error and raise an HTTPException
        print(f"Error occurred while evaluating interview without QA: {str(e)}")
        logger.error(f"Error occurred while evaluating interview without QA: {str(e)}")
        raise HTTPException(status_code=404, detail=f"Interview not found (except: evaluate_interview_with_no_qa): {str(e)}")


def evaluate_interview_transcript_based(interview_id: str) -> EvaluationResult:
    """
    Evaluate the interview based on questions actually present in the transcript.

    Args:
        interview_id (str): The ID of the interview to evaluate.

    Returns:
        EvaluationResult: The evaluation result based on transcript questions.
    """
    with get_cursor() as cur:
        cur.execute(
            """
            SELECT i.transcript_tec, i.feedback_tec, i.position_id
            FROM interviews i
            WHERE i.id = %s;
            """,
            (interview_id,)
        )
        row = cur.fetchone()

    if not row or not row[0]:
        raise HTTPException(status_code=404, detail="Interview transcript not found")

    transcript_tec, feedback_tec, position_id = row

    if not position_id:
        raise HTTPException(status_code=400, detail="Position ID not found for interview")

    # Extract questions from transcript
    logger.info(f"TRANSCRIPT EVAL DEBUG: Starting question extraction from transcript (length: {len(transcript_tec)})")
    logger.info(f"TRANSCRIPT EVAL DEBUG: Transcript preview: {transcript_tec[:300]}...")

    try:
        transcript_questions = extract_questions_from_transcript(transcript_tec)
        logger.info(f"TRANSCRIPT EVAL DEBUG: Question extraction SUCCESS - found {len(transcript_questions.questions)} questions")
    except Exception as extract_error:
        logger.error(f"TRANSCRIPT EVAL DEBUG: Question extraction FAILED: {str(extract_error)}")
        logger.error(f"TRANSCRIPT EVAL DEBUG: Full traceback: {traceback.format_exc()}")
        raise HTTPException(status_code=400, detail=f"Question extraction failed: {str(extract_error)}")

    # Debug logging
    logger.info(f"TRANSCRIPT EVAL DEBUG: Extracted {len(transcript_questions.questions)} questions from transcript")
    for i, q in enumerate(transcript_questions.questions, 1):
        logger.info(f"TRANSCRIPT EVAL DEBUG: Question {i}: {q.question_text[:100]}...")
        logger.info(f"TRANSCRIPT EVAL DEBUG: Category {i}: {q.category if q.category else 'None'}")

    if not transcript_questions.questions:
        logger.error(f"TRANSCRIPT EVAL DEBUG: No questions found in transcript - this will cause fallback to predefined questions")
        raise HTTPException(status_code=400, detail="No questions found in transcript")

    # Check if we extracted suspiciously many questions (likely means extraction is wrong)
    if len(transcript_questions.questions) >= 15:
        logger.error(f"TRANSCRIPT EVAL DEBUG: Extracted {len(transcript_questions.questions)} questions - this is too many for a typical interview")
        logger.error(f"TRANSCRIPT EVAL DEBUG: This suggests extraction is pulling predefined questions instead of actual interview Q&A")
        logger.error(f"TRANSCRIPT EVAL DEBUG: Falling back to predefined questions approach to avoid incorrect evaluation")
        raise HTTPException(status_code=400, detail=f"Extracted too many questions ({len(transcript_questions.questions)}) - likely extraction error")

    # Check if we extracted too many questions (might indicate extraction is pulling from predefined questions instead of actual transcript)
    if len(transcript_questions.questions) >= 15:
        logger.warning(f"TRANSCRIPT EVAL DEBUG: Extracted {len(transcript_questions.questions)} questions - this seems high for a typical interview")
        logger.warning(f"TRANSCRIPT EVAL DEBUG: This might indicate the extraction is pulling predefined questions instead of actual interview questions")

        # Let's continue but log this as suspicious
        logger.warning(f"TRANSCRIPT EVAL DEBUG: Continuing with transcript-based evaluation but results may be inaccurate")

    # Retrieve expected answers from generated questions
    expected_questions = None
    try:
        expected_questions = fetch_questions_by_position_id(str(position_id))
        logger.info(f"TRANSCRIPT EVAL DEBUG: Retrieved {len(expected_questions.data.get('questions', []))} expected questions for position {position_id}")
    except HTTPException as e:
        if e.status_code == 404:
            logger.warning(f"TRANSCRIPT EVAL DEBUG: No generated questions found for position {position_id}, proceeding without expected answers")
        else:
            logger.error(f"TRANSCRIPT EVAL DEBUG: Error retrieving questions for position {position_id}: {e.detail}")
    except Exception as e:
        logger.error(f"TRANSCRIPT EVAL DEBUG: Unexpected error retrieving questions for position {position_id}: {str(e)}")

    # Match transcript questions with expected questions
    matched_questions = []
    if expected_questions and expected_questions.data and 'questions' in expected_questions.data:
        expected_q_list = expected_questions.data['questions']

        for transcript_q in transcript_questions.questions:
            # Try to find matching expected question by question number first
            matched_expected = None
            for expected_q in expected_q_list:
                if expected_q.get('question_number') == transcript_q.question_number:
                    matched_expected = expected_q
                    break

            # If no match by number, try basic text similarity (first 50 chars)
            if not matched_expected:
                transcript_text_start = transcript_q.question_text[:50].lower().strip()
                for expected_q in expected_q_list:
                    expected_text_start = expected_q.get('question', '')[:50].lower().strip()
                    if transcript_text_start and expected_text_start and transcript_text_start in expected_text_start:
                        matched_expected = expected_q
                        break

            # Add to matched questions with expected answers if found
            question_with_expected = {
                'transcript_question': transcript_q.model_dump(),
                'expected_answers': matched_expected if matched_expected else None
            }
            matched_questions.append(question_with_expected)

            if matched_expected:
                logger.info(f"TRANSCRIPT EVAL DEBUG: Matched transcript question {transcript_q.question_number} with expected question {matched_expected.get('question_number', 'unknown')}")
            else:
                logger.warning(f"TRANSCRIPT EVAL DEBUG: No expected answer found for transcript question {transcript_q.question_number}")

    # Create evaluation prompt for transcript-based assessment
    task_prompt = f"""
        You are evaluating a candidate's responses in a technical job interview based on the actual questions asked in the transcript.

        EVALUATION APPROACH:
        - Your goal is to evaluate the candidate's responses based on the questions they were actually asked
        - You have been provided with {len(transcript_questions.questions)} questions that were identified in the transcript
        - Analyze ONLY these questions and their corresponding candidate responses
        - Calculate percentage_of_match based on questions actually answered vs questions actually asked
        - **CRITICAL**: Evaluate each response independently - one "I don't know" should NOT make other responses invalid
        - remember Expected answers in the transcript could be Candidate answers, look for response patterns including:
          * Text following 'Expected Response:' labels (this IS the candidate's actual response)

        **MANDATORY CLASSIFICATION RULES - FOLLOW EXACTLY:**

        **ADEQUATE (counts toward percentage):**
        - Any response showing knowledge, experience, or understanding
        - Mentions specific technologies, tools, or work experience
        - Shows basic to advanced competency
        - Examples: "I use React", "I have 3 years experience", "I built a system", "I understand the concept"

        **INADEQUATE (does NOT count toward percentage - MUST EXCLUDE FROM CALCULATION):**
        - **"I don't know"** - NEVER counts toward percentage
        - **"I'm not sure"** - NEVER counts toward percentage
        - **"I'm not familiar with that"** - NEVER counts toward percentage
        - **"I don't have experience with that"** - NEVER counts toward percentage
        - **"I don't understand the question"** - NEVER counts toward percentage
        - Completely wrong or nonsensical answers (e.g., "I could have know before", grammatically incorrect or meaningless responses)
        - Responses that don't address the question at all
        - Responses with major grammatical errors that make them incomprehensible
        - No response at all

        **CRITICAL RULE: Evaluate each question independently. One "I don't know" should NOT affect other responses.**

        **MANDATORY TASK INSTRUCTIONS - FOLLOW EXACTLY:**
        1. For each question, find the candidate's response
        2. If response shows ANY knowledge/experience → mark as ADEQUATE and count toward percentage
        3. **If response is "I don't know" (or similar), wrong, or nonsensical → mark as INADEQUATE and EXCLUDE from percentage calculation**
        4. **EXAMPLES of INADEQUATE responses that MUST BE EXCLUDED**: "I don't know", "I'm not sure", "I'm not familiar", "I could have know before", grammatically broken responses, completely off-topic answers
        5. If no response found → mark as UNANSWERED and EXCLUDE from percentage calculation
        6. Rate each ADEQUATE response as junior/mid/senior based on complexity
        7. **CRITICAL: Each question is independent - evaluate separately**

        **MANDATORY CALCULATION RULES - FOLLOW EXACTLY:**
        - **TOTAL QUESTIONS = {len(transcript_questions.questions)} (ALL questions asked in interview)**
        - **ADEQUATE RESPONSES = count of responses showing knowledge/experience**
        - **INADEQUATE RESPONSES = count of "I don't know" and similar responses**
        - **FORMULA: percentage_of_match = (ADEQUATE responses / TOTAL questions) * 100**
        - **DENOMINATOR IS ALWAYS {len(transcript_questions.questions)} - NEVER CHANGE THIS**
        - overall_seniority = most common seniority level among ADEQUATE responses

        **MANDATORY CALCULATION EXAMPLES:**
        - **Scenario 1**: {len(transcript_questions.questions)} questions total
          * 5 adequate responses + 1 "I don't know" = 5/{len(transcript_questions.questions)} = {(5/len(transcript_questions.questions)*100):.1f}% match
        - **Scenario 2**: {len(transcript_questions.questions)} questions total
          * 4 adequate responses + 2 "I don't know" = 4/{len(transcript_questions.questions)} = {(4/len(transcript_questions.questions)*100):.1f}% match
        - **Scenario 3**: {len(transcript_questions.questions)} questions total
          * {len(transcript_questions.questions)} adequate responses + 0 "I don't know" = {len(transcript_questions.questions)}/{len(transcript_questions.questions)} = 100% match

        **CRITICAL RULE: DENOMINATOR IS ALWAYS {len(transcript_questions.questions)} (total questions asked)**
        **NEVER exclude "I don't know" responses from the denominator - they still count as questions asked**

        3. Response quality guidelines (PRIORITIZE RECOGNIZING COMPETENCY):
           - 'senior': Look for ANY of these clear indicators (don't require multiple):
             * Mentions architecture, design patterns, or system design
             * Discusses performance, optimization, scalability, or best practices
             * Shows leadership, mentoring, or team coordination experience
             * Demonstrates complex problem-solving or strategic thinking
             * Explains trade-offs, alternatives, or decision-making rationale
             * Shows deep technical knowledge beyond basic implementation
             * Uses advanced terminology correctly and confidently
           - 'mid': Shows solid practical experience (LOOK FOR THESE INDICATORS):
             * **Years of experience**: "I have X years of experience with...", "I've been working with..."
             * **Hands-on implementation**: "I have built", "I have implemented", "I have developed"
             * **Independent work capability**: "I can work independently", "I have delivered projects"
             * **Tool/technology proficiency**: "I am proficient with", "I have extensive experience using"
             * **Problem-solving examples**: "I solved a problem by...", "I troubleshot an issue with..."
             * **Understanding of best practices**: "I follow best practices", "I ensure quality by..."
           - 'junior': Shows foundational knowledge (BASIC LEVEL):
             * **Learning orientation**: "I am learning", "I have studied", "I understand the basics"
             * **Basic concepts**: "I know what X is", "I understand the concept of"
             * **Limited experience**: "I have some experience", "I have worked on small projects"
             * **Guided work**: "With guidance, I can", "I need help with", "I am still learning"

        4. Response adequacy assessment (separate from seniority level - BE VERY INCLUSIVE):
           - ADEQUATE: Any response that demonstrates understanding at junior, mid, or senior level
           - ADEQUATE: Any response that mentions specific technologies, tools, frameworks, or methodologies
           - ADEQUATE: Any response that describes work experience, projects, or practical application
           - ADEQUATE: Any response that shows the candidate has used, worked with, or learned about the topic
           - ADEQUATE: Responses like "I have experience with...", "I use...", "I work with...", "I know..."
           - INADEQUATE: Responses that show fundamental misunderstanding, completely incorrect information, or no meaningful content
           - NO RESPONSE: Literally no verbal response provided by candidate

        **MANDATORY COUNTING RULES - EVALUATE EACH RESPONSE INDEPENDENTLY:**
        - **TOTAL QUESTIONS TO EVALUATE: {len(transcript_questions.questions)} (FIXED DENOMINATOR)**
        - Each question MUST have a corresponding QuestionEvaluation in the per_question array
        - **CRITICAL**: Evaluate each response separately - do NOT let one response affect others
        - Count ONLY ADEQUATE responses in the NUMERATOR of percentage calculation
        - **"I don't know" responses are INADEQUATE - count as 0 in NUMERATOR but still count as questions in DENOMINATOR**
        - **DO NOT count INADEQUATE responses in NUMERATOR** (responses showing fundamental misunderstanding or no meaningful content)
        - Only mark as NO RESPONSE if there is genuinely NO verbal response from candidate
        - **MANDATORY CALCULATION CHECK**: If ANY question has "I don't know" response, percentage MUST be less than 100%

        **STEP-BY-STEP CALCULATION EXAMPLE:**
        - **Step 1**: Count ADEQUATE responses (numerator) = X
        - **Step 2**: Total questions asked (denominator) = {len(transcript_questions.questions)} (ALWAYS)
        - **Step 3**: percentage_of_match = (X / {len(transcript_questions.questions)}) * 100
        - **Example**: 4 adequate + 2 "I don't know" = (4 / {len(transcript_questions.questions)}) * 100 = {(4/len(transcript_questions.questions)*100):.1f}%

        - The per_question array must contain exactly {len(transcript_questions.questions)} evaluations

        HOW TO USE EXPECTED ANSWERS:
        - In 'matched_questions_with_expected_answers', each question has:
          * 'transcript_question': The question extracted from transcript
          * 'expected_answers': Object with junior_answer, mid_answer, senior_answer (if available)
        - When expected answers are available, use them as quality benchmarks:
          * Compare candidate response to junior_answer, mid_answer, senior_answer
          * Rate based on which level the response most closely matches
          * Consider depth, accuracy, and comprehensiveness relative to expected answers
        - When expected answers are NOT available, use general evaluation criteria

        EXAMPLES FOR CLARITY:
        Question: "Can you explain your experience with React?"
        Expected junior_answer: "I have basic understanding of React components and JSX"
        Expected mid_answer: "I have solid experience with React hooks, state management, and building SPAs"
        Expected senior_answer: "I architect React applications with performance optimization, custom hooks, and strategic state management decisions"

        - Response: "I don't know React" → INADEQUATE, does NOT count toward percentage, detected_seniority: 'junior'
        - Response: "I understand React components and JSX basics" → ADEQUATE (matches junior_answer level), detected_seniority: 'junior'
        - Response: "I have 2 years with React, built SPAs, understand hooks and state management" → ADEQUATE (matches mid_answer level), detected_seniority: 'mid'
        - Response: "I've architected React applications considering performance optimization, implemented custom hooks for reusability" → ADEQUATE (matches senior_answer level), detected_seniority: 'senior'
        - Response: "I use React in my projects" → ADEQUATE (shows practical experience), detected_seniority: 'junior' or 'mid'
        - Response: "I work with React daily" → ADEQUATE (shows ongoing experience), detected_seniority: 'mid'
        - Response: "React is a database system for storing data" → INADEQUATE (fundamental misunderstanding), does not count toward percentage
        - No response in transcript → NO RESPONSE, does not count toward percentage

        **MANDATORY EXAMPLE - MIXED RESPONSES (STRICT CALCULATION REQUIRED):**
        Scenario: {len(transcript_questions.questions)} questions total (FIXED DENOMINATOR)
        - Q1: "I have 3 years experience with Python" → ADEQUATE (counts in numerator)
        - Q2: "I don't know" → INADEQUATE (does NOT count in numerator, but IS counted in denominator)
        - Q3: "I've built REST APIs" → ADEQUATE (counts in numerator)
        - Q4: "I work with Docker containers" → ADEQUATE (counts in numerator)
        - Q5: "I have experience with CI/CD" → ADEQUATE (counts in numerator)
        - Q6: "I'm not familiar with Kubernetes" → INADEQUATE (does NOT count in numerator, but IS counted in denominator)

        **STEP-BY-STEP CALCULATION:**
        - ADEQUATE responses (numerator): 4 (Q1, Q3, Q4, Q5)
        - TOTAL questions (denominator): {len(transcript_questions.questions)} (ALL questions asked)
        - **CORRECT CALCULATION**: 4/{len(transcript_questions.questions)} * 100 = {(4/len(transcript_questions.questions)*100):.1f}%
        - **FORBIDDEN CALCULATION**: 4/4 * 100 = 100% (WRONG - excludes "I don't know" from denominator)

        **VERIFICATION RULES:**
        - If you calculate 100% but there are any "I don't know" responses, you have made an ERROR
        - The denominator must ALWAYS be {len(transcript_questions.questions)} (total questions asked)
        - "I don't know" responses reduce the numerator but NOT the denominator

        SENIOR-LEVEL RECOGNITION (classify as senior if ANY of these are present):
        - Architecture, design patterns, system design
        - Performance, optimization, scalability, best practices
        - Leadership, mentoring, team coordination
        - Complex problem-solving, strategic thinking
        - Trade-offs, alternatives, decision-making rationale
        - Advanced technical knowledge beyond basics

        CRITICAL CLASSIFICATION RULES:
        - ACTIVELY LOOK FOR senior-level indicators - don't be overly conservative
        - If response shows advanced competency, classify as senior immediately
        - Don't require perfection - look for evidence of expertise
        - **BE VERY INCLUSIVE**: If a response mentions technologies, tools, experience, or shows any understanding, count it as ADEQUATE
        - **IMPORTANT**: Responses that describe work experience, mention specific technologies, or show practical knowledge should be counted toward percentage
        - Base evaluation ONLY on the {len(transcript_questions.questions)} questions provided
        - The per_question array length MUST equal {len(transcript_questions.questions)}

        **FINAL SUMMARY AND EXPLANATION REQUIREMENTS:**
        - Your explanation MUST accurately reflect the calculation: "X adequate responses out of {len(transcript_questions.questions)} questions = Y%"
        - If there are "I don't know" responses, your explanation MUST mention them and show they reduced the percentage
        - Example explanation: "The candidate provided adequate responses to 4 out of {len(transcript_questions.questions)} questions, with 2 'I don't know' responses, resulting in {(4/len(transcript_questions.questions)*100):.1f}% match"
        - NEVER write explanations that suggest 100% match when there are "I don't know" responses

        - Provide output as valid JSON matching the schema
        """

    # Prepare evaluation context
    evaluation_context = {
        'transcript': transcript_tec,
        'questions_identified': [q.model_dump() for q in transcript_questions.questions],
        'feedback_comments': feedback_tec if feedback_tec else {},
        'matched_questions_with_expected_answers': matched_questions
    }

    # DEBUG: Log the evaluation context
    logger.info(f"TRANSCRIPT EVAL DEBUG: Transcript length: {len(transcript_tec) if transcript_tec else 0}")
    logger.info(f"TRANSCRIPT EVAL DEBUG: Transcript preview: {transcript_tec[:300] if transcript_tec else 'None'}...")
    logger.info(f"TRANSCRIPT EVAL DEBUG: Questions identified: {len(transcript_questions.questions)}")
    for i, q in enumerate(transcript_questions.questions):
        logger.info(f"TRANSCRIPT EVAL DEBUG: Q{i+1}: {q.question_text[:100]}... | Response: {q.candidate_response[:100] if q.candidate_response else 'None'}...")
    logger.info(f"TRANSCRIPT EVAL DEBUG: Matched questions with expected answers: {len(matched_questions)}")

    schema_text = get_related_class_definitions(EvaluationResult)
    result = inference_with_fallback(
        task_prompt=task_prompt,
        model_schema=EvaluationResult,
        user_messages=[HumanMessage(content=json.dumps(evaluation_context, ensure_ascii=False))],
        model_schema_text=schema_text,
        models_order=MODELS_CONFIG["default_models_order"],
    )

    if not result:
        raise RuntimeError("LLM evaluation failed")

    # Validate evaluation results
    validation_issues = _validate_evaluation_result(result, len(transcript_questions.questions), "transcript-based")
    if validation_issues:
        logger.warning(f"TRANSCRIPT EVAL VALIDATION: Issues detected: {validation_issues}")
        # Log issues but don't fail - let the result through with warnings

    # Debug logging for evaluation results
    logger.info(f"TRANSCRIPT EVAL DEBUG: Evaluation completed")
    logger.info(f"TRANSCRIPT EVAL DEBUG: Overall seniority: {result.overall_seniority}")
    logger.info(f"TRANSCRIPT EVAL DEBUG: Percentage of match: {result.percentage_of_match}")
    logger.info(f"TRANSCRIPT EVAL DEBUG: Number of per_question evaluations: {len(result.per_question)}")
    logger.info(f"TRANSCRIPT EVAL DEBUG: Expected questions from transcript: {len(transcript_questions.questions)}")
    logger.info(f"TRANSCRIPT EVAL DEBUG: Actual per_question results: {len(result.per_question)}")

    # Check if there's a mismatch
    if len(result.per_question) != len(transcript_questions.questions):
        logger.error(f"TRANSCRIPT EVAL DEBUG: MISMATCH! Expected {len(transcript_questions.questions)} evaluations but got {len(result.per_question)}")
        logger.error(f"TRANSCRIPT EVAL DEBUG: This suggests the LLM evaluated against wrong question set")

    # Log the explanation to see what the LLM is saying
    logger.info(f"TRANSCRIPT EVAL DEBUG: Explanation: {result.explanation[:200]}...")

    return result


# Evaluate the interview by comparing candidate answers with expected answers
def re_evaluate_interview(interview_id: str) -> Union[EvaluationResult, EvaluateInterviewNoQA]:
    """
    Evaluate the interview using transcript-based analysis for more accurate assessment.

    Args:
        interview_id (str): The ID of the interview to evaluate.

    Returns:
        Union[EvaluationResult, EvaluateInterviewNoQA]: The evaluation result.
        Returns EvaluationResult when transcript is available, EvaluateInterviewNoQA otherwise.
    """
    # First, try transcript-based evaluation for more accurate results
    try:
        with get_cursor() as cur:
            cur.execute(
                """
                SELECT i.transcript_tec
                FROM interviews i
                WHERE i.id = %s;
                """,
                (interview_id,)
            )
            transcript_row = cur.fetchone()

        # If we have a transcript, use transcript-based evaluation
        if transcript_row and transcript_row[0] and transcript_row[0].strip():
            logger.info(f"RE_EVALUATE: Using transcript-based evaluation for interview {interview_id}")
            logger.info(f"RE_EVALUATE: Transcript length: {len(transcript_row[0])}")
            logger.info(f"RE_EVALUATE: Transcript preview: {transcript_row[0][:200]}...")
            try:
                result = evaluate_interview_transcript_based(interview_id)
                logger.info(f"RE_EVALUATE: Transcript-based evaluation SUCCESS - {result.percentage_of_match}% match with {len(result.per_question)} questions")

                # Persist the result
                with get_cursor() as cur:
                    cur.execute(
                        """
                        UPDATE interviews
                        SET interview_data = %s,
                            updated_at = NOW()
                        WHERE id = %s;
                        """,
                        (Json(result.model_dump()), interview_id)
                    )
                return result
            except Exception as eval_error:
                logger.error(f"RE_EVALUATE: Transcript-based evaluation FAILED for interview {interview_id}: {str(eval_error)}")
                logger.error(f"RE_EVALUATE: Full traceback: {traceback.format_exc()}")
                # Don't re-raise - let it fall back to predefined questions approach
                logger.warning(f"RE_EVALUATE: Falling back to predefined questions approach")

    except Exception as e:
        logger.error(f"Database error in transcript evaluation for interview {interview_id}: {str(e)}")
        logger.error(f"Full traceback: {traceback.format_exc()}")
        # Continue to fallback approaches

    # Fallback 1: Try predefined questions approach
    with get_cursor() as cur:
        cur.execute(
            """
            SELECT iq.data, i.anwers_data, i.feedback_tec, i.transcript_tec
            FROM interview_questions iq
            JOIN interviews i ON iq.position_id = i.position_id
            WHERE i.id = %s;
            """,
            (interview_id,)
        )
        row = cur.fetchone()

    if not row:
        logger.info(f"No predefined questions found, using no-QA evaluation for interview {interview_id}")
        return evaluate_interview_with_no_qa(interview_id)

    expected_data, actual_answers, feedback_tec, transcript_tec = row

    # If expected questions are missing or empty, fallback to evaluation without QA
    if expected_data is None or not expected_data.get('questions'):
        logger.info(f"Empty predefined questions, using no-QA evaluation for interview {interview_id}")
        return evaluate_interview_with_no_qa(interview_id)

    # Fallback 2: Use predefined questions approach (legacy method)
    logger.warning(f"RE_EVALUATE: Using predefined questions evaluation for interview {interview_id}")
    logger.warning(f"RE_EVALUATE: This will evaluate against ALL {len(expected_data.get('questions', []))} predefined questions, not just transcript questions")
    logger.warning(f"RE_EVALUATE: Expected questions count: {len(expected_data.get('questions', []))}")
    logger.warning(f"RE_EVALUATE: This may result in lower percentage if not all questions were asked in interview")

    task_prompt = f"""
        You are evaluating a candidate's responses in a technical job interview.

        **CRITICAL INSTRUCTION**: Only evaluate questions that were ACTUALLY ASKED in the interview.
        - You have {len(expected_data.get('questions', []))} predefined questions available
        - But the interviewer may have only asked a subset of these questions (typically 3-8 questions)
        - **ONLY evaluate questions where you can find evidence in the transcript that they were asked AND answered**
        - **DO NOT evaluate questions that appear to be unanswered or not asked**

        DATA SOURCES:
        - **CRITICAL**: If all actual answers are "Invalid transcript," this indicates an EXTRACTION FAILURE, not that the candidate didn't respond
        - When extraction fails, use the transcript_excerpt as your PRIMARY source to find responses
        - Look directly in the transcript for candidate responses, even if extraction failed
        - Use valid actual answers as the primary source when available
        - **EXTRACTION FAILURE RECOVERY**: If you see "Invalid transcript" for most/all answers but the transcript_excerpt contains responses, extract and evaluate those responses directly

        **MANDATORY CLASSIFICATION RULES - FOLLOW EXACTLY:**

        **ADEQUATE (counts toward percentage):**
        - Any response showing knowledge, experience, or understanding
        - Mentions specific technologies, tools, or work experience
        - Shows basic to advanced competency
        - Examples: "I use React", "I have 3 years experience", "I built a system", "I understand the concept"

        **INADEQUATE (does NOT count toward percentage - MUST EXCLUDE FROM CALCULATION):**
        - **"I don't know"** - NEVER counts toward percentage
        - **"I'm not sure"** - NEVER counts toward percentage
        - **"I'm not familiar with that"** - NEVER counts toward percentage
        - **"I don't have experience with that"** - NEVER counts toward percentage
        - Completely wrong or nonsensical answers (e.g., "I could have know before", grammatically incorrect or meaningless responses)
        - Responses that don't address the question at all
        - Responses with major grammatical errors that make them incomprehensible
        - No response at all

        **CRITICAL RULE: Evaluate each question independently. One "I don't know" should NOT affect other responses.**
        **MANDATORY RULE: If ANY question has "I don't know", "No idea" or simillar response, percentage MUST be less than 100%**

        ✗ UNANSWERED (does not count toward percentage):
        - Complete silence, no verbal response at all
        - Question was asked but candidate never responded
        - Interviewer moved on without getting any response

        **MANDATORY TASK INSTRUCTIONS - FOLLOW EXACTLY:**
        1. **FIRST**: Identify which questions were actually asked by looking at the transcript
        2. **ONLY evaluate questions that were actually asked** - ignore questions with no evidence of being asked
        3. For each question that was actually asked, find the candidate's response
        4. If response shows ANY knowledge/experience → mark as ADEQUATE and count toward percentage
        5. **If response is "I don't know", wrong, or nonsensical → mark as INADEQUATE and EXCLUDE from NUMERATOR (but keep in denominator)**
        6. **EXAMPLES of INADEQUATE responses that MUST BE EXCLUDED from numerator**: "I don't know", "I'm not sure", "I'm not familiar", "I could have know before", grammatically broken responses, completely off-topic answers
        7. If question was asked but no response found → mark as UNANSWERED and EXCLUDE from NUMERATOR (but keep in denominator)
        8. Rate each ADEQUATE response as junior/mid/senior based on complexity
        9. **CRITICAL: Only include questions that were actually asked in your per_question array**
        10. **CRITICAL: Calculate percentage based only on questions that were actually asked**
        11. **IMPORTANT: If you find evidence of only 4 questions being asked, your per_question array should have exactly 4 entries, NOT 20**

        **MANDATORY CALCULATION RULES:**
        - **TOTAL QUESTIONS ASKED = N (this is your FIXED DENOMINATOR)**
        - **ADEQUATE RESPONSES = count of responses showing knowledge (this is your NUMERATOR)**
        - **FORMULA: percentage_of_match = (ADEQUATE responses / TOTAL questions asked) * 100**
        - **CRITICAL: "I don't know" responses reduce NUMERATOR but NOT DENOMINATOR**
        - **VERIFICATION CHECK: If you calculate 100% but there are any "I don't know" responses, you have made an ERROR**
             * 'mid': Shows solid practical experience and independent work capability (LOOK FOR: years of experience, hands-on implementation, project delivery, tool proficiency)
             * 'junior': Shows foundational understanding and learning mindset (LOOK FOR: basic concepts, learning orientation, limited experience, need for guidance)
           - For INADEQUATE responses: Note as inadequate but still assign the most appropriate seniority level if any competency is shown
           - Provide clear explanation focusing on what the candidate demonstrated, not what they lacked
        3. For UNANSWERED questions:
           - Mark as unanswered and assign detected_seniority based on overall candidate profile, not automatically as 'junior'

        OVERALL ASSESSMENT - MANDATORY THRESHOLD-BASED CLASSIFICATION:
        - STEP 1: Count each adequate response by its detected_seniority level (senior_count, mid_count, junior_count)
        - STEP 2: Calculate percentages of each level from total adequate responses
        - STEP 3: Apply this EXACT threshold logic (MANDATORY - NO EXCEPTIONS):
          ** If 80%+ responses are senior → SET overall_seniority = "senior"**
          ** If 80%+ responses are mid → SET overall_seniority = "mid"**
          ** If 80%+ responses are junior → SET overall_seniority = "junior"**
          ** If no single level reaches 80%, use highest count (senior > mid > junior for ties)**
        - EXAMPLE: 17 senior + 3 junior = 85% senior → MUST set overall_seniority = "senior"
        - CRITICAL: Ignore traditional "averaging" or "predominant level" - use ONLY this threshold method
        - If all responses are inadequate, set overall_seniority to 'n/a' and explain: "Insufficient adequate responses to determine seniority level."
        - Candidate background is secondary - threshold calculation is PRIMARY

        CALCULATION:
        - percentage_of_match = (number of ADEQUATE responses / number of questions ACTUALLY ASKED) * 100
        - overall_seniority = most common seniority level among ADEQUATE responses
        - **EXAMPLE 1**: 5 adequate responses + 1 "I don't know" out of 6 questions asked = 83% match (5/6), NOT 0%
        - **EXAMPLE 2**: If 20 questions exist but only 4 were asked, and candidate answered 3 adequately + 1 "I don't know" = 75% match (3/4), NOT 15% (3/20)

        **CRITICAL OUTPUT REQUIREMENT:**
        - Your per_question array length MUST equal the number of questions actually asked
        - If only 4 questions were asked, return exactly 4 QuestionEvaluation objects
        - Do NOT create evaluations for questions that weren't asked

        EXPLANATION REQUIREMENTS - POSITIVE FOCUS:
        - HIGHLIGHT what the candidate demonstrated, not what they lacked
        - For senior: Emphasize expertise, strategic thinking, architecture, optimization, leadership
        - For mid: Emphasize practical experience, problem-solving, independence
        - For junior: Emphasize foundational knowledge, learning attitude, growth potential
        - Use positive language - avoid words like "lacking", "poor", "inadequate"

        EXAMPLES FOR CLARITY:
        Question: "Describe your experience with microservices"
        - Response: "I don't know about microservices but I understand they're used for scalable applications" → ADEQUATE (honest + basic understanding), detected_seniority: 'junior'
        - Response: "I haven't worked with them yet, but I'm interested in learning" → ADEQUATE (honest acknowledgment + learning mindset), detected_seniority: 'junior'
        - Response: "Microservices are just big databases" → INADEQUATE (fundamental misunderstanding), does not count toward percentage
        - Response: "I've built microservices using Docker and handled API communication between services" → ADEQUATE, detected_seniority: 'mid'
        - Response: "I've designed microservice architectures considering service boundaries, data consistency patterns, and implemented circuit breakers for resilience" → ADEQUATE, detected_seniority: 'senior'
        - Response: "I've led the migration from monolith to microservices, considering team structure and deployment strategies" → ADEQUATE, detected_seniority: 'senior'
        - No response found in transcript/answers → NO RESPONSE, does not count toward percentage

        **CRITICAL EXAMPLE - MIXED RESPONSES (DO NOT LET "I DON'T KNOW" CONTAMINATE OTHER RESPONSES):**
        Scenario: 6 questions total
        - Q1: "I have 3 years experience with Python" → ADEQUATE, counts toward percentage
        - Q2: "I use SQL daily in my work" → ADEQUATE, counts toward percentage
        - Q3: "I've built REST APIs" → ADEQUATE, counts toward percentage
        - Q4: "I work with Docker containers" → ADEQUATE, counts toward percentage
        - Q5: "I have experience with CI/CD" → ADEQUATE, counts toward percentage
        - **Q6: "I don't know Kubernetes" → INADEQUATE, does NOT count toward percentage**

        **CORRECT CALCULATION**: 5 adequate responses out of 6 total = 83.3% (5/6 * 100)
        **WRONG CALCULATION**: 0% (this would be incorrect contamination)

        REMEMBER: If you see architecture, optimization, leadership, or advanced technical knowledge - classify as SENIOR immediately. Don't be conservative.

        ADDITIONAL REQUIREMENTS:
        - Incorporate percentage_of_match into the overall_explanation
        - Provide output as a valid JSON object only (no additional text)
        - Maintain consistent labeling: 'senior', 'mid', 'junior', or 'n/a' when no valid data

        Format explanations clearly for each question and the overall evaluation.
        """
    schema_text = get_related_class_definitions(EvaluationResult)

    # Prepare comprehensive context for LLM
    evaluation_context = {
        'expected': expected_data,
        'actual': actual_answers
    }

    # DEBUG: Log the actual data being evaluated
    logger.info(f"EVALUATION DEBUG: Expected data: {expected_data}")
    logger.info(f"EVALUATION DEBUG: Actual answers: {actual_answers}")
    if hasattr(actual_answers, 'answers') and actual_answers.answers:
        logger.info(f"EVALUATION DEBUG: Number of actual answers: {len(actual_answers.answers)}")
        for i, answer in enumerate(actual_answers.answers):
            logger.info(f"EVALUATION DEBUG: Answer {i+1}: '{answer[:100]}...' (length: {len(answer)})")
    else:
        logger.warning(f"EVALUATION DEBUG: No answers found in actual_answers: {type(actual_answers)}")

    # Add feedback comments if available
    if feedback_tec:
        evaluation_context['feedback_comments'] = feedback_tec

    # Add transcript excerpt if available (truncate if too long)
    if transcript_tec:
        transcript_excerpt = transcript_tec[:1000] + "..." if len(transcript_tec) > 1000 else transcript_tec
        evaluation_context['transcript_excerpt'] = transcript_excerpt

    result = inference_with_fallback(
        task_prompt=task_prompt,
        model_schema=EvaluationResult,
        user_messages=[HumanMessage(content=json.dumps(evaluation_context, ensure_ascii=False))],
        model_schema_text=schema_text,
        models_order=MODELS_CONFIG["default_models_order"],
    )
    if not result:
        raise RuntimeError("LLM evaluation failed")

    # Debug logging for predefined questions evaluation results
    expected_questions_count = len(expected_data.get('questions', [])) if expected_data else 0
    logger.warning(f"PREDEFINED EVAL DEBUG: Evaluation completed")
    logger.warning(f"PREDEFINED EVAL DEBUG: Overall seniority: {result.overall_seniority}")
    logger.warning(f"PREDEFINED EVAL DEBUG: Percentage of match: {result.percentage_of_match}")
    logger.warning(f"PREDEFINED EVAL DEBUG: Number of per_question evaluations: {len(result.per_question)}")
    logger.warning(f"PREDEFINED EVAL DEBUG: Expected questions count: {expected_questions_count}")
    logger.warning(f"PREDEFINED EVAL DEBUG: Explanation: {result.explanation[:200]}...")

    # Validate evaluation results
    validation_issues = _validate_evaluation_result(result, expected_questions_count, "predefined-questions")
    if validation_issues:
        logger.warning(f"PREDEFINED EVAL VALIDATION: Issues detected: {validation_issues}")

        # Check if validation detected extraction failure
        critical_issues = [issue for issue in validation_issues if "CRITICAL" in issue]
        if critical_issues:
            logger.error(f"PREDEFINED EVAL RETRY: Critical validation issues detected, attempting re-extraction...")

            # Attempt to re-extract answers with fallback method
            try:
                questions = [q["question"] for q in expected_data["questions"]]
                retry_request = InterviewProcessingRequest(
                    questions=questions,
                    transcript=transcript_tec if transcript_tec else "",
                    process_type=ProcessType.EXTRACT
                )

                # Try fallback extraction directly
                fallback_result = _attempt_fallback_extraction(retry_request)
                if fallback_result:
                    logger.info("PREDEFINED EVAL RETRY: Fallback extraction succeeded, re-evaluating...")

                    # Update evaluation context with new extracted answers
                    evaluation_context['actual'] = fallback_result.model_dump()

                    # Re-run evaluation with new extracted answers
                    retry_result = inference_with_fallback(
                        task_prompt=task_prompt,
                        model_schema=EvaluationResult,
                        user_messages=[HumanMessage(content=json.dumps(evaluation_context, ensure_ascii=False))],
                        model_schema_text=schema_text,
                        models_order=MODELS_CONFIG["default_models_order"],
                    )

                    if retry_result:
                        logger.info("PREDEFINED EVAL RETRY: Re-evaluation succeeded!")
                        result = retry_result
                    else:
                        logger.error("PREDEFINED EVAL RETRY: Re-evaluation failed")
                else:
                    logger.error("PREDEFINED EVAL RETRY: Fallback extraction failed")

            except Exception as retry_error:
                logger.error(f"PREDEFINED EVAL RETRY: Retry attempt failed: {str(retry_error)}")

        # Log issues but don't fail - let the result through with warnings

    with get_cursor() as cur:
        cur.execute(
            """
            UPDATE interviews
            SET interview_data = %s,
                updated_at = NOW()
            WHERE id = %s;
            """,
            (Json(result.model_dump()), interview_id)
        )
    return result


# Generate and persist interview questions
# This function generates interview questions based on the position ID and persists them in the database.
def generate_and_persist_qa(position_id: str, n_questions: int, include: str, current_user: str) -> QA_model:
    """
    This function generates interview questions based on the position ID and persists them in the database.

    Args:
        position_id (str): The ID of the position for which to generate questions.
        n_questions (int): The number of questions to generate.
        include (str): A comma-separated string of topics to include in the questions.
        current_user (str): The current user generating the questions.

    Returns:
        QA_model: The generated questions and related data.
    """
    # 1) Fetch position
    position = get_position_by_id(position_id)
    if not position:
        raise HTTPException(status_code=404, detail="Position not found")

    # Check if questions already exist for this position
    with get_cursor() as cur:
        cur.execute(
            """
            SELECT data, allow_regeneration FROM interview_questions WHERE position_id = %s;
            """,
            (position_id,)
        )
        result = cur.fetchone()  # fetchone can return None
        allow_regeneration = True if not result or result[1] is None else result[1]
        if not allow_regeneration:
            # If questions exist and regeneration is not allowed, raise an error
            raise HTTPException(
                status_code=400,
                detail=f"Interview questions already exist for position {position_id} and regeneration is not allowed"
            )

    # Adjust the field names if your JSON differs.
    info: dict = position.position_info or {}
    full_description = json.dumps(info, ensure_ascii=False)

    topics_text = get_topics(include)
    # print("topics_text", topics_text)
    task_prompt = f"""
        You are tasked with creating a structured interview questionnaire designed to evaluate **technical and methodological skills** while clearly differentiating levels of seniority among candidates for a specific role.

        Role Description:
        {full_description}

        **Please generate exactly {n_questions} questions based on the following topics: {topics_text}. For each question, ensure the output includes:**

        1. A sequential question_number ranging from 1 to {n_questions}.
        2. A single tag indicating the specific topic addressed, selected exclusively from: {topics_text}.
        3. Three distinct answers that reflect different seniority levels:
        - junior_answer
        - mid_answer
        - senior_answer

        **Guidelines for Answer Construction (Chain of Thought per level):**

        - senior_answer: Highlight advanced concepts, frameworks, and strategies. Emphasize decision-making, scalability, efficiency, and alignment with business value. Conclude with measurable outcomes or impact on organizational objectives.  
        - mid_answer: Describe practical execution, tools, and methodologies in detail. Show structured problem-solving and collaboration. Conclude with how these practices improve workflows or contribute to project/team success.  
        - junior_answer: Cover foundational concepts, learning in practice, and hands-on skills. Emphasize adaptability, eagerness to learn, and contribution to immediate team objectives.  

        **Formatting Rules:**
        - Deliver the output strictly in JSON format with valid syntax.  
        - Each topic from {topics_text} must appear in at least one question.  
        - Each question must have exactly one tag.  
        - Do not combine tags (e.g., "SOFT SKILLS METHODOLOGIES" is prohibited).  
        - Ensure clear differentiation between junior, mid, and senior answers — avoid repetition or generic filler.  
        - Avoid referencing seniority explicitly (e.g., "As a junior…" or "With X years of experience").  
        - Keep answers professional, substantive, and business-relevant.

        **Example (Agile Methodologies — Sprint Planning):**  
        - Junior: Basic understanding of Agile/Scrum, learning task organization, showing how participation supports team collaboration.  
        - Mid: Refining backlog, coordinating with stakeholders, ensuring adaptability and efficiency in delivery.  
        - Senior: Driving strategic alignment, leading planning sessions, ensuring measurable improvements in delivery and business outcomes.  
        """
    
    schema_text = get_related_class_definitions(QA_model)

    qa = inference_with_fallback(
        task_prompt=task_prompt,
        model_schema=QA_model,
        user_messages=[HumanMessage(content="")],  # no extra user message needed
        model_schema_text=schema_text,
        models_order=MODELS_CONFIG["default_models_order"],
    )
    if not qa:
        raise RuntimeError("LLM failed to generate questionnaire")

    # 3) Persist
    with get_cursor() as cur:
        cur.execute(
            """
            INSERT INTO interview_questions 
                (position_id, data, created_by, created_at, updated_by, updated_at) 
            VALUES 
                (%s, %s, %s, NOW(), %s, NOW())
            ON CONFLICT (position_id) DO UPDATE
            SET 
                data = EXCLUDED.data,
                updated_by = EXCLUDED.updated_by,
                updated_at = NOW();
            """,
            (position_id, Json(qa.model_dump()), current_user, current_user)
        )
    return qa


# Create interviews for the given position and candidates
# This function creates interviews for the given position and candidates.
def create_interviews_for_position(position_id, analysis_data: list[InterviewCreate]) -> List[Interview]:
    """
    Create interviews for the given position and candidates.

    Args:
        position_id (str): The ID of the position for which to create interviews.
        analysis_data (list[InterviewCreate]): List of interview data for candidates.

    Returns:
        List[Interview]: List of created interview objects.
    """
    try:
        for data in analysis_data:
            candidate_id = data.candidate_id

            if not candidate_id:
                continue
            # Validate if candidate_id is a valid UUID
            if not isinstance(candidate_id, str) or len(candidate_id) != 36:
                continue
            # validate if candidate_id exists in candidates_smarthr table
            exist = get_candidate_by_id(candidate_id)
            if not exist:
                continue
            # Check if interview already exists for this candidate and position
            exist = fetch_interview_by_position_id_candidate_id(position_id, candidate_id)
            if exist:
                continue
            # Insert new interview
            with get_cursor() as cur:
                cur.execute(
                    """
                    INSERT INTO interviews 
                    (position_id, candidate_id, analysis_data, status_hr, status_tec, created_at, updated_at)
                    VALUES (%s, %s, %s, 'not_scheduled', 'not_scheduled', NOW(), NOW())
                    RETURNING id, position_id, candidate_id
                    """,
                    (
                        position_id,
                        candidate_id,
                        Json(data.analysis_data if data.analysis_data else {}),
                    ),
                )

        return fetch_all_interviews_by_position_id(position_id)
    except psycopg2.Error as e:
        print(f"Database error occurred while creating interview: {str(e)}")
        logger.error(f"Database error occurred while creating interview: {str(e)}")
        raise HTTPException(status_code=500, detail=f"create_interview.Database error occurred: {str(e)}")
    except HTTPException as e:
        print(f"HTTPException occurred while creating interview: {str(e.detail)}")
        logger.error(f"HTTPException occurred while creating interview: {str(e.detail)}")
        raise e
    except Exception as e:
        print(f"Error occurred while creating interview: {str(e)}")
        raise HTTPException(status_code=500, detail=str(e))


# Get all interviews by position ID
# This function fetches all interviews for a given position ID.
def fetch_all_interviews_by_position_id(position_id: str) -> List[Interview]:
    """
    Fetch all interviews for a given position ID.

    Args:
        position_id (str): The ID of the position for which to fetch interviews.

    Returns:
        List[Interview]: List of interview objects.
    """
    try:
        with get_cursor() as cur:
            cur.execute(
                """
                SELECT i.id, i.position_id, i.candidate_id, i.feedback_hr, i.interview_date_hr, i.feedback_date_hr, i.status_hr,
                i.recommendation_hr, i.transcript_hr, i.feedback_tec, i.interview_date_tec, i.feedback_date_tec, i.status_tec, i.recommendation_tec,
                i.transcript_tec, i.anwers_data, i.interview_data, i.created_at, i.updated_at, c.candidate_info,
                i.recruiter_hr_id, i.scheduled_hr_id, i.recruiter_tec_id, i.scheduled_tec_id, i.analysis_data
                FROM interviews i 
                JOIN candidates_smarthr c ON i.candidate_id = c.id
                WHERE i.position_id::text=%s and c.is_deleted = false
                ORDER BY created_at DESC
                """,
                (position_id,),
            )
            rows = cur.fetchall()
        interviews = []
        for row in rows:
            position = get_position_by_id(str(row[1]))
            interviews.append(
                Interview(
                    id=str(row[0]),
                    position_id=str(row[1]),
                    candidate_id=str(row[2]),
                    candidate_info=row[19].get('personal_info', None),
                    position_info=position.position_info if position is not None else None,
                    feedback_hr=row[3],
                    interview_date_hr=row[4],
                    feedback_date_hr=row[5],
                    status_hr=row[6],
                    recommendation_hr=row[7],
                    transcript_hr=row[8],
                    feedback_tec=row[9],
                    interview_date_tec=row[10],
                    feedback_date_tec=row[11],
                    status_tec=row[12],
                    recommendation_tec=row[13],
                    transcript_tec=row[14],
                    anwers_data=row[15],
                    interview_data=row[16],
                    created_at=row[17],
                    updated_at=row[18],
                    recruiter_hr_id=row[20],
                    scheduled_hr_id=row[21],
                    recruiter_tec_id=row[22],
                    scheduled_tec_id=row[23],
                    analysis_data=row[24]
                )
            )
        return interviews
    except psycopg2.Error as e:
        print(f"Database error occurred while fetching interviews: {str(e)}")
        logger.error(f"Database error occurred while fetching interviews: {str(e)}")
        raise HTTPException(status_code=500, detail=f"fetch_all_interviews_by_position_id. Database error occurred: {str(e)}")
    except HTTPException as e:
        print(f"HTTPException occurred while fetching interviews: {str(e.detail)}")
        logger.error(f"HTTPException occurred while fetching interviews: {str(e.detail)}")
        raise e
    except Exception as e:
        print(f"Error occurred while fetching interviews: {str(e)}")
        logger.error(f"Error occurred while fetching interviews: {str(e)}")
        raise HTTPException(status_code=500, detail=str(e))


# Update interview feedback for HR
# This function updates the interview feedback for HR.
def update_interview_hr(interviewData: InterviewHr) -> Interview:
    """
    Update the interview feedback for HR.

    Args:
        interviewData (InterviewHr): The interview data to update.

    Returns:
        Interview: The updated interview object.
    """
    feedback = fetch_interview_by_position_id_candidate_id(interviewData.position_id, interviewData.candidate_id)
    if not feedback:
        raise HTTPException(status_code=404, detail="Interview not found")

    if feedback.status_hr == StatusInterview.COMPLETED.value or feedback.status_hr == StatusInterview.CANCELLED.value:
        raise HTTPException(status_code=400, detail="Interview already completed or cancelled")

    sqlQuery = """
         UPDATE interviews SET
            feedback_hr = %s,
            recruiter_hr_id = %s,
            scheduled_hr_id = %s,
            interview_date_hr = %s,
            feedback_date_hr = %s,
            status_hr = %s,
            recommendation_hr = %s,
            transcript_hr = %s,
            updated_at = Now()
            Where position_id = %s and candidate_id = %s
                RETURNING id,    position_id,    candidate_id,    feedback_hr,    interview_date_hr,    feedback_date_hr,    status_hr,
            recommendation_hr,    transcript_hr,    feedback_tec,    interview_date_tec,    feedback_date_tec,    status_tec,    recommendation_tec,
            transcript_tec,    created_at,    updated_at, recruiter_hr_id, scheduled_hr_id, recruiter_tec_id, scheduled_tec_id, analysis_data
            """
    params = [
        Json(interviewData.feedback_hr),
        interviewData.recruiter_hr_id,
        interviewData.scheduled_hr_id,
        interviewData.interview_date_hr,
        interviewData.feedback_date_hr,
        interviewData.status_hr,
        interviewData.recommendation_hr,
        interviewData.transcript_hr,
        interviewData.position_id,
        interviewData.candidate_id
    ]

    with get_cursor() as cur:
        cur.execute(sqlQuery, params)
        row = cur.fetchone()

    if not row:
        return None

    return Interview(
        id=str(row[0]),
        position_id=str(row[1]),
        candidate_id=str(row[2]),
        feedback_hr=row[3],
        interview_date_hr=row[4],
        feedback_date_hr=row[5],
        status_hr=row[6],
        recommendation_hr=row[7],
        transcript_hr=row[8],
        feedback_tec=row[9],
        interview_date_tec=row[10],
        feedback_date_tec=row[11],
        status_tec=row[12],
        recommendation_tec=row[13],
        transcript_tec=row[14],
        created_at=row[15],
        updated_at=row[16],
        recruiter_hr_id=row[17],
        scheduled_hr_id=row[18],
        recruiter_tec_id=row[19],
        scheduled_tec_id=row[20],
        analysis_data=row[21]
    )


# Update interview feedback for the technical team
# This function updates the interview feedback for the technical team.
def update_interview_tec(interviewData: InterviewTec) -> Interview:
    """
     Update the interview feedback for the technical team.

     Args:
         interviewData (InterviewTec): The interview data to update.

     Returns:
         Interview: The updated interview object.
    """
    # Debug logging
    logger.info(f"UPDATE_INTERVIEW_TEC: Received data for position_id={interviewData.position_id}, candidate_id={interviewData.candidate_id}")
    logger.info(f"UPDATE_INTERVIEW_TEC: Status received: '{interviewData.status_tec}'")
    logger.info(f"UPDATE_INTERVIEW_TEC: Transcript length: {len(interviewData.transcript_tec) if interviewData.transcript_tec else 0}")
    logger.info(f"UPDATE_INTERVIEW_TEC: Transcript preview: {interviewData.transcript_tec[:200] if interviewData.transcript_tec else 'None'}...")
    logger.info(f"UPDATE_INTERVIEW_TEC: Feedback keys: {list(interviewData.feedback_tec.keys()) if interviewData.feedback_tec else 'None'}")
    logger.info(f"UPDATE_INTERVIEW_TEC: Feedback content: {interviewData.feedback_tec}")

    feedback = fetch_interview_by_position_id_candidate_id(interviewData.position_id, interviewData.candidate_id)
    if not feedback:
        raise HTTPException(status_code=404, detail="Interview not found (update_interview_tec)")

    logger.info(f"UPDATE_INTERVIEW_TEC: Current status in DB: '{feedback.status_tec}'")
    logger.info(f"UPDATE_INTERVIEW_TEC: StatusInterview.COMPLETED.value = '{StatusInterview.COMPLETED.value}'")

    if feedback.status_tec == StatusInterview.COMPLETED.value or feedback.status_tec == StatusInterview.CANCELLED.value:
        raise HTTPException(status_code=400, detail="Interview already completed or cancelled")

    sqlQuery = """
         UPDATE interviews SET            
            feedback_tec = %s,
            recruiter_tec_id = %s,
            scheduled_tec_id = %s,
            interview_date_tec = %s,
            feedback_date_tec = %s,
            status_tec = %s,
            recommendation_tec = %s,
            transcript_tec = %s,
            updated_at = Now()
            Where position_id = %s and candidate_id = %s
                RETURNING id,    position_id,    candidate_id,    feedback_hr,    interview_date_hr,    feedback_date_hr,    status_hr,
            recommendation_hr,    transcript_hr,    feedback_tec,    interview_date_tec,    feedback_date_tec,    status_tec,    recommendation_tec,
            transcript_tec,    created_at,    updated_at, recruiter_hr_id, scheduled_hr_id, recruiter_tec_id, scheduled_tec_id, analysis_data
            """
    params = [
        Json(interviewData.feedback_tec),
        interviewData.recruiter_tec_id,
        interviewData.scheduled_tec_id,
        interviewData.interview_date_tec,
        interviewData.feedback_date_tec,
        interviewData.status_tec,
        interviewData.recommendation_tec,
        interviewData.transcript_tec,
        interviewData.position_id,
        interviewData.candidate_id
    ]

    with get_cursor() as cur:
        cur.execute(sqlQuery, params)
        row = cur.fetchone()

    if not row:
        return None

    response = Interview(
        id=str(row[0]),
        position_id=str(row[1]),
        candidate_id=str(row[2]),
        feedback_hr=row[3],
        interview_date_hr=row[4],
        feedback_date_hr=row[5],
        status_hr=row[6],
        recommendation_hr=row[7],
        transcript_hr=row[8],
        feedback_tec=row[9],
        interview_date_tec=row[10],
        feedback_date_tec=row[11],
        status_tec=row[12],
        recommendation_tec=row[13],
        transcript_tec=row[14],
        created_at=row[15],
        updated_at=row[16],
        recruiter_hr_id=row[17],
        scheduled_hr_id=row[18],
        recruiter_tec_id=row[19],
        scheduled_tec_id=row[20],
        analysis_data=row[21]
    )

    # fill anwers_data from transcript_tec after is completed
    logger.info(f"UPDATE_INTERVIEW_TEC: Checking if evaluation should run. Status: '{response.status_tec}', Expected: '{StatusInterview.COMPLETED.value}'")
    logger.info(f"UPDATE_INTERVIEW_TEC: Status comparison result: {response.status_tec == StatusInterview.COMPLETED.value}")

    if response.status_tec == StatusInterview.COMPLETED.value:
        logger.info(f"UPDATE_INTERVIEW_TEC: Status is COMPLETED, starting evaluation process for interview {response.id}")

        # Check if we have transcript data or feedback data to evaluate
        has_transcript = response.transcript_tec and response.transcript_tec.strip()
        transcript_length = len(response.transcript_tec) if response.transcript_tec else 0
        has_feedback_data = response.feedback_tec and isinstance(response.feedback_tec, dict)

        logger.info(f"UPDATE_INTERVIEW_TEC: Has transcript: {has_transcript}")
        logger.info(f"UPDATE_INTERVIEW_TEC: Transcript length: {transcript_length}")
        logger.info(f"UPDATE_INTERVIEW_TEC: Has feedback data: {has_feedback_data}")

        # Check if transcript is too short to be meaningful
        if has_transcript and transcript_length < 100:
            logger.warning(f"UPDATE_INTERVIEW_TEC: Transcript is very short ({transcript_length} chars), might be truncated")
            logger.warning(f"UPDATE_INTERVIEW_TEC: Transcript content: '{response.transcript_tec}'")

        if has_transcript or has_feedback_data:
            try:
                # Extract answers from transcript and persist to database
                extraction_result = run_and_persist_interview(response.id, ProcessType.EXTRACT)
                logger.info(f"UPDATE_INTERVIEW_TEC: Extraction result: {extraction_result}")

                # Only proceed with evaluation if extraction was successful
                if extraction_result:
                    # evaluate: anwers_data against questionaire, and persist interview_data
                    logger.info(f"Starting re-evaluation for interview {response.id}")
                    re_evaluate_interview(response.id)
                    logger.info(f"Re-evaluation completed for interview {response.id}")
                else:
                    logger.warning(f"UPDATE_INTERVIEW_TEC: Extraction failed for interview {response.id}")
                    # Try direct evaluation if we have feedback data
                    if has_feedback_data:
                        logger.info(f"UPDATE_INTERVIEW_TEC: Attempting direct evaluation using feedback data")
                        re_evaluate_interview(response.id)
            except Exception as e:
                logger.error(f"Error during interview processing for {response.id}: {str(e)}")
                logger.error(f"Full traceback: {traceback.format_exc()}")
                # Don't re-raise the exception to avoid breaking the update operation
                # The interview update should still succeed even if evaluation fails
        else:
            logger.warning(f"UPDATE_INTERVIEW_TEC: No transcript or feedback data to evaluate for interview {response.id}")
            # Create a basic evaluation indicating no data was provided
            try:
                from models.interview import EvaluateInterviewNoQA, Seniority
                basic_evaluation = EvaluateInterviewNoQA(
                    overall_seniority=Seniority.NA,
                    percentage_of_match=0.0,
                    explanation="No technical interview data was provided. The transcript field appears to be empty or incomplete. Please ensure the full interview transcript with questions and candidate responses is included when submitting technical feedback."
                )

                # Persist this basic evaluation
                with get_cursor() as cur:
                    cur.execute(
                        """
                        UPDATE interviews
                        SET interview_data = %s,
                            updated_at = NOW()
                        WHERE id = %s;
                        """,
                        (Json(basic_evaluation.model_dump()), response.id)
                    )
                logger.info(f"UPDATE_INTERVIEW_TEC: Created basic evaluation for incomplete data in interview {response.id}")
            except Exception as eval_error:
                logger.error(f"UPDATE_INTERVIEW_TEC: Failed to create basic evaluation: {str(eval_error)}")
    else:
        logger.info(f"UPDATE_INTERVIEW_TEC: Status is not COMPLETED, skipping evaluation. Status: '{response.status_tec}'")

    return response


# Get a single interview by position ID and candidate ID
# This function fetches a single interview for the given position and candidate IDs.
def fetch_interview_by_position_id_candidate_id(position_id: str, candidate_id: str) -> Optional[Interview]:
    """
     Fetch a single interview for the given position and candidate IDs.

     Args:
         position_id (str): The ID of the position.
         candidate_id (str): The ID of the candidate.

     Returns:
         Optional[Interview]: The interview object if found, None otherwise.
    """
    try:
        with get_cursor() as cur:
            cur.execute(
                """
                SELECT i.id, i.position_id, i.candidate_id, i.feedback_hr, i.interview_date_hr, i.feedback_date_hr, i.status_hr,
                i.recommendation_hr, i.transcript_hr, i.feedback_tec, i.interview_date_tec, i.feedback_date_tec, i.status_tec, i.recommendation_tec,
                i.transcript_tec, i.anwers_data, i.interview_data, i.created_at, i.updated_at, c.candidate_info, 
                i.recruiter_hr_id, i.scheduled_hr_id, i.recruiter_tec_id, i.scheduled_tec_id, i.analysis_data
                FROM interviews i 
                JOIN candidates_smarthr c ON i.candidate_id = c.id
                WHERE i.position_id::text=%s and i.candidate_id::text=%s and c.is_deleted = false
                ORDER BY created_at DESC
                """,
                (position_id, candidate_id,),
            )
            row = cur.fetchone()
        if not row:
            return None

        position = get_position_by_id(str(row[1]))
        return Interview(
            id=str(row[0]),
            position_id=str(row[1]),
            candidate_id=str(row[2]),
            candidate_info=row[19].get('personal_info', None),
            position_info=position.position_info if position is not None else None,
            feedback_hr=row[3],
            interview_date_hr=row[4],
            feedback_date_hr=row[5],
            status_hr=row[6],
            recommendation_hr=row[7],
            transcript_hr=row[8],
            feedback_tec=row[9],
            interview_date_tec=row[10],
            feedback_date_tec=row[11],
            status_tec=row[12],
            recommendation_tec=row[13],
            transcript_tec=row[14],
            anwers_data=row[15],
            interview_data=row[16],
            created_at=row[17],
            updated_at=row[18],
            recruiter_hr_id=row[20],
            scheduled_hr_id=row[21],
            recruiter_tec_id=row[22],
            scheduled_tec_id=row[23],
            analysis_data=row[24]
        )
    except psycopg2.Error as e:
        print(f"Database error occurred while fetching interviews: {str(e)}")
        logger.error(f"Database error occurred while fetching interviews: {str(e)}")
        raise HTTPException(status_code=500, detail=f"fetch_interview_by_position_id_candidate_id. Database error occurred: {str(e)}")
    except HTTPException as e:
        print(f"HTTPException occurred while fetching interviews: {str(e.detail)}")
        logger.error(f"HTTPException occurred while fetching interviews: {str(e.detail)}")
        raise e
    except Exception as e:
        print(f"Error occurred while fetching interviews: {str(e)}")
        logger.error(f"Error occurred while fetching interviews: {str(e)}")
        raise HTTPException(status_code=500, detail=str(e))


# Get questions by position ID
# This function fetches the questions for the given position ID.
def fetch_questions_by_position_id(position_id: str) -> SingleQuestions:
    """
    Fetch the questions for the given position ID.

    Args:
        position_id (str): The ID of the position.

    Returns:
        SingleQuestions: The questions object for the specified position ID.
    """
    try:
        with get_cursor() as cur:
            sqlQuery = """
                SELECT id, position_id, data, created_at, updated_at, allow_regeneration, created_by, updated_by
                FROM interview_questions WHERE position_id::text=%s
            """
            params = (position_id,)
            cur.execute(sqlQuery, params)
            row = cur.fetchone()
            if not row:
                raise HTTPException(status_code=404, detail="Questions not found")
            return SingleQuestions(
                id=str(row[0]),
                position_id=str(row[1]),
                data=row[2],
                created_at=row[3],
                updated_at=row[4],
                allow_regeneration=row[5],
                created_by=row[6],
                updated_by=row[7]
            )
    except psycopg2.Error as e:
        print(f"Database error occurred while fetch_questions_by_position_id: {str(e)}")
        logger.error(f"Database error occurred while fetch_questions_by_position_id: {str(e)}")
        raise HTTPException(status_code=500, detail=f"fetch_questions_by_position_id. Database error occurred: {str(e)}")
    except HTTPException as e:
        print(f"HTTPException occurred while fetch_questions_by_position_id: {str(e.detail)}")
        logger.error(f"HTTPException occurred while fetch_questions_by_position_id: {str(e.detail)}")
        raise e
    except Exception as e:
        print(f"Error occurred while fetch_questions_by_position_id: {str(e)}")
        logger.error(f"Error occurred while fetch_questions_by_position_id: {str(e)}")
        raise HTTPException(status_code=500, detail=str(e))


# Delete interview by position ID and candidate ID
# This function deletes an interview for the given position and candidate IDs.
def delete_interview(position_id: str, candidate_id: str) -> bool:
    """
    Delete an interview for the given position and candidate IDs.

    Args:
        position_id (str): The ID of the position.
        candidate_id (str): The ID of the candidate.

    Returns:
        bool: True if the interview was deleted successfully, False otherwise.
    """
    try:
        # Fetch the interview to check its status
        feedback = fetch_interview_by_position_id_candidate_id(position_id, candidate_id)
        if not feedback:
            raise HTTPException(status_code=404, detail="Interview not found")
        # You may add status checks here if needed

        with get_cursor() as cur:
            cur.execute(
                """
                DELETE FROM interviews WHERE id = %s
                """,
                (feedback.id,),
            )
        return True
    except psycopg2.Error as e:
        print(f"Database error occurred while deleting interview: {str(e)}")
        logger.error(f"Database error occurred while deleting interview: {str(e)}")
        raise HTTPException(status_code=500, detail=f"delete_interview. Database error occurred: {str(e)}")
    except HTTPException as e:
        print(f"HTTPException occurred while deleting interview: {str(e.detail)}")
        logger.error(f"HTTPException occurred while deleting interview: {str(e.detail)}")
        raise e
    except Exception as e:
        print(f"Error occurred while deleting interview: {str(e)}")
        logger.error(f"Error occurred while deleting interview: {str(e)}")
        raise HTTPException(status_code=500, detail=str(e))


# Get all interviews by candidate ID
# This function fetches all interviews for the given candidate ID.
def fetch_interviews_by_candidate_id(candidate_id: str) -> List[Interview]:
    """
    Fetch all interviews for the given candidate ID.

    Args:
        candidate_id (str): The ID of the candidate for which to fetch interviews.

    Returns:
        List[Interview]: List of interview objects.
    """
    try:
        with get_cursor() as cur:
            cur.execute(
                """
                SELECT i.id, i.position_id, i.candidate_id, i.feedback_hr, i.interview_date_hr, i.feedback_date_hr, i.status_hr,
                i.recommendation_hr, i.transcript_hr, i.feedback_tec, i.interview_date_tec, i.feedback_date_tec, i.status_tec, i.recommendation_tec,
                i.transcript_tec, i.anwers_data, i.interview_data, i.created_at, i.updated_at, c.candidate_info,
                i.recruiter_hr_id, i.scheduled_hr_id, i.recruiter_tec_id, i.scheduled_tec_id, i.analysis_data
                FROM interviews i 
                JOIN candidates_smarthr c ON i.candidate_id = c.id
                WHERE i.candidate_id::text=%s and c.is_deleted = false
                ORDER BY created_at DESC
                """,
                (candidate_id,),
            )
            rows = cur.fetchall()

        interviews: List[Interview] = []
        for row in rows:
            position = get_position_by_id(str(row[1]))
            interviews.append(
                Interview(
                    id=str(row[0]),
                    position_id=str(row[1]),
                    candidate_id=str(row[2]),
                    candidate_info=row[19].get('personal_info', None),
                    position_info=position.position_info if position is not None else None,
                    feedback_hr=row[3],
                    interview_date_hr=row[4],
                    feedback_date_hr=row[5],
                    status_hr=row[6],
                    recommendation_hr=row[7],
                    transcript_hr=row[8],
                    feedback_tec=row[9],
                    interview_date_tec=row[10],
                    feedback_date_tec=row[11],
                    status_tec=row[12],
                    recommendation_tec=row[13],
                    transcript_tec=row[14],
                    anwers_data=row[15],
                    interview_data=row[16],
                    created_at=row[17],
                    updated_at=row[18],
                    recruiter_hr_id=row[20],
                    scheduled_hr_id=row[21],
                    recruiter_tec_id=row[22],
                    scheduled_tec_id=row[23],
                    analysis_data=row[24]
                )
            )
        return interviews
    except psycopg2.Error as e:
        print(f"Database error occurred while fetch_interviews_by_candidate_id: {str(e)}")
        logger.error(f"Database error occurred while fetch_interviews_by_candidate_id: {str(e)}")
        raise HTTPException(status_code=500, detail=f"fetch_interviews_by_candidate_id. Database error occurred: {str(e)}")
    except HTTPException as e:
        print(f"HTTPException occurred while fetch_interviews_by_candidate_id: {str(e.detail)}")
        logger.error(f"HTTPException occurred while fetch_interviews_by_candidate_id: {str(e.detail)}")
        raise e
    except Exception as e:
        print(f"Error occurred while fetch_interviews_by_candidate_id: {str(e)}")
        logger.error(f"Error occurred while fetch_interviews_by_candidate_id: {str(e)}")
        raise HTTPException(status_code=500, detail=str(e))


# Fetch interview by interview ID
# This function fetches an interview by its ID.
def fetch_interview_by_interview_id(interview_id: str) -> Optional[Interview]:
    """
    Fetch an interview by its ID.

    Args:
        interview_id (str): The ID of the interview to fetch.

    Returns:
        Optional[Interview]: The interview object if found, None otherwise.
    """
    try:
        with get_cursor() as cur:
            cur.execute(
                """
                SELECT i.id, i.position_id, i.candidate_id, i.feedback_hr, i.interview_date_hr, i.feedback_date_hr, i.status_hr,
                i.recommendation_hr, i.transcript_hr, i.feedback_tec, i.interview_date_tec, i.feedback_date_tec, i.status_tec, i.recommendation_tec,
                i.transcript_tec, i.anwers_data, i.interview_data, i.created_at, i.updated_at, c.candidate_info,
                i.recruiter_hr_id, i.scheduled_hr_id, i.recruiter_tec_id, i.scheduled_tec_id, i.analysis_data
                FROM interviews i 
                JOIN candidates_smarthr c ON i.candidate_id = c.id
                WHERE i.id::text=%s and c.is_deleted = false
                ORDER BY created_at DESC
                """,
                (interview_id,),
            )
            row = cur.fetchone()
        if not row:
            return None

        position = get_position_by_id(str(row[1]))
        return Interview(
            id=str(row[0]),
            position_id=str(row[1]),
            candidate_id=str(row[2]),
            candidate_info=row[19].get('personal_info', None),
            position_info=position.position_info if position is not None else None,
            feedback_hr=row[3],
            interview_date_hr=row[4],
            feedback_date_hr=row[5],
            status_hr=row[6],
            recommendation_hr=row[7],
            transcript_hr=row[8],
            feedback_tec=row[9],
            interview_date_tec=row[10],
            feedback_date_tec=row[11],
            status_tec=row[12],
            recommendation_tec=row[13],
            transcript_tec=row[14],
            anwers_data=row[15],
            interview_data=row[16],
            created_at=row[17],
            updated_at=row[18],
            recruiter_hr_id=row[20],
            scheduled_hr_id=row[21],
            recruiter_tec_id=row[22],
            scheduled_tec_id=row[23],
            analysis_data=row[24]
        )
    except psycopg2.Error as e:
        print(f"Database error occurred while fetch_interview_by_interview_id: {str(e)}")
        logger.error(f"Database error occurred while fetch_interview_by_interview_id: {str(e)}")
        raise HTTPException(status_code=500, detail=f"fetch_interview_by_interview_id. Database error occurred: {str(e)}")
    except HTTPException as e:
        print(f"HTTPException occurred while fetch_interview_by_interview_id: {str(e.detail)}")
        logger.error(f"HTTPException occurred while fetch_interview_by_interview_id: {str(e.detail)}")
        raise e
    except Exception as e:
        print(f"Error occurred while fetch_interview_by_interview_id: {str(e)}")
        logger.error(f"Error occurred while fetch_interview_by_interview_id: {str(e)}")
        raise HTTPException(status_code=500, detail=str(e))


# Re-evaluate an interview by its ID
# This function re-evaluates an interview by its ID.
# It fetches the interview data, extract answers data if needed, evaluates it, and then fetches the updated interview data.
def re_evalute_interview(interview_id: str) -> Interview:
    """
    Re-evaluate an interview by its ID.

    Args:       
        interview_id (str): The ID of the interview to re-evaluate.

    Returns:
        Interview: The updated interview object.
    """
    # 1. Fetch the interview data
    interview = fetch_interview_by_interview_id(interview_id)
    if not interview:
        raise HTTPException(status_code=404, detail="Interview not found (re_evalute_interview)")

    if not interview.transcript_tec:
        raise HTTPException(status_code=400, detail="Technical Transcript not found")

    if not interview.anwers_data:
        # 2. Run and persist the interview
        run_and_persist_interview(interview.id, ProcessType.EXTRACT)

    # 3. Evaluate the interview
    re_evaluate_interview(interview.id)

    # 4. Fetch the updated interview data
    interview = fetch_interview_by_interview_id(interview.id)
    return interview


# Change Questions status
def update_question_regeneration_status(position_id: str, question_id: str, allow_regeneration: bool) -> bool:
    """
    Change the status of a question in the interview_questions table.
    :param position_id: The ID of the position to which the question belongs.
    :param question_id: The ID of the question to update.
    :param allow_regeneration: Boolean indicating whether regeneration is allowed.
    :return: True if the update was successful, False otherwise.
    """
    with get_cursor() as cur:
        cur.execute(
            """
            UPDATE interview_questions
            SET allow_regeneration = %s,
                updated_at = NOW()
            WHERE id = %s AND position_id = %s;
            """,
            (allow_regeneration, question_id, position_id)
        )
        if cur.rowcount == 0:
            raise HTTPException(status_code=404, detail="Question not found")
        return True
    return False
