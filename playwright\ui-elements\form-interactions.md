# SmartHR Form Interactions and Validation Patterns

This document provides comprehensive guidance on form interactions, validation patterns, and error handling in the SmartHR application for automated testing.

## 📋 Form Structure Overview

### Ant Design Form Components
The SmartHR application uses Ant Design form components with consistent patterns:

```javascript
// Basic form structure
'.ant-form'                    // Form container
'.ant-form-item'              // Individual form field container
'.ant-form-item-label'        // Field label
'.ant-form-item-control'      // Field control wrapper
'.ant-form-item-required'     // Required field indicator
```

### Form Types in SmartHR
1. **Search and Filter Forms** - Job/candidate filtering
2. **Interview Feedback Forms** - HR and Technical feedback
3. **Interview Generation Forms** - Question configuration
4. **Candidate Upload Forms** - File upload and data entry

## 🔄 Form Interaction Patterns

### Basic Form Field Interactions

#### Text Input Fields
```javascript
// Standard text input
await page.fill('.ant-input', 'Sample text');
await page.fill('input[placeholder="Enter name"]', '<PERSON>');

// Clear and refill
await page.fill('.ant-input', '');
await page.fill('.ant-input', 'New text');

// Verify input value
const inputValue = await page.inputValue('.ant-input');
expect(inputValue).toBe('Expected value');
```

#### Text Area Fields
```javascript
// Fill text area
await page.fill('textarea.ant-input', 'Multi-line text content');
await page.fill('textarea[placeholder*="Comments"]', 'Detailed feedback here');

// Handle large text content
const longText = 'A'.repeat(1000);
await page.fill('textarea', longText);
```

#### Number Input Fields
```javascript
// Number input interaction
await page.fill('.ant-input-number input', '10');
await page.fill('input[type="number"]', '25');

// Use increment/decrement buttons
await page.click('.ant-input-number-handler-up');
await page.click('.ant-input-number-handler-down');

// Verify numeric value
const numericValue = await page.inputValue('.ant-input-number input');
expect(parseInt(numericValue)).toBe(10);
```

### Select and Dropdown Interactions

#### Single Select Dropdowns
```javascript
// Open dropdown and select option
await page.click('.ant-select');
await page.waitForSelector('.ant-select-dropdown');
await page.click('.ant-select-item-option:has-text("Option 1")');

// Select by value
await page.selectOption('.ant-select', 'option-value');

// Verify selection
const selectedText = await page.textContent('.ant-select-selection-item');
expect(selectedText).toBe('Expected Option');
```

#### Multi-Select Dropdowns
```javascript
// Multi-select interaction
await page.click('.ant-select-multiple');
await page.click('.ant-select-item-option:has-text("Option 1")');
await page.click('.ant-select-item-option:has-text("Option 2")');

// Remove selection
await page.click('.ant-select-selection-item-remove');

// Clear all selections
await page.click('.ant-select-clear');
```

### Date and Time Picker Interactions

#### Date Picker
```javascript
// Open date picker
await page.click('.ant-picker');
await page.waitForSelector('.ant-picker-dropdown');

// Select today's date
await page.click('.ant-picker-today-btn');

// Select specific date
await page.click('.ant-picker-cell[title="2024-01-15"]');

// Navigate months
await page.click('.ant-picker-header-prev-btn');
await page.click('.ant-picker-header-next-btn');

// Direct date input
await page.fill('.ant-picker input', '2024-01-15');
```

#### Time Picker
```javascript
// Time picker interaction
await page.click('.ant-time-picker');
await page.waitForSelector('.ant-time-picker-panel');

// Select time components
await page.click('.ant-time-picker-column:nth-child(1) .ant-time-picker-cell:has-text("14")'); // Hour
await page.click('.ant-time-picker-column:nth-child(2) .ant-time-picker-cell:has-text("30")'); // Minute

// Confirm time selection
await page.click('.ant-time-picker-now-btn');
```

### Checkbox and Radio Button Interactions

#### Checkbox Interactions
```javascript
// Check/uncheck individual checkboxes
await page.check('.ant-checkbox-input');
await page.uncheck('.ant-checkbox-input');

// Check specific skill categories
await page.check('.ant-checkbox:has-text("Technical Skills") input');
await page.check('.ant-checkbox:has-text("Soft Skills") input');

// Verify checkbox state
const isChecked = await page.isChecked('.ant-checkbox-input');
expect(isChecked).toBeTruthy();

// Select all checkbox
await page.check('.ant-checkbox:has-text("Select All") input');
```

#### Radio Button Interactions
```javascript
// Select radio button
await page.check('.ant-radio-input[value="option1"]');
await page.click('.ant-radio:has-text("Option 1")');

// Verify radio selection
const selectedRadio = await page.locator('.ant-radio-checked').textContent();
expect(selectedRadio).toContain('Option 1');
```

## ✅ Form Validation Patterns

### Client-Side Validation Testing

#### Required Field Validation
```javascript
// Test required field validation
const requiredInput = page.locator('.ant-form-item-required input');
await requiredInput.fill('');
await page.click('.ant-btn:has-text("Submit")');

// Check for validation error
const validationError = page.locator('.ant-form-item-explain-error');
await expect(validationError).toBeVisible();
await expect(validationError).toContainText('required');
```

#### Format Validation
```javascript
// Test email format validation
await page.fill('input[type="email"]', 'invalid-email');
await page.click('.ant-btn:has-text("Submit")');

const emailError = page.locator('.ant-form-item-has-error .ant-form-item-explain-error');
await expect(emailError).toContainText('valid email');

// Test number range validation
await page.fill('input[type="number"]', '100');
await page.blur('input[type="number"]');

const rangeError = page.locator('.ant-form-item-explain-error');
if (await rangeError.isVisible()) {
  const errorText = await rangeError.textContent();
  expect(errorText).toMatch(/range|between|maximum/i);
}
```

#### Custom Validation Rules
```javascript
// Test question count validation (1-20 range)
await page.fill('.ant-input-number input', '25');
await page.click('.ant-btn:has-text("Generate")');

const countError = page.locator('.ant-message-error, .ant-notification-error');
await expect(countError).toBeVisible();
await expect(countError).toContainText('maximum');
```

### Server-Side Validation Testing

#### API Validation Errors
```javascript
// Test server validation by submitting invalid data
await page.fill('input[name="title"]', ''); // Empty required field
await page.click('.ant-btn-primary:has-text("Save")');

// Wait for server response
await page.waitForSelector('.ant-notification-error', { timeout: 10000 });

const serverError = page.locator('.ant-notification-error');
await expect(serverError).toBeVisible();

// Check for specific validation messages
const errorMessage = await serverError.textContent();
expect(errorMessage).toMatch(/required|invalid|error/i);
```

## 📤 Form Submission Patterns

### Successful Form Submission
```javascript
// Complete form submission flow
await page.fill('input[name="recruiter"]', 'John Smith');
await page.click('.ant-picker');
await page.click('.ant-picker-today-btn');
await page.selectOption('.ant-select', 'completed');
await page.fill('textarea[name="comments"]', 'Excellent candidate');

// Submit form
await page.click('.ant-btn-primary:has-text("Save")');

// Wait for success feedback
await page.waitForSelector('.ant-notification-success', { timeout: 10000 });
await expect(page.locator('.ant-notification-success')).toBeVisible();

// Verify form reset or navigation
const formReset = await page.inputValue('input[name="recruiter"]') === '';
const navigationOccurred = page.url() !== initialUrl;
expect(formReset || navigationOccurred).toBeTruthy();
```

### Form Submission with Loading States
```javascript
// Monitor loading state during submission
await page.click('.ant-btn-primary:has-text("Save")');

// Verify loading state
await expect(page.locator('.ant-btn-loading')).toBeVisible();
await expect(page.locator('.ant-spin-spinning')).toBeVisible();

// Wait for loading to complete
await page.waitForSelector('.ant-btn-loading', { state: 'detached' });
await page.waitForSelector('.ant-spin-spinning', { state: 'detached' });

// Verify completion
await expect(page.locator('.ant-notification-success')).toBeVisible();
```

## 🚨 Error Handling Patterns

### Network Error Handling
```javascript
// Simulate network failure
await page.route('**/api/**', route => route.abort());

// Attempt form submission
await page.click('.ant-btn-primary:has-text("Save")');

// Check for network error handling
const networkError = page.locator('.ant-notification-error, .ant-message-error');
await expect(networkError).toBeVisible({ timeout: 10000 });

const errorText = await networkError.textContent();
expect(errorText).toMatch(/network|connection|failed/i);

// Restore network
await page.unroute('**/api/**');
```

### Timeout Error Handling
```javascript
// Simulate slow API response
await page.route('**/api/**', route => {
  setTimeout(() => route.continue(), 30000); // 30 second delay
});

// Submit form with timeout
await page.click('.ant-btn-primary:has-text("Save")');

// Check for timeout handling
const timeoutError = page.locator('.ant-notification-error');
await expect(timeoutError).toBeVisible({ timeout: 35000 });
```

## 🔄 Dynamic Form Patterns

### Conditional Field Display
```javascript
// Test conditional field visibility
await page.selectOption('.ant-select', 'custom-option');

// Check if conditional fields appear
const conditionalField = page.locator('.conditional-field, [data-conditional="true"]');
await expect(conditionalField).toBeVisible();

// Fill conditional field
await page.fill('.conditional-field input', 'Conditional value');
```

### Dynamic Field Addition
```javascript
// Test adding dynamic fields (if applicable)
const addButton = page.locator('button:has-text("Add"), .ant-btn:has(.anticon-plus)');
if (await addButton.isVisible()) {
  await addButton.click();
  
  // Verify new field appears
  const dynamicFields = page.locator('.dynamic-field, .ant-form-item');
  const fieldCount = await dynamicFields.count();
  expect(fieldCount).toBeGreaterThan(1);
}
```

## 📋 Form Testing Best Practices

### Comprehensive Form Testing Strategy
```javascript
// Complete form testing function
async function testFormCompletely(page, formSelector) {
  const form = page.locator(formSelector);
  
  // 1. Test empty form submission
  await page.click('.ant-btn-primary:has-text("Save")');
  await expect(page.locator('.ant-form-item-has-error')).toBeVisible();
  
  // 2. Fill required fields
  const requiredFields = form.locator('.ant-form-item-required input, .ant-form-item-required textarea');
  const fieldCount = await requiredFields.count();
  
  for (let i = 0; i < fieldCount; i++) {
    const field = requiredFields.nth(i);
    const fieldType = await field.getAttribute('type');
    
    if (fieldType === 'text' || fieldType === 'email') {
      await field.fill('Test value');
    } else if (fieldType === 'number') {
      await field.fill('10');
    }
  }
  
  // 3. Test successful submission
  await page.click('.ant-btn-primary:has-text("Save")');
  await expect(page.locator('.ant-notification-success')).toBeVisible();
  
  // 4. Verify form state after submission
  const isFormReset = await form.locator('input').first().inputValue() === '';
  console.log('Form reset after submission:', isFormReset);
}
```

### Form Validation Testing Utilities
```javascript
// Utility function for validation testing
async function testFieldValidation(page, fieldSelector, invalidValue, expectedErrorPattern) {
  await page.fill(fieldSelector, invalidValue);
  await page.blur(fieldSelector);
  
  const errorElement = page.locator('.ant-form-item-has-error .ant-form-item-explain-error');
  await expect(errorElement).toBeVisible();
  
  const errorText = await errorElement.textContent();
  expect(errorText).toMatch(expectedErrorPattern);
}

// Usage examples
await testFieldValidation(page, 'input[type="email"]', 'invalid-email', /valid email/i);
await testFieldValidation(page, 'input[type="number"]', '-5', /positive number/i);
```

This comprehensive form interactions documentation provides detailed guidance for testing all aspects of form functionality in the SmartHR application.
