# SmartHR Playwright Testing Documentation

This documentation provides comprehensive guidance for automated testing of the SmartHR application using Playwright. It's specifically designed to help AI agents understand the application structure, user workflows, and testing patterns.

## 📋 Table of Contents

- [Overview](#overview)
- [Quick Start](#quick-start)
- [Application Architecture](#application-architecture)
- [Key Workflows](#key-workflows)
- [Documentation Structure](#documentation-structure)
- [Testing Patterns](#testing-patterns)
- [Best Practices](#best-practices)

## 🎯 Overview

SmartHR is a comprehensive HR management application built with React + TypeScript frontend and FastAPI backend. The application manages job positions, candidates, interviews, and feedback processes.

### Core Functionality
- **Job Management**: Create, view, and manage job positions
- **Candidate Management**: Handle candidate profiles and applications
- **Interview Process**: Generate AI-powered interview questions and manage interviews
- **Feedback System**: Collect and manage HR and Technical interview feedback

### Technology Stack
- **Frontend**: React 18 + TypeScript, Vite, Ant Design, React Router
- **Backend**: FastAPI, PostgreSQL, AI-powered question generation
- **Authentication**: Azure MSAL (bypassed in local development)
- **Testing**: Playwright with comprehensive test infrastructure

## 🚀 Quick Start

### Prerequisites
- Node.js 18+
- Python 3.11+
- PostgreSQL 15+
- Existing Playwright setup in `smarthr-fe/tests/`

### Key Application URLs
- **Home/Job Orders**: `http://localhost:5173/`
- **Job Details**: `http://localhost:5173/job/{id}`
- **Candidates**: `http://localhost:5173/candidates`
- **Candidate Details**: `http://localhost:5173/candidates/{id}`

### Authentication
In local development, authentication is bypassed. The app runs without login requirements when accessed from localhost.

## 🏗️ Application Architecture

### Main Components
1. **JobOrders** (`/`): Lists all open positions with filtering and search
2. **JobDetails** (`/job/:id`): Multi-tab interface for job management
3. **CandidatesTable** (`/candidates`): Candidate management interface
4. **CandidateDetailPage** (`/candidates/:id`): Individual candidate details

### Navigation Structure
```
Home (JobOrders)
├── Job Details (/job/:id)
│   ├── Job Description Tab
│   ├── Matching Candidates Tab
│   ├── Manual Matching Tab
│   └── Generate AI Interview Tab
│       └── Candidate List (Selected Candidates)
└── Candidates (/candidates)
    └── Candidate Details (/candidates/:id)
```

### State Management
- **JobContext**: Manages job selection and candidate selection state
- **NavigationContext**: Handles navigation state and breadcrumbs
- **LocalAuthWrapper**: Manages authentication state

## 🔄 Key Workflows

### 1. Job Application and Interview Generation Workflow
1. Navigate to Home (`/`) to view job orders
2. Search/filter for specific positions
3. Click on a job to view details (`/job/:id`)
4. Navigate to "Generate AI Interview" tab
5. Configure interview parameters (question count, skill categories)
6. Generate interview questions
7. View and manage selected candidates

### 2. Candidate Management and Interview Workflow
1. Navigate to job details (`/job/:id`)
2. View matching candidates or manually add candidates
3. Select candidates for interviews
4. Navigate to candidate list in interview tab
5. Submit HR and Technical feedback for each candidate
6. Manage interview scheduling and status

## 📁 Documentation Structure

```
playwright/
├── README.md                     # This file - main overview
├── workflows/                    # Step-by-step workflow guides
│   ├── job-application-workflow.md
│   ├── interview-generation-workflow.md
│   ├── candidate-management-workflow.md
│   └── interview-feedback-workflow.md
├── ui-elements/                  # UI element references
│   ├── selectors-reference.md
│   ├── navigation-patterns.md
│   └── form-interactions.md
├── test-scenarios/              # Test scenarios and cases
│   ├── job-management-scenarios.md
│   ├── interview-scenarios.md
│   └── candidate-scenarios.md
├── examples/                    # Code examples
│   ├── basic-test-examples.js
│   ├── advanced-test-examples.js
│   └── page-object-examples.js
└── api-integration/            # Backend integration docs
    ├── backend-endpoints.md
    └── data-flow.md
```

## 🧪 Testing Patterns

### Existing Test Infrastructure
The application already has comprehensive Playwright tests in `smarthr-fe/tests/`:
- **E2E Tests**: Complete user workflow testing
- **Page Objects**: Reusable page interaction patterns
- **Fixtures**: Test data and setup utilities
- **Visual Tests**: UI consistency validation

### Integration with Existing Tests
This documentation complements the existing test infrastructure by providing:
- Detailed workflow documentation for AI agents
- UI element reference guides
- Test scenario libraries
- Example code patterns

## 📋 Best Practices

### For AI Agents
1. **Always wait for loading states** to complete before interactions
2. **Use data-testid attributes** when available for reliable element selection
3. **Handle dynamic content** with appropriate wait strategies
4. **Validate API responses** when testing form submissions
5. **Clean up test data** after test execution

### Selector Priorities
1. `data-testid` attributes (most reliable)
2. Ant Design class names (`.ant-*`)
3. Text content selectors (`:has-text()`)
4. CSS selectors (least reliable, use sparingly)

### Common Patterns
- **Loading States**: Wait for `.ant-spin-spinning` to disappear
- **Notifications**: Look for `.ant-notification` or `.ant-message`
- **Modals**: Use `.ant-modal` for popup interactions
- **Forms**: Use `.ant-form` and `.ant-form-item` for form interactions

## 🔗 Related Resources

- [Existing E2E Testing Guide](../smarthr-fe/README-E2E-TESTING.md)
- [Playwright Documentation](https://playwright.dev/)
- [Ant Design Components](https://ant.design/components/overview/)
- [React Router Documentation](https://reactrouter.com/)

## 📝 Usage Notes

This documentation is designed to be used alongside the existing Playwright test infrastructure. When creating new tests:

1. Reference the workflow documentation for user journey understanding
2. Use the UI elements reference for reliable selectors
3. Follow the test scenarios for comprehensive coverage
4. Leverage the examples for implementation patterns

The goal is to enable AI agents to understand and test the SmartHR application effectively while maintaining consistency with existing test patterns.
