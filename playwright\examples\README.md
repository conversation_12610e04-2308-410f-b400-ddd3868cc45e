# SmartHR Test Examples

This directory contains practical code examples for automated testing of the SmartHR application using Playwright. These examples demonstrate common patterns, best practices, and integration with the existing test infrastructure.

## 📋 Available Examples

### Code Example Categories

1. **[Basic Test Examples](basic-test-examples.js)**
   - Simple navigation and interaction patterns
   - Form filling and submission
   - Basic assertions and validations
   - Common UI element interactions

2. **[Advanced Test Examples](advanced-test-examples.js)**
   - Complex workflow automation
   - Error handling and recovery
   - Performance testing patterns
   - Cross-browser compatibility testing

3. **[Page Object Examples](page-object-examples.js)**
   - Page Object Model implementations
   - Reusable component patterns
   - State management in page objects
   - Integration with existing page objects

## 🎯 Example Categories

### Navigation Examples
- Page routing and URL validation
- Tab switching and state management
- Breadcrumb navigation
- Menu interactions

### Form Interaction Examples
- Text input and validation
- Dropdown and select interactions
- Checkbox and radio button handling
- Date picker and time selection

### Data Validation Examples
- API response validation
- UI state verification
- Cross-page data consistency
- Error message validation

### Workflow Examples
- End-to-end user journeys
- Multi-step process automation
- State transition testing
- Business logic validation

## 🧪 Example Structure

Each example file follows this structure:

### 1. Imports and Setup
```javascript
import { test, expect } from '@playwright/test';
import { JobOrdersPage, JobDetailsPage } from '../pages';
import { createAssertions } from '../utils/assertions';
```

### 2. Test Configuration
```javascript
test.describe('Feature Name', () => {
  test.beforeEach(async ({ page }) => {
    // Common setup
  });
});
```

### 3. Test Implementation
```javascript
test('should perform specific action', async ({ page }) => {
  // Test implementation with detailed comments
});
```

### 4. Validation and Cleanup
```javascript
// Comprehensive assertions
// Cleanup operations
```

## 🔄 Common Patterns

### Basic Navigation Pattern
```javascript
// Navigate to page and wait for load
await page.goto('/');
await page.waitForLoadState('networkidle');
await page.waitForSelector('.ant-table', { timeout: 10000 });

// Verify page loaded correctly
await expect(page).toHaveURL('/');
await expect(page.locator('.ant-table')).toBeVisible();
```

### Form Interaction Pattern
```javascript
// Fill form with validation
await page.fill('input[placeholder="Search..."]', 'test query');
await page.selectOption('.ant-select', 'option-value');
await page.check('.ant-checkbox:has-text("Option")');

// Submit and validate
await page.click('.ant-btn-primary:has-text("Submit")');
await page.waitForSelector('.ant-notification-success');
await expect(page.locator('.success-message')).toBeVisible();
```

### Error Handling Pattern
```javascript
// Test error scenarios
await page.fill('input[required]', ''); // Invalid input
await page.click('.submit-button');

// Validate error handling
await expect(page.locator('.ant-form-item-explain-error')).toBeVisible();
await expect(page.locator('.error-notification')).toContainText('Required field');
```

### Async Operation Pattern
```javascript
// Handle async operations
await page.click('.generate-button');
await page.waitForSelector('.ant-spin-spinning'); // Loading starts
await page.waitForSelector('.ant-spin-spinning', { state: 'detached' }); // Loading ends

// Validate results
await expect(page.locator('.generated-content')).toBeVisible();
```

## 🎨 Integration Examples

### With Existing Page Objects
```javascript
import { JobDetailsPage } from '../tests/pages/JobDetailsPage';

test('should use existing page object', async ({ page }) => {
  const jobDetailsPage = new JobDetailsPage(page, 'job-id-123');
  
  await jobDetailsPage.goto();
  await jobDetailsPage.navigateToTab('interview');
  await jobDetailsPage.generateInterviewQuestions({
    questionCount: 10,
    technicalSkills: true,
    softSkills: false
  });
  
  await jobDetailsPage.expectQuestionsGenerated(10);
});
```

### With Test Fixtures
```javascript
import { test, expect } from '../tests/fixtures';

test('should use test fixtures', async ({ 
  loggedInPage, 
  createdPositionId, 
  jobOrdersPage 
}) => {
  await jobOrdersPage.goto();
  await jobOrdersPage.searchForJob('Software Engineer');
  await jobOrdersPage.expectJobToBeVisible('Software Engineer');
});
```

### With Custom Assertions
```javascript
import { createAssertions } from '../tests/utils/assertions';

test('should use custom assertions', async ({ page }) => {
  const assertions = createAssertions(page);
  
  await page.goto('/job/123');
  await assertions.expectLoadingComplete();
  await assertions.expectSuccessNotification();
  await assertions.expectUrlToMatch(/\/job\/123/);
});
```

## 🚀 Performance Examples

### Load Time Testing
```javascript
test('should load page within performance budget', async ({ page }) => {
  const startTime = Date.now();
  
  await page.goto('/');
  await page.waitForLoadState('networkidle');
  
  const loadTime = Date.now() - startTime;
  expect(loadTime).toBeLessThan(3000); // 3 second budget
});
```

### Resource Monitoring
```javascript
test('should monitor network requests', async ({ page }) => {
  const requests = [];
  
  page.on('request', request => {
    requests.push({
      url: request.url(),
      method: request.method(),
      timestamp: Date.now()
    });
  });
  
  await page.goto('/');
  await page.waitForLoadState('networkidle');
  
  // Validate API calls
  const apiRequests = requests.filter(r => r.url.includes('/api/'));
  expect(apiRequests.length).toBeGreaterThan(0);
});
```

## 🔍 Debugging Examples

### Screenshot on Failure
```javascript
test('should capture screenshot on failure', async ({ page }) => {
  try {
    await page.goto('/');
    await page.click('.non-existent-element');
  } catch (error) {
    await page.screenshot({ 
      path: `screenshots/failure-${Date.now()}.png`,
      fullPage: true 
    });
    throw error;
  }
});
```

### Console Log Monitoring
```javascript
test('should monitor console errors', async ({ page }) => {
  const consoleErrors = [];
  
  page.on('console', msg => {
    if (msg.type() === 'error') {
      consoleErrors.push(msg.text());
    }
  });
  
  await page.goto('/');
  await page.waitForLoadState('networkidle');
  
  // Validate no console errors
  expect(consoleErrors).toHaveLength(0);
});
```

## 🎯 Best Practice Examples

### Reliable Element Selection
```javascript
// Good: Use data-testid when available
await page.click('[data-testid="submit-button"]');

// Good: Use Ant Design classes
await page.click('.ant-btn-primary:has-text("Submit")');

// Avoid: Generic selectors
// await page.click('button'); // Too generic
// await page.click('#btn-123'); // Brittle ID
```

### Proper Wait Strategies
```javascript
// Wait for element to be visible
await page.waitForSelector('.content', { state: 'visible' });

// Wait for network to be idle
await page.waitForLoadState('networkidle');

// Wait for specific condition
await page.waitForFunction(() => {
  return document.querySelectorAll('.item').length > 0;
});
```

### Error Recovery
```javascript
// Retry mechanism for flaky operations
async function retryOperation(page, operation, maxRetries = 3) {
  for (let i = 0; i < maxRetries; i++) {
    try {
      await operation();
      return; // Success
    } catch (error) {
      if (i === maxRetries - 1) throw error; // Last attempt
      await page.waitForTimeout(1000); // Wait before retry
    }
  }
}
```

## 📝 Usage Guidelines

### Adapting Examples
1. **Modify Selectors**: Update selectors to match your specific UI
2. **Adjust Timeouts**: Set appropriate timeouts for your environment
3. **Customize Validation**: Adapt assertions to your requirements
4. **Add Error Handling**: Include appropriate error handling for your scenarios

### Combining Examples
1. **Mix Patterns**: Combine different patterns for complex scenarios
2. **Reuse Components**: Extract reusable functions and patterns
3. **Build Libraries**: Create custom utility functions from examples
4. **Document Changes**: Keep track of modifications and customizations

### Integration Strategy
1. **Start Simple**: Begin with basic examples and build complexity
2. **Follow Conventions**: Maintain consistency with existing test patterns
3. **Test Thoroughly**: Validate examples in your specific environment
4. **Iterate and Improve**: Refine examples based on real-world usage

These examples provide practical, ready-to-use code patterns for comprehensive automated testing of the SmartHR application, ensuring reliable and maintainable test automation.
