# Job Application and Position Management Workflow

This document provides comprehensive step-by-step instructions for testing the job application and position management workflows in the SmartHR application.

## 📋 Overview

### Purpose
Test the complete job management functionality including job listings, search, filtering, and detailed job view navigation.

### Scope
- Job listings display and pagination
- Search and filtering functionality
- Job details navigation and viewing
- Tab-based job information management

### Prerequisites
- Application running on `http://localhost:5173`
- Backend API available on `http://localhost:8080`
- Test data: At least one job position in the database
- Authentication bypassed (local development mode)

### Expected Outcomes
- Successfully navigate job listings
- Filter and search jobs effectively
- View detailed job information
- Navigate between job detail tabs

## 🔄 Step-by-Step Workflow

### Phase 1: Navigate to Job Listings

#### Step 1.1: Access Home Page
```javascript
// Navigate to the main application
await page.goto('http://localhost:5173/');
await page.waitForLoadState('networkidle');

// Wait for job listings to load
await page.waitForSelector('.ant-table', { timeout: 10000 });
```

**UI Elements:**
- **URL**: `http://localhost:5173/`
- **Main Container**: `.ant-layout-content`
- **Job Table**: `.ant-table`
- **Loading Indicator**: `.ant-spin-spinning`

**Validation Points:**
- Page loads successfully
- URL matches expected home page
- Job table is visible
- No loading indicators remain active

#### Step 1.2: Verify Job Listings Display
```javascript
// Check if jobs are displayed
const jobRows = page.locator('.ant-table-tbody .ant-table-row');
const jobCount = await jobRows.count();

// Verify table structure
await expect(page.locator('.ant-table-thead')).toBeVisible();
await expect(page.locator('.ant-table-tbody')).toBeVisible();

// Check for empty state if no jobs
if (jobCount === 0) {
  await expect(page.locator('.ant-empty')).toBeVisible();
}
```

**UI Elements:**
- **Table Header**: `.ant-table-thead`
- **Table Body**: `.ant-table-tbody`
- **Job Rows**: `.ant-table-row`
- **Empty State**: `.ant-empty`

**Validation Points:**
- Table structure is properly rendered
- Job data is displayed or empty state is shown
- Column headers are visible and properly labeled

### Phase 2: Search and Filter Jobs

#### Step 2.1: Use Search Functionality
```javascript
// Locate search input
const searchInput = page.locator('input[placeholder*="Search"], .ant-input[placeholder*="search"]');
await expect(searchInput).toBeVisible();

// Perform search
await searchInput.fill('Software Engineer');
await searchInput.press('Enter');

// Wait for search results
await page.waitForSelector('.ant-spin-spinning', { state: 'detached' });
await page.waitForTimeout(1000); // Allow for debounced search
```

**UI Elements:**
- **Search Input**: `input[placeholder*="Search"]`
- **Search Button**: `.ant-btn:has-text("Search")`
- **Clear Search**: `.ant-input-clear-icon`

**Validation Points:**
- Search input accepts text input
- Search results update based on query
- Loading states are handled properly
- Results are filtered correctly

#### Step 2.2: Apply Filters
```javascript
// Stage filter
const stageFilter = page.locator('.ant-select:has(.ant-select-selection-item:has-text("Stage"))');
if (await stageFilter.isVisible()) {
  await stageFilter.click();
  await page.click('.ant-select-item-option:has-text("Active")');
}

// Client filter
const clientFilter = page.locator('.ant-select:has(.ant-select-selection-item:has-text("Client"))');
if (await clientFilter.isVisible()) {
  await clientFilter.click();
  const clientOptions = page.locator('.ant-select-item-option');
  if (await clientOptions.count() > 0) {
    await clientOptions.first().click();
  }
}

// Apply filters
const applyButton = page.locator('.ant-btn:has-text("Apply")');
if (await applyButton.isVisible()) {
  await applyButton.click();
  await page.waitForSelector('.ant-spin-spinning', { state: 'detached' });
}
```

**UI Elements:**
- **Stage Filter**: `.ant-select` (stage dropdown)
- **Client Filter**: `.ant-select` (client dropdown)
- **Location Filter**: `.ant-select` (location dropdown)
- **Apply Button**: `.ant-btn:has-text("Apply")`
- **Clear Filters**: `.ant-btn:has-text("Clear")`

**Validation Points:**
- Filter dropdowns populate with available options
- Multiple filters can be applied simultaneously
- Apply button triggers filter execution
- Results update based on selected filters

#### Step 2.3: Clear Filters and Search
```javascript
// Clear search
const clearSearchIcon = page.locator('.ant-input-clear-icon');
if (await clearSearchIcon.isVisible()) {
  await clearSearchIcon.click();
}

// Clear filters
const clearFiltersButton = page.locator('.ant-btn:has-text("Clear")');
if (await clearFiltersButton.isVisible()) {
  await clearFiltersButton.click();
  await page.waitForSelector('.ant-spin-spinning', { state: 'detached' });
}
```

**Validation Points:**
- Clear actions reset search and filters
- Job listings return to unfiltered state
- UI elements reset to default state

### Phase 3: Navigate to Job Details

#### Step 3.1: Select and Click Job
```javascript
// Get first available job
const jobRows = page.locator('.ant-table-tbody .ant-table-row');
const firstJob = jobRows.first();

// Get job information before clicking
const jobTitle = await firstJob.locator('td').first().textContent();
console.log('Clicking job:', jobTitle);

// Click on job row to navigate to details
await firstJob.click();

// Wait for navigation to job details
await page.waitForURL('**/job/**');
await page.waitForSelector('.ant-card, [data-testid="job-details"]', { timeout: 10000 });
```

**UI Elements:**
- **Job Row**: `.ant-table-row`
- **Job Title Link**: `a[href*="/job/"]`
- **Job Details Container**: `.ant-card, [data-testid="job-details"]`

**Validation Points:**
- Job row is clickable
- Navigation occurs to job details page
- URL changes to include job ID
- Job details page loads successfully

#### Step 3.2: Verify Job Details Page Structure
```javascript
// Verify page structure
await expect(page.locator('.ant-tabs')).toBeVisible();
await expect(page.locator('.ant-tabs-tab')).toHaveCount({ min: 1 });

// Check for breadcrumbs or back navigation
const breadcrumbs = page.locator('.ant-breadcrumb, [data-testid="breadcrumbs"]');
const backButton = page.locator('.ant-btn:has(.anticon-arrow-left)');

// Verify at least one navigation element exists
const hasNavigation = await breadcrumbs.isVisible() || await backButton.isVisible();
expect(hasNavigation).toBeTruthy();
```

**UI Elements:**
- **Tab Container**: `.ant-tabs`
- **Tab Items**: `.ant-tabs-tab`
- **Breadcrumbs**: `.ant-breadcrumb`
- **Back Button**: `.ant-btn:has(.anticon-arrow-left)`

**Validation Points:**
- Tab navigation is available
- Multiple tabs are present
- Navigation elements are functional
- Page structure is properly rendered

### Phase 4: Navigate Job Detail Tabs

#### Step 4.1: Job Description Tab
```javascript
// Navigate to description tab (usually default)
const descriptionTab = page.locator('.ant-tabs-tab:has-text("Description"), [data-testid="description-tab"]');
if (await descriptionTab.isVisible()) {
  await descriptionTab.click();
  await page.waitForSelector('.ant-spin-spinning', { state: 'detached' });
  
  // Verify description content
  const descriptionContent = page.locator('.job-description, [data-testid="job-description"], .ant-card');
  await expect(descriptionContent).toBeVisible();
}
```

**UI Elements:**
- **Description Tab**: `.ant-tabs-tab:has-text("Description")`
- **Description Content**: `.job-description, [data-testid="job-description"]`
- **Job Title**: `h1, .ant-typography-title`

#### Step 4.2: Matching Candidates Tab
```javascript
// Navigate to matching candidates tab
const matchingTab = page.locator('.ant-tabs-tab:has-text("Matching"), [data-testid="matchings-tab"]');
if (await matchingTab.isVisible()) {
  await matchingTab.click();
  await page.waitForSelector('.ant-spin-spinning', { state: 'detached' });
  
  // Verify matching content
  const matchingContent = page.locator('.ant-table, .candidate-list, .matching-results');
  await expect(matchingContent).toBeVisible();
}
```

#### Step 4.3: Manual Matching Tab
```javascript
// Navigate to manual matching tab
const manualTab = page.locator('.ant-tabs-tab:has-text("Manual"), [data-testid="manual-matching-tab"]');
if (await manualTab.isVisible()) {
  await manualTab.click();
  await page.waitForSelector('.ant-spin-spinning', { state: 'detached' });
}
```

#### Step 4.4: Generate AI Interview Tab
```javascript
// Navigate to interview generation tab
const interviewTab = page.locator('.ant-tabs-tab:has-text("Interview"), [data-testid="interview-tab"]');
if (await interviewTab.isVisible()) {
  await interviewTab.click();
  await page.waitForSelector('.ant-spin-spinning', { state: 'detached' });
  
  // Verify interview generation interface
  const generateButton = page.locator('.ant-btn:has-text("Generate")');
  const candidateList = page.locator('.candidates-container, [data-testid="candidates-list"]');
  
  // At least one of these should be visible
  const hasInterviewInterface = await generateButton.isVisible() || await candidateList.isVisible();
  expect(hasInterviewInterface).toBeTruthy();
}
```

**Validation Points for All Tabs:**
- Tab switching works correctly
- Content loads for each tab
- Loading states are handled properly
- Tab-specific functionality is available

## 🚨 Common Issues and Solutions

### Issue 1: Jobs Not Loading
**Symptoms**: Empty table or loading spinner doesn't disappear
**Solutions**:
- Check backend API connectivity
- Verify database has job data
- Increase timeout for API calls
- Check console for network errors

### Issue 2: Search Not Working
**Symptoms**: Search input doesn't filter results
**Solutions**:
- Wait for debounced search (1-2 seconds)
- Check search API endpoint
- Verify search input selector
- Test with different search terms

### Issue 3: Navigation Failures
**Symptoms**: Clicking job doesn't navigate to details
**Solutions**:
- Verify job row click handler
- Check URL routing configuration
- Ensure job ID is valid
- Test with different jobs

### Issue 4: Tab Content Not Loading
**Symptoms**: Tabs switch but content doesn't load
**Solutions**:
- Wait for loading states to complete
- Check tab-specific API calls
- Verify content selectors
- Test individual tab functionality

## 📊 Performance Considerations

### Load Time Expectations
- **Initial Page Load**: < 3 seconds
- **Search Results**: < 2 seconds
- **Tab Switching**: < 1 second
- **Job Details Navigation**: < 2 seconds

### Optimization Strategies
- Use `waitForLoadState('networkidle')` for complete loading
- Implement proper timeout values
- Handle loading states explicitly
- Use efficient selectors

This workflow documentation provides comprehensive guidance for testing job application and position management functionality in the SmartHR application.
