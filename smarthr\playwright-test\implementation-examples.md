# SmartHR Playwright Implementation Examples

## Complete Test Implementation Examples

This document provides ready-to-use code examples for implementing SmartHR Playwright tests.

## Basic Test Structure

### Complete Generate New Questions Test
```typescript
import { test, expect } from '@playwright/test';
import { writeFileSync, mkdirSync } from 'fs';
import { join } from 'path';

interface GeneratedQuestion {
  number: string;
  question: string;
  expectedResponse: string;
  category?: string;
  index: number;
}

test('Generate New Questions for Salesforce QA', async ({ page }, testInfo) => {
  const projectName = testInfo.project.name;
  const isMobile = projectName.includes('Mobile');
  const startTime = Date.now();
  
  // Step 1: Navigate to homepage
  await page.goto('/');
  await page.waitForLoadState('networkidle');
  
  // Step 2: Go to jobs page
  const jobsLink = page.locator('a:has-text("Jobs"), .ant-menu-item:has-text("Jobs")');
  await jobsLink.first().click();
  await page.waitForLoadState('networkidle');
  
  // Step 3: Select Salesforce QA position
  const jobRows = page.locator('.ant-table-row, .job-row, .ant-card');
  const salesforceQAJob = jobRows.filter({ hasText: 'Salesforce QA' });
  
  if (await salesforceQAJob.count() > 0) {
    await salesforceQAJob.first().click();
  } else {
    throw new Error('Salesforce QA position not found');
  }
  
  // Step 4: Click Generate AI Interview button
  const generateAIButton = page.locator('#root > div > div > main > div > div:nth-child(2) > div:nth-child(1) > div > div:nth-child(4)');
  await generateAIButton.click();
  await page.waitForTimeout(3000);
  
  // Step 5: Click Generate New Questions button
  const generateNewQuestionsButton = page.locator('#root > div > div > main > div > div:nth-child(2) > div:nth-child(2) > div > div.ant-col.ant-col-xs-24.ant-col-md-14.css-dev-only-do-not-override-ni1kz0 > div:nth-child(1) > div > div > div:nth-child(1) > button');
  await generateNewQuestionsButton.click();
  
  // Step 6: Wait for generation to complete
  const loadingButton = page.locator('.ant-btn-loading:has-text("Generate AI Interview")');
  if (await loadingButton.isVisible({ timeout: 5000 })) {
    await loadingButton.waitFor({ state: 'hidden', timeout: 60000 });
  }
  
  // Step 7: Extract questions
  const questionElements = page.locator('.ant-card:has-text("?")');
  const questions: GeneratedQuestion[] = [];
  
  if (await questionElements.count() > 0) {
    const fullText = await questionElements.first().textContent();
    if (fullText) {
      const parsedQuestions = parseQuestionsAndAnswers(fullText);
      questions.push(...parsedQuestions);
    }
  }
  
  // Step 8: Save results
  const generatedQuestionsDir = join('generated-questions');
  mkdirSync(generatedQuestionsDir, { recursive: true });
  
  const questionsContent = generateQuestionsContent(questions, 'Salesforce QA');
  const questionsPath = join(generatedQuestionsDir, `Salesforce-QA-questions-${Date.now()}.md`);
  writeFileSync(questionsPath, questionsContent, 'utf8');
  
  console.log(`✅ Generated ${questions.length} questions for Salesforce QA`);
  expect(questions.length).toBeGreaterThan(0);
});

function parseQuestionsAndAnswers(fullText: string): GeneratedQuestion[] {
  const questions: GeneratedQuestion[] = [];
  const questionPattern = /(\d+)\.\s*([^]*?)(?=\d+\.\s|$)/g;
  let match;
  
  while ((match = questionPattern.exec(fullText)) !== null) {
    const questionNumber = match[1];
    const questionContent = match[2].trim();
    const parts = questionContent.split('Expected Response:');
    
    if (parts.length >= 2) {
      const questionText = parts[0].trim();
      const expectedResponse = parts[1].trim();
      
      questions.push({
        number: questionNumber,
        question: questionText,
        expectedResponse: expectedResponse,
        index: parseInt(questionNumber)
      });
    }
  }
  
  return questions;
}

function generateQuestionsContent(questions: GeneratedQuestion[], positionTitle: string): string {
  return `# ${positionTitle} - Interview Questions and Answers

**Generated**: ${new Date().toLocaleString()}
**Total Questions**: ${questions.length}

---

${questions.map(q => `## ${q.number}. ${q.question}

**Expected Response**: ${q.expectedResponse}

---
`).join('\n')}`;
}
```

## Reusable Helper Functions

### Position Selection Helper
```typescript
async function selectJobPosition(page: Page, positionName: string) {
  const jobRows = page.locator('.ant-table-row, .job-row, .ant-card, .position-item');
  
  // Try exact match first
  let targetJob = jobRows.filter({ hasText: positionName });
  
  if (await targetJob.count() === 0) {
    // Try partial matches
    const keywords = positionName.split(' ');
    for (const keyword of keywords) {
      targetJob = jobRows.filter({ hasText: keyword });
      if (await targetJob.count() > 0) {
        console.log(`Found position using keyword: ${keyword}`);
        break;
      }
    }
  }
  
  if (await targetJob.count() === 0) {
    throw new Error(`Position "${positionName}" not found`);
  }
  
  await targetJob.first().click();
  await page.waitForLoadState('networkidle');
  
  // Extract position ID from URL
  const url = page.url();
  const idMatch = url.match(/\/job\/([^\/\?]+)/);
  return idMatch ? idMatch[1] : 'unknown';
}
```

### Smart Loading Detection
```typescript
async function waitForInterviewGeneration(page: Page, timeoutMs: number = 60000) {
  console.log('⏰ Waiting for interview generation to complete...');
  
  // Look for Generate AI Interview button loading state
  const loadingButton = page.locator('.ant-btn-loading:has-text("Generate AI Interview")');
  
  if (await loadingButton.isVisible({ timeout: 5000 })) {
    console.log('🔄 Generation in progress...');
    
    // Wait for loading to complete
    await loadingButton.waitFor({ state: 'hidden', timeout: timeoutMs });
    
    // Additional wait for button to return to normal state
    const normalButton = page.locator('button:has-text("Generate AI Interview"):not(.ant-btn-loading)');
    await normalButton.waitFor({ state: 'visible', timeout: 10000 });
    
    console.log('✅ Generation completed');
  } else {
    console.log('ℹ️ No loading state detected, waiting 10 seconds...');
    await page.waitForTimeout(10000);
  }
}
```

### Mobile-Aware Interaction Helper
```typescript
async function smartClick(element: Locator, isMobile: boolean) {
  // Ensure element is visible and enabled
  await expect(element).toBeVisible();
  await expect(element).toBeEnabled();
  
  // Use appropriate interaction method
  if (isMobile) {
    await element.tap();
  } else {
    await element.click();
  }
}

async function smartScreenshot(page: Page, path: string, isMobile: boolean) {
  const options = isMobile ? {} : { fullPage: true };
  await page.screenshot({ path, ...options });
}
```

## Configuration Examples

### Playwright Config for SmartHR
```typescript
// playwright.config.ts
import { defineConfig, devices } from '@playwright/test';

export default defineConfig({
  testDir: './tests',
  timeout: 120000,  // 2 minutes for AI generation
  expect: { timeout: 10000 },
  
  use: {
    baseURL: 'http://localhost:5173',
    actionTimeout: 15000,
    navigationTimeout: 30000,
    screenshot: 'only-on-failure',
    video: 'retain-on-failure',
    trace: 'retain-on-failure',
  },
  
  projects: [
    {
      name: 'chromium',
      use: { ...devices['Desktop Chrome'] },
    },
    {
      name: 'Mobile Chrome',
      use: { ...devices['Pixel 5'] },
    },
    {
      name: 'Mobile Safari',
      use: { ...devices['iPhone 12'] },
    },
  ],
  
  // Global setup for backend health check
  globalSetup: require.resolve('./global-setup'),
});
```

### Global Setup Example
```typescript
// global-setup.ts
import { chromium, FullConfig } from '@playwright/test';

async function globalSetup(config: FullConfig) {
  console.log('🚀 Starting global setup for SmartHR E2E tests...');
  
  const browser = await chromium.launch();
  const page = await browser.newPage();
  
  try {
    // Check if frontend is accessible
    console.log('⏳ Checking frontend accessibility...');
    await page.goto('http://localhost:5173', { timeout: 30000 });
    
    // Check if backend is accessible
    console.log('⏳ Checking backend accessibility...');
    const response = await page.request.get('http://localhost:8080/health');
    if (!response.ok()) {
      throw new Error(`Backend health check failed: ${response.status()}`);
    }
    
    console.log('✅ Global setup completed successfully');
  } catch (error) {
    console.error('❌ Global setup failed:', error);
    throw error;
  } finally {
    await browser.close();
  }
}

export default globalSetup;
```

## Test Data Management

### Test Data Factory
```typescript
// test-data.ts
export interface TestPosition {
  name: string;
  alternatives: string[];
  expectedQuestionCount: number;
}

export const TEST_POSITIONS: TestPosition[] = [
  {
    name: 'Salesforce QA',
    alternatives: ['Salesforce Quality Assurance', 'QA Engineer - Salesforce'],
    expectedQuestionCount: 10
  },
  {
    name: 'Data Analyst',
    alternatives: ['Business Analyst', 'Data Scientist'],
    expectedQuestionCount: 10
  },
  {
    name: 'Frontend Developer',
    alternatives: ['React Developer', 'UI Developer'],
    expectedQuestionCount: 10
  }
];

export function getTestPosition(name: string): TestPosition | undefined {
  return TEST_POSITIONS.find(pos => 
    pos.name === name || pos.alternatives.includes(name)
  );
}
```

### Parameterized Tests
```typescript
// Multiple position testing
import { TEST_POSITIONS } from './test-data';

for (const position of TEST_POSITIONS) {
  test(`Generate questions for ${position.name}`, async ({ page }) => {
    const positionId = await selectJobPosition(page, position.name);
    
    // Generate questions
    await generateInterviewQuestions(page);
    
    // Validate results
    const questions = await extractQuestions(page);
    expect(questions.length).toBeGreaterThanOrEqual(position.expectedQuestionCount);
    
    // Save results
    await saveQuestions(questions, position.name, positionId);
  });
}
```

## Error Handling Examples

### Comprehensive Error Recovery
```typescript
async function robustButtonClick(page: Page, selector: string, description: string) {
  const maxAttempts = 3;
  
  for (let attempt = 1; attempt <= maxAttempts; attempt++) {
    try {
      console.log(`Attempting to click ${description} (attempt ${attempt}/${maxAttempts})`);
      
      const element = page.locator(selector);
      
      // Wait for element to be ready
      await element.waitFor({ state: 'visible', timeout: 10000 });
      await element.waitFor({ state: 'attached', timeout: 5000 });
      
      // Scroll into view if needed
      await element.scrollIntoViewIfNeeded();
      
      // Click the element
      await element.click();
      
      console.log(`✅ Successfully clicked ${description}`);
      return;
      
    } catch (error) {
      console.log(`❌ Attempt ${attempt} failed for ${description}:`, error.message);
      
      if (attempt === maxAttempts) {
        // Take screenshot for debugging
        await page.screenshot({ path: `error-${description.replace(/\s+/g, '-')}.png` });
        throw new Error(`Failed to click ${description} after ${maxAttempts} attempts: ${error.message}`);
      }
      
      // Wait before retry
      await page.waitForTimeout(2000);
    }
  }
}
```

### Graceful Test Degradation
```typescript
test('Generate questions with fallback strategies', async ({ page }) => {
  let questions: GeneratedQuestion[] = [];
  
  try {
    // Primary strategy: Full workflow
    questions = await fullGenerationWorkflow(page);
  } catch (error) {
    console.log('Primary workflow failed, trying alternative approach...');
    
    try {
      // Fallback strategy: Direct question extraction
      questions = await directQuestionExtraction(page);
    } catch (fallbackError) {
      console.log('Fallback failed, using mock data for testing...');
      questions = generateMockQuestions();
    }
  }
  
  // Validate we have some questions
  expect(questions.length).toBeGreaterThan(0);
  
  // Save results regardless of source
  await saveQuestions(questions, 'Test Position', 'test-id');
});
```

## Performance Optimization Examples

### Parallel Operations
```typescript
async function optimizedTestExecution(page: Page) {
  // Run independent operations in parallel
  const [positionId, screenshot] = await Promise.all([
    extractPositionId(page),
    page.screenshot({ path: 'position-page.png' })
  ]);
  
  // Batch DOM queries
  const [questionCount, categoryTags, loadingState] = await Promise.all([
    page.locator('.question-item').count(),
    page.locator('.ant-tag').allTextContents(),
    page.locator('.ant-btn-loading').isVisible()
  ]);
  
  return { positionId, questionCount, categoryTags, loadingState };
}
```

### Smart Waiting Strategies
```typescript
async function efficientWaiting(page: Page) {
  // Use Promise.race for timeout with early completion
  const result = await Promise.race([
    // Wait for success condition
    page.locator('.success-indicator').waitFor({ state: 'visible' }),
    
    // Wait for error condition
    page.locator('.error-message').waitFor({ state: 'visible' }).then(() => {
      throw new Error('Generation failed');
    }),
    
    // Maximum timeout
    new Promise((_, reject) => 
      setTimeout(() => reject(new Error('Operation timeout')), 60000)
    )
  ]);
  
  return result;
}
```

---

**Usage Notes:**
- Copy and adapt these examples for your specific needs
- Always test examples in your environment before production use
- Modify selectors and timeouts based on your application's behavior
- Add appropriate error handling for your use cases

*These examples are based on successful SmartHR test implementations and are continuously updated.*
