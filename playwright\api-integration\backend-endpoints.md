# SmartHR Backend API Endpoints Reference

This document provides a comprehensive reference of backend API endpoints used by the SmartHR application, including request/response formats, authentication requirements, and testing patterns.

## 📋 API Base Configuration

### Base URLs and Configuration
```javascript
// Environment configuration
const BASE_URL = process.env.VITE_BASE_API_URL || 'http://localhost:8080';
const LUMUS_URL = process.env.VITE_LUMUSAI_API_URL;

// Axios configuration
const api = axios.create({
  baseURL: BASE_URL,
  headers: {
    'Content-Type': 'application/json'
  }
});
```

### Authentication
```javascript
// Local development - authentication bypassed
const isLocalDev = window.location.hostname === 'localhost';

// Production authentication (when not local)
if (!isLocalDev) {
  const token = await getAuthToken();
  api.defaults.headers.common['Authorization'] = `Bearer ${token}`;
}
```

## 🏢 Job Position Management Endpoints

### Job Listings and Search
```javascript
// GET/POST /position/positions_pagination/
// Get paginated job positions with search and filtering

// Request format
const jobsRequest = {
  method: 'POST',
  url: '/position/positions_pagination/',
  params: {
    chunk_size: 10,
    page: 1
  },
  data: {
    stage: 'Active',           // Optional: filter by stage
    search_term: 'developer',  // Optional: search term
    client_name: 'ACME Corp',  // Optional: filter by client
    location: 'Remote',        // Optional: filter by location
    created_from: '2024-01-01', // Optional: date range
    created_to: '2024-12-31'   // Optional: date range
  }
};

// Response format
const jobsResponse = {
  data: [
    {
      id: 'job-uuid-123',
      job_title_pl__c: 'Senior Developer',
      position_info: {
        roleName: 'Senior Software Engineer',
        positionName: 'Full Stack Developer',
        clientName: 'ACME Corporation',
        positionAllocations: [
          { Name: 'Remote' },
          { Name: 'New York' }
        ]
      },
      created_at: '2024-01-15T10:30:00Z',
      updated_at: '2024-01-15T10:30:00Z'
    }
  ],
  total: 150,
  page: 1,
  chunk_size: 10,
  total_pages: 15
};
```

### Job Metadata Endpoints
```javascript
// GET /position/get/locations/
// Get available job locations
const locationsResponse = {
  data: ['Remote', 'New York', 'San Francisco', 'London']
};

// GET /position/get/clients/
// Get available client names
const clientsResponse = {
  data: ['ACME Corp', 'TechStart Inc', 'Global Solutions']
};
```

## 👥 Candidate Management Endpoints

### Candidate Listings and Search
```javascript
// GET/POST /candidate/candidates_pagination/
// Get paginated candidates with search and filtering

// Request format
const candidatesRequest = {
  method: 'POST',
  url: '/candidate/candidates_pagination/',
  params: {
    chunk_size: 10,
    page: 1
  },
  data: {
    search_term: 'john',      // Optional: search term
    country: 'US',            // Optional: filter by country
    role: 'Developer',        // Optional: filter by role
    status: 'active',         // Optional: filter by status
    created_from: '2024-01-01', // Optional: date range
    created_to: '2024-12-31'   // Optional: date range
  }
};

// Response format
const candidatesResponse = {
  data: [
    {
      id: 'candidate-uuid-456',
      candidate_info: {
        name: 'John Doe',
        email: '<EMAIL>',
        phone: '******-0123',
        location: 'New York',
        skills: ['JavaScript', 'React', 'Node.js'],
        experience: '5 years'
      },
      analysis_status: 'completed',
      is_active: true,
      created_at: '2024-01-10T09:15:00Z'
    }
  ],
  total: 75,
  page: 1,
  chunk_size: 10,
  total_pages: 8
};
```

### Candidate CRUD Operations
```javascript
// POST /candidate/
// Create new candidate
const createCandidateRequest = {
  proj_id: 'project-uuid',
  candidate_info: {
    name: 'Jane Smith',
    email: '<EMAIL>',
    skills: ['Python', 'Django', 'PostgreSQL']
  },
  created_by: '<EMAIL>'
};

// PUT /candidate/
// Update existing candidate
const updateCandidateRequest = {
  id: 'candidate-uuid-456',
  candidate_info: {
    name: 'John Doe Updated',
    skills: ['JavaScript', 'React', 'Node.js', 'TypeScript']
  },
  updated_by: '<EMAIL>'
};

// GET /candidate/{id}
// Get specific candidate details
const candidateDetailsResponse = {
  id: 'candidate-uuid-456',
  candidate_info: { /* full candidate data */ },
  suggested_positions: [
    {
      position_id: 'job-uuid-123',
      match_score: 0.85,
      match_reasons: ['Skills alignment', 'Experience level']
    }
  ],
  analysis_data: { /* AI analysis results */ }
};

// DELETE /candidate/{id}/delete
// Soft delete candidate (sets is_deleted = true)
```

### File Upload Endpoint
```javascript
// POST /candidate/uploadfiles/
// Upload candidate files (resumes, documents)

const uploadRequest = {
  method: 'POST',
  url: '/candidate/uploadfiles/',
  params: {
    project_id: 'project-uuid',
    created_by: '<EMAIL>'
  },
  headers: {
    'Content-Type': 'multipart/form-data'
  },
  data: formData // FormData object with files
};

const uploadResponse = {
  uploaded_files: [
    {
      filename: 'resume.pdf',
      candidate_id: 'candidate-uuid-789',
      status: 'processed'
    }
  ],
  duplicates: [
    {
      filename: 'existing_resume.pdf',
      existing_candidate_id: 'candidate-uuid-456'
    }
  ],
  errors: []
};
```

## 🎤 Interview Management Endpoints

### Interview Question Generation
```javascript
// POST /interview/{position_id}/questions
// Generate AI interview questions for a position

const generateQuestionsRequest = {
  method: 'POST',
  url: '/interview/position-uuid-123/questions',
  params: {
    n_questions: 10,
    include: 'Technical Skills,Soft Skills,Methodologies',
    current_user: '<EMAIL>'
  }
};

const questionsResponse = {
  questions: [
    {
      question_number: 1,
      question_text: 'Explain the concept of closures in JavaScript',
      category: 'Technical Skills',
      junior_answer: 'Basic explanation of closures...',
      mid_answer: 'Intermediate explanation with examples...',
      senior_answer: 'Advanced explanation with use cases...'
    }
  ],
  position_id: 'position-uuid-123',
  generated_at: '2024-01-15T14:30:00Z'
};

// GET /interview/{position_id}/questions
// Retrieve generated questions for a position
```

### Interview Creation and Management
```javascript
// POST /interview/{position_id}/create
// Create interviews for selected candidates

const createInterviewsRequest = {
  method: 'POST',
  url: '/interview/position-uuid-123/create',
  data: [
    {
      candidate_id: 'candidate-uuid-456',
      analysis_data: {
        match_score: 0.85,
        recommended_questions: [1, 3, 5, 7, 9]
      }
    }
  ]
};

const interviewsResponse = {
  created_interviews: [
    {
      id: 'interview-uuid-789',
      position_id: 'position-uuid-123',
      candidate_id: 'candidate-uuid-456',
      status_hr: 'not_scheduled',
      status_tec: 'not_scheduled',
      created_at: '2024-01-15T15:00:00Z'
    }
  ]
};
```

### Interview Feedback Submission
```javascript
// PUT /interview/hr
// Submit HR interview feedback

const hrFeedbackRequest = {
  position_id: 'position-uuid-123',
  candidate_id: 'candidate-uuid-456',
  recruiter_hr_id: 'John Smith',
  scheduled_hr_id: 'Jane Doe',
  feedback_hr: {
    communication: 'excellent',
    cultural_fit: 'good',
    motivation: 'high'
  },
  interview_date_hr: '2024-01-20T10:00:00Z',
  feedback_date_hr: '2024-01-20T11:00:00Z',
  status_hr: 'completed',
  recommendation_hr: true,
  transcript_hr: 'Interview transcript content...'
};

// PUT /interview/tec
// Submit Technical interview feedback

const techFeedbackRequest = {
  position_id: 'position-uuid-123',
  candidate_id: 'candidate-uuid-456',
  recruiter_tec_id: 'Mike Johnson',
  scheduled_tec_id: 'Sarah Wilson',
  feedback_tec: {
    technical_skills: 'strong',
    problem_solving: 'excellent',
    code_quality: 'good'
  },
  interview_date_tec: '2024-01-22T14:00:00Z',
  feedback_date_tec: '2024-01-22T15:30:00Z',
  status_tec: 'completed',
  recommendation_tec: true,
  transcript_tec: 'Technical interview transcript...'
};
```

## 🔍 Matching and Analysis Endpoints

### Candidate-Position Matching
```javascript
// POST /match
// Match candidates to positions or positions to candidates

const matchRequest = {
  method: 'POST',
  url: '/match',
  params: {
    position_id: 'position-uuid-123', // OR candidate_id
    limit: 5,
    hasFeedback: 2, // 0=without, 1=with, 2=both
    batch_mode: true
  }
};

const matchResponse = {
  matches: [
    {
      candidate_id: 'candidate-uuid-456',
      match_score: 0.85,
      compatibility_analysis: {
        percentage_of_match: 85,
        recommendation: 'Highly Recommended',
        matches_found: ['JavaScript', 'React', 'Node.js'],
        missing_requirements: ['TypeScript', 'AWS']
      }
    }
  ],
  position_id: 'position-uuid-123',
  total_matches: 12
};

// POST /match/custom
// Custom matching with specific prompts
const customMatchRequest = {
  position_id: 'position-uuid-123',
  custom_prompt: 'Focus on leadership experience and team management skills',
  limit: 3
};
```

## 🚨 Error Response Patterns

### Standard Error Format
```javascript
// HTTP 400 - Bad Request
const badRequestError = {
  detail: 'Invalid request parameters',
  status_code: 400,
  error_type: 'ValidationError'
};

// HTTP 404 - Not Found
const notFoundError = {
  detail: 'Resource not found',
  status_code: 404,
  error_type: 'NotFoundError'
};

// HTTP 422 - Validation Error
const validationError = {
  detail: [
    {
      field: 'email',
      message: 'Invalid email format'
    },
    {
      field: 'n_questions',
      message: 'Must be between 1 and 20'
    }
  ],
  status_code: 422,
  error_type: 'ValidationError'
};

// HTTP 500 - Internal Server Error
const serverError = {
  detail: 'Internal server error',
  status_code: 500,
  error_type: 'InternalServerError'
};
```

## 🧪 API Testing Patterns

### Mocking API Responses
```javascript
// Mock successful API response
await page.route('**/api/position/positions_pagination/', route => {
  route.fulfill({
    status: 200,
    contentType: 'application/json',
    body: JSON.stringify(mockJobsResponse)
  });
});

// Mock API error response
await page.route('**/api/interview/*/questions', route => {
  route.fulfill({
    status: 500,
    contentType: 'application/json',
    body: JSON.stringify({
      detail: 'AI service unavailable',
      status_code: 500
    })
  });
});
```

### Validating API Calls
```javascript
// Capture and validate API requests
const apiRequests = [];
page.on('request', request => {
  if (request.url().includes('/api/')) {
    apiRequests.push({
      url: request.url(),
      method: request.method(),
      postData: request.postData(),
      headers: request.headers()
    });
  }
});

// After test actions, validate API calls
const createCandidateRequest = apiRequests.find(
  req => req.method === 'POST' && req.url.includes('/candidate/')
);
expect(createCandidateRequest).toBeDefined();
expect(JSON.parse(createCandidateRequest.postData)).toMatchObject({
  candidate_info: expect.objectContaining({
    name: expect.any(String),
    email: expect.any(String)
  })
});
```

This comprehensive API endpoints reference provides detailed information about all backend integrations used in the SmartHR application for effective automated testing.
