# Candidate Management and Selection Workflow

This document provides comprehensive step-by-step instructions for testing candidate management and selection workflows in the SmartHR application, including adding candidates to interviews and managing candidate selection.

## 📋 Overview

### Purpose
Test the complete candidate management functionality including candidate viewing, selection for interviews, and candidate-job matching processes.

### Scope
- Navigate candidate listings and profiles
- Search and filter candidates
- Add candidates to interview processes
- Manage candidate selection and matching
- View candidate details and analysis

### Prerequisites
- Application running on `http://localhost:5173`
- Backend API available on `http://localhost:8080`
- Test data: Candidates and job positions in database
- Authentication bypassed (local development mode)

### Expected Outcomes
- Successfully navigate candidate management interface
- Select and manage candidates for interviews
- View candidate profiles and matching data
- Handle candidate selection workflows

## 🔄 Step-by-Step Workflow

### Phase 1: Navigate to Candidate Management

#### Step 1.1: Access Candidates Page
```javascript
// Navigate to candidates page
await page.goto('http://localhost:5173/candidates');
await page.waitForLoadState('networkidle');

// Wait for candidates table to load
await page.waitForSelector('.ant-table', { timeout: 10000 });
```

**UI Elements:**
- **URL**: `http://localhost:5173/candidates`
- **Candidates Table**: `.ant-table`
- **Loading Indicator**: `.ant-spin-spinning`

**Validation Points:**
- Candidates page loads successfully
- Table structure is rendered
- Candidate data is displayed or empty state shown

#### Step 1.2: Verify Candidates Display
```javascript
// Check candidates table structure
await expect(page.locator('.ant-table-thead')).toBeVisible();
await expect(page.locator('.ant-table-tbody')).toBeVisible();

// Count available candidates
const candidateRows = page.locator('.ant-table-tbody .ant-table-row');
const candidateCount = await candidateRows.count();
console.log(`Found ${candidateCount} candidates`);

// Check for empty state if no candidates
if (candidateCount === 0) {
  await expect(page.locator('.ant-empty')).toBeVisible();
}
```

**UI Elements:**
- **Table Header**: `.ant-table-thead`
- **Candidate Rows**: `.ant-table-row`
- **Empty State**: `.ant-empty`

### Phase 2: Search and Filter Candidates

#### Step 2.1: Use Candidate Search
```javascript
// Locate search input
const searchInput = page.locator('input[placeholder*="Search"], .ant-input[placeholder*="candidate"]');
if (await searchInput.isVisible()) {
  // Perform search
  await searchInput.fill('Software Engineer');
  await searchInput.press('Enter');
  
  // Wait for search results
  await page.waitForSelector('.ant-spin-spinning', { state: 'detached' });
  await page.waitForTimeout(1000);
}
```

#### Step 2.2: Apply Candidate Filters
```javascript
// Role filter
const roleFilter = page.locator('.ant-select:has(.ant-select-selection-item:has-text("Role"))');
if (await roleFilter.isVisible()) {
  await roleFilter.click();
  await page.click('.ant-select-item-option:has-text("Developer")');
}

// Country filter
const countryFilter = page.locator('.ant-select:has(.ant-select-selection-item:has-text("Country"))');
if (await countryFilter.isVisible()) {
  await countryFilter.click();
  const countryOptions = page.locator('.ant-select-item-option');
  if (await countryOptions.count() > 0) {
    await countryOptions.first().click();
  }
}

// Apply filters
const applyButton = page.locator('.ant-btn:has-text("Apply")');
if (await applyButton.isVisible()) {
  await applyButton.click();
  await page.waitForSelector('.ant-spin-spinning', { state: 'detached' });
}
```

**UI Elements:**
- **Role Filter**: `.ant-select` (role dropdown)
- **Country Filter**: `.ant-select` (country dropdown)
- **Status Filter**: `.ant-select` (status dropdown)
- **Apply Button**: `.ant-btn:has-text("Apply")`

### Phase 3: View Candidate Details

#### Step 3.1: Navigate to Candidate Profile
```javascript
// Click on first candidate to view details
const candidateRows = page.locator('.ant-table-tbody .ant-table-row');
if (await candidateRows.count() > 0) {
  const firstCandidate = candidateRows.first();
  
  // Get candidate name before clicking
  const candidateName = await firstCandidate.locator('td').first().textContent();
  console.log('Viewing candidate:', candidateName);
  
  // Click to navigate to candidate details
  await firstCandidate.click();
  
  // Wait for navigation to candidate details
  await page.waitForURL('**/candidates/**');
  await page.waitForSelector('.ant-card, [data-testid="candidate-details"]', { timeout: 10000 });
}
```

**UI Elements:**
- **Candidate Row**: `.ant-table-row`
- **Candidate Details**: `.ant-card, [data-testid="candidate-details"]`

#### Step 3.2: Review Candidate Information
```javascript
// Verify candidate details page structure
await expect(page.locator('.ant-card')).toBeVisible();

// Check for candidate information sections
const candidateInfo = page.locator('.candidate-info, [data-testid="candidate-info"]');
const candidateSkills = page.locator('.candidate-skills, .skills-section');
const candidateExperience = page.locator('.candidate-experience, .experience-section');

// Verify at least basic information is displayed
const hasBasicInfo = await candidateInfo.isVisible() || 
                    await page.locator('.ant-descriptions').isVisible();
expect(hasBasicInfo).toBeTruthy();
```

### Phase 4: Add Candidates to Interviews

#### Step 4.1: Navigate to Job Details for Interview Setup
```javascript
// Navigate back to job details to add candidates to interviews
// (This assumes we have a job ID - replace with actual job ID)
const jobId = 'your-job-id-here';
await page.goto(`http://localhost:5173/job/${jobId}`);
await page.waitForLoadState('networkidle');

// Navigate to matching candidates tab
const matchingTab = page.locator('.ant-tabs-tab:has-text("Matching"), .ant-tabs-tab:has-text("Candidates")');
if (await matchingTab.isVisible()) {
  await matchingTab.click();
  await page.waitForSelector('.ant-spin-spinning', { state: 'detached' });
}
```

#### Step 4.2: View Available Candidates for Position
```javascript
// Check for candidate matching results
const candidateTable = page.locator('.ant-table, .candidate-list');
await expect(candidateTable).toBeVisible({ timeout: 10000 });

// Count available candidates for this position
const availableCandidates = page.locator('.ant-table-row, .candidate-item');
const availableCount = await availableCandidates.count();
console.log(`Found ${availableCount} candidates available for this position`);
```

#### Step 4.3: Select Candidates for Interview
```javascript
// Select candidates using checkboxes or selection mechanism
const candidateCheckboxes = page.locator('.ant-checkbox, input[type="checkbox"]');
const checkboxCount = await candidateCheckboxes.count();

if (checkboxCount > 0) {
  // Select first few candidates (up to 3)
  const selectCount = Math.min(checkboxCount, 3);
  
  for (let i = 0; i < selectCount; i++) {
    const checkbox = candidateCheckboxes.nth(i);
    if (await checkbox.isVisible() && !await checkbox.isChecked()) {
      await checkbox.check();
      console.log(`Selected candidate ${i + 1}`);
    }
  }
  
  // Verify selections
  const selectedCount = await page.locator('.ant-checkbox:checked').count();
  expect(selectedCount).toBeGreaterThan(0);
  console.log(`Selected ${selectedCount} candidates for interview`);
}
```

**UI Elements:**
- **Candidate Checkboxes**: `.ant-checkbox, input[type="checkbox"]`
- **Select All**: `.ant-checkbox:has-text("Select All")`
- **Selection Counter**: `.selection-count, .selected-count`

#### Step 4.4: Create Interviews for Selected Candidates
```javascript
// Look for create interview button
const createInterviewButton = page.locator('button:has-text("Create Interview"), .ant-btn:has-text("Interview")');

if (await createInterviewButton.isVisible()) {
  await createInterviewButton.click();
  
  // Handle interview creation modal/form
  const interviewModal = page.locator('.ant-modal, .ant-drawer');
  if (await interviewModal.isVisible()) {
    // Fill interview details if form is present
    const interviewForm = interviewModal.locator('.ant-form');
    if (await interviewForm.isVisible()) {
      // Set interview type if available
      const typeSelect = interviewForm.locator('.ant-select');
      if (await typeSelect.isVisible()) {
        await typeSelect.click();
        await page.click('.ant-select-item-option:has-text("Technical")');
      }
      
      // Set interview date if available
      const dateInput = interviewForm.locator('.ant-picker, input[type="date"]');
      if (await dateInput.isVisible()) {
        const tomorrow = new Date();
        tomorrow.setDate(tomorrow.getDate() + 1);
        const dateString = tomorrow.toISOString().split('T')[0];
        await dateInput.fill(dateString);
      }
    }
    
    // Submit interview creation
    const submitButton = interviewModal.locator('button:has-text("Create"), .ant-btn-primary');
    if (await submitButton.isVisible()) {
      await submitButton.click();
      
      // Wait for success notification
      await page.waitForSelector('.ant-notification-success', { timeout: 10000 });
      console.log('Interviews created successfully');
    }
  }
}
```

### Phase 5: Manage Selected Candidates in Interview Tab

#### Step 5.1: Navigate to Interview Generation Tab
```javascript
// Navigate to interview tab to see selected candidates
const interviewTab = page.locator('.ant-tabs-tab:has-text("Interview"), .ant-tabs-tab:has-text("Generate")');
if (await interviewTab.isVisible()) {
  await interviewTab.click();
  await page.waitForSelector('.ant-spin-spinning', { state: 'detached' });
}
```

#### Step 5.2: View Selected Candidates List
```javascript
// Look for candidate list in interview tab
const candidateList = page.locator('.candidates-container, [data-testid="candidates-list"]');
if (await candidateList.isVisible()) {
  console.log('Candidate list is available in interview tab');
  
  // Count selected candidates
  const candidateCards = page.locator('.candidate-card, .ant-card:has([data-testid="candidate-card"])');
  const selectedCount = await candidateCards.count();
  console.log(`Found ${selectedCount} selected candidates`);
  
  // Verify candidate information is displayed
  if (selectedCount > 0) {
    const firstCandidate = candidateCards.first();
    const candidateInfo = await firstCandidate.textContent();
    expect(candidateInfo.trim()).toBeTruthy();
    console.log('First candidate info:', candidateInfo.substring(0, 100));
  }
}
```

**UI Elements:**
- **Candidate Container**: `.candidates-container`
- **Candidate Cards**: `.candidate-card, .ant-card`
- **Candidate Count Badge**: `.count-badge`

#### Step 5.3: Interact with Individual Candidates
```javascript
// Click on a candidate to view details or provide feedback
const candidateCards = page.locator('.candidate-card');
if (await candidateCards.count() > 0) {
  const firstCandidate = candidateCards.first();
  await firstCandidate.click();
  
  // Check if candidate detail drawer/modal opens
  const candidateDrawer = page.locator('.ant-drawer, .ant-modal');
  if (await candidateDrawer.isVisible()) {
    console.log('Candidate detail drawer opened');
    
    // Look for feedback tabs (HR/Technical)
    const feedbackTabs = candidateDrawer.locator('.ant-tabs-tab');
    const tabCount = await feedbackTabs.count();
    console.log(`Found ${tabCount} feedback tabs`);
    
    // Close drawer
    const closeButton = candidateDrawer.locator('.ant-drawer-close, .ant-modal-close');
    if (await closeButton.isVisible()) {
      await closeButton.click();
    }
  }
}
```

## 🚨 Common Issues and Solutions

### Issue 1: Candidates Not Loading
**Symptoms**: Empty candidate table or loading spinner persists
**Solutions**:
- Check backend API connectivity
- Verify database has candidate data
- Increase timeout for API calls
- Check console for network errors

### Issue 2: Candidate Selection Not Working
**Symptoms**: Checkboxes don't respond or selections don't persist
**Solutions**:
- Verify checkbox selectors are correct
- Check for JavaScript errors in console
- Ensure proper event handling
- Test with different candidates

### Issue 3: Interview Creation Fails
**Symptoms**: Create interview button doesn't work or modal doesn't appear
**Solutions**:
- Verify candidates are properly selected
- Check interview creation API endpoint
- Ensure required fields are filled
- Validate interview data format

### Issue 4: Candidate Details Not Displaying
**Symptoms**: Candidate profile page is empty or doesn't load
**Solutions**:
- Verify candidate ID is valid
- Check candidate data in database
- Test navigation to candidate details
- Validate candidate information API

## 📊 Performance Considerations

### Load Time Expectations
- **Candidates Page Load**: < 3 seconds
- **Candidate Search**: < 2 seconds
- **Candidate Details**: < 2 seconds
- **Interview Creation**: < 5 seconds

### Optimization Strategies
- Use proper loading state handling
- Implement efficient candidate filtering
- Cache candidate data when appropriate
- Use pagination for large candidate lists

This workflow documentation provides comprehensive guidance for testing candidate management and selection functionality in the SmartHR application.
