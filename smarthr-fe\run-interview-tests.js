#!/usr/bin/env node

/**
 * Test Runner for Interview Question Generation Tests
 * 
 * This script demonstrates how to run the automated interview question generation tests
 * that were created using the comprehensive documentation in the playwright folder.
 * 
 * Usage:
 *   node run-interview-tests.js [test-type]
 * 
 * Test types:
 *   - basic: Run basic interview question generation test
 *   - comprehensive: Run comprehensive workflow test
 *   - all: Run all interview-related tests
 */

const { spawn } = require('child_process');
const path = require('path');

// Test configurations
const TEST_CONFIGS = {
  basic: {
    name: 'Basic Interview Question Generation',
    file: 'tests/e2e/interview-question-generation.spec.ts',
    description: 'Tests basic question generation workflow with different configurations'
  },
  comprehensive: {
    name: 'Comprehensive Interview Workflow',
    file: 'tests/e2e/comprehensive-interview-workflow.spec.ts',
    description: 'Tests complete workflow with advanced features and error handling'
  },
  existing: {
    name: 'Existing Interview Management Tests',
    file: 'tests/e2e/interview-management.spec.ts',
    description: 'Original interview management tests for comparison'
  }
};

function printHeader() {
  console.log('🎭 SmartHR Interview Question Generation Test Runner');
  console.log('=' .repeat(60));
  console.log('');
  console.log('This test runner executes automated tests for interview question');
  console.log('generation using the comprehensive documentation created in the');
  console.log('playwright folder.');
  console.log('');
}

function printUsage() {
  console.log('Usage: node run-interview-tests.js [test-type]');
  console.log('');
  console.log('Available test types:');
  
  Object.entries(TEST_CONFIGS).forEach(([key, config]) => {
    console.log(`  ${key.padEnd(12)} - ${config.description}`);
  });
  
  console.log('  all          - Run all interview-related tests');
  console.log('');
  console.log('Examples:');
  console.log('  node run-interview-tests.js basic');
  console.log('  node run-interview-tests.js comprehensive');
  console.log('  node run-interview-tests.js all');
  console.log('');
}

function runPlaywrightTest(testFile, testName) {
  return new Promise((resolve, reject) => {
    console.log(`🚀 Running ${testName}...`);
    console.log(`📁 Test file: ${testFile}`);
    console.log('');
    
    const npxPath = process.platform === 'win32' ? 'npx.cmd' : 'npx';
    const playwrightProcess = spawn(npxPath, ['playwright', 'test', testFile, '--reporter=line'], {
      cwd: process.cwd(),
      stdio: 'inherit'
    });
    
    playwrightProcess.on('close', (code) => {
      console.log('');
      if (code === 0) {
        console.log(`✅ ${testName} completed successfully`);
        resolve();
      } else {
        console.log(`❌ ${testName} failed with exit code ${code}`);
        reject(new Error(`Test failed with exit code ${code}`));
      }
      console.log('-'.repeat(60));
    });
    
    playwrightProcess.on('error', (error) => {
      console.error(`❌ Failed to start ${testName}:`, error.message);
      reject(error);
    });
  });
}

async function runTests(testType) {
  printHeader();
  
  if (!testType || testType === 'help' || testType === '--help' || testType === '-h') {
    printUsage();
    return;
  }
  
  const startTime = Date.now();
  
  try {
    if (testType === 'all') {
      console.log('🎯 Running all interview-related tests...');
      console.log('');
      
      for (const [key, config] of Object.entries(TEST_CONFIGS)) {
        await runPlaywrightTest(config.file, config.name);
      }
    } else if (TEST_CONFIGS[testType]) {
      const config = TEST_CONFIGS[testType];
      await runPlaywrightTest(config.file, config.name);
    } else {
      console.error(`❌ Unknown test type: ${testType}`);
      console.log('');
      printUsage();
      process.exit(1);
    }
    
    const duration = Date.now() - startTime;
    console.log('');
    console.log('🎉 All tests completed successfully!');
    console.log(`⏱️  Total execution time: ${Math.round(duration / 1000)}s`);
    console.log('');
    console.log('📋 Test Summary:');
    console.log('- Interview question generation workflow validated');
    console.log('- UI interactions and selectors working correctly');
    console.log('- Error handling and edge cases covered');
    console.log('- Page object models functioning properly');
    console.log('');
    console.log('✨ The playwright documentation has been successfully validated!');
    
  } catch (error) {
    const duration = Date.now() - startTime;
    console.log('');
    console.log('❌ Test execution failed');
    console.log(`⏱️  Execution time: ${Math.round(duration / 1000)}s`);
    console.log(`🔍 Error: ${error.message}`);
    console.log('');
    console.log('💡 Troubleshooting tips:');
    console.log('1. Ensure the SmartHR application is running locally');
    console.log('2. Check that the database is properly initialized');
    console.log('3. Verify Playwright is installed: npm install @playwright/test');
    console.log('4. Run Playwright setup: npx playwright install');
    console.log('5. Check the test configuration in playwright.config.ts');
    console.log('');
    process.exit(1);
  }
}

// Handle command line arguments
const testType = process.argv[2];

// Run the tests
runTests(testType).catch((error) => {
  console.error('Unexpected error:', error);
  process.exit(1);
});

// Handle process termination
process.on('SIGINT', () => {
  console.log('');
  console.log('🛑 Test execution interrupted by user');
  process.exit(0);
});

process.on('SIGTERM', () => {
  console.log('');
  console.log('🛑 Test execution terminated');
  process.exit(0);
});
