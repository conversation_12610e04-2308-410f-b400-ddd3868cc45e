# SmartHR Advanced Test Scenarios and Edge Cases

This document provides comprehensive advanced test scenarios, edge cases, error handling, and validation scenarios for thorough testing of the SmartHR application.

## 📋 Advanced Testing Strategy

### Edge Case Categories
1. **Data Boundary Testing** - Testing limits and edge values
2. **Concurrent User Actions** - Multiple simultaneous operations
3. **Network Instability** - Connection issues and timeouts
4. **Large Dataset Handling** - Performance with high data volumes
5. **Browser Compatibility** - Cross-browser behavior differences
6. **State Corruption** - Invalid or corrupted application state

## 🔄 Concurrent Operations Test Scenarios

### Simultaneous Question Generation
```javascript
test('Concurrent question generation requests', async ({ browser }) => {
  // Create multiple browser contexts for concurrent testing
  const contexts = await Promise.all([
    browser.newContext(),
    browser.newContext(),
    browser.newContext()
  ]);

  const pages = await Promise.all(contexts.map(context => context.newPage()));

  try {
    // Navigate all pages to the same job
    const jobId = 'test-job-id';
    await Promise.all(pages.map(page => 
      page.goto(`http://localhost:5173/job/${jobId}`)
    ));

    // Navigate to interview tab on all pages
    await Promise.all(pages.map(async page => {
      await page.waitForSelector('.ant-tabs-tab:has-text("Interview")');
      await page.click('.ant-tabs-tab:has-text("Interview")');
      await page.waitForSelector('.ant-spin-spinning', { state: 'detached' });
    }));

    // Simultaneously trigger question generation
    const generatePromises = pages.map(async (page, index) => {
      const questionCount = 5 + index; // Different counts for each request
      
      const countInput = page.locator('.ant-input-number input');
      if (await countInput.isVisible()) {
        await countInput.fill(questionCount.toString());
      }

      const generateButton = page.locator('button:has-text("Generate")');
      if (await generateButton.isVisible()) {
        await generateButton.click();
        
        // Wait for completion or error
        try {
          await page.waitForSelector('.ant-notification-success, .ant-notification-error', { timeout: 30000 });
          return { success: true, page: index };
        } catch (error) {
          return { success: false, page: index, error: error.message };
        }
      }
    });

    const results = await Promise.all(generatePromises);
    
    // Analyze results
    const successful = results.filter(r => r.success).length;
    const failed = results.filter(r => !r.success).length;
    
    console.log(`Concurrent generation results: ${successful} successful, ${failed} failed`);
    
    // At least one should succeed, others should handle gracefully
    expect(successful).toBeGreaterThanOrEqual(1);
    
  } finally {
    // Cleanup
    await Promise.all(contexts.map(context => context.close()));
  }
});
```

### Concurrent Feedback Submission
```javascript
test('Concurrent feedback submission for same candidate', async ({ browser }) => {
  const contexts = await Promise.all([browser.newContext(), browser.newContext()]);
  const pages = await Promise.all(contexts.map(context => context.newPage()));

  try {
    // Both pages navigate to same candidate feedback
    await Promise.all(pages.map(async page => {
      await page.goto('http://localhost:5173/job/test-job-id');
      await page.click('.ant-tabs-tab:has-text("Interview")');
      await page.click('.candidate-card:first-child');
      await page.waitForSelector('.ant-drawer');
    }));

    // One submits HR feedback, other submits Technical feedback simultaneously
    const feedbackPromises = pages.map(async (page, index) => {
      const tabName = index === 0 ? 'HR' : 'Technical';
      const tab = page.locator(`.ant-tabs-tab:has-text("${tabName}")`);
      
      if (await tab.isVisible()) {
        await tab.click();
        
        // Fill form quickly
        await fillFeedbackFormQuickly(page, {
          recruiter: `Recruiter ${index + 1}`,
          status: 'completed',
          recommendation: 'Yes',
          comments: `Concurrent test feedback ${index + 1}`
        });
        
        // Submit simultaneously
        const submitButton = page.locator('button:has-text("Save")');
        await submitButton.click();
        
        try {
          await page.waitForSelector('.ant-notification-success', { timeout: 10000 });
          return { success: true, type: tabName };
        } catch (error) {
          return { success: false, type: tabName, error: error.message };
        }
      }
    });

    const results = await Promise.all(feedbackPromises);
    
    // Both should succeed as they're different feedback types
    results.forEach(result => {
      expect(result.success).toBeTruthy();
    });
    
  } finally {
    await Promise.all(contexts.map(context => context.close()));
  }
});
```

## 📊 Large Dataset Test Scenarios

### Performance with Large Job Lists
```javascript
test('Performance with large job dataset', async ({ page }) => {
  // Mock large dataset response
  await page.route('**/api/position/positions_pagination/', route => {
    const largeDataset = {
      data: Array.from({ length: 1000 }, (_, i) => ({
        id: `job-${i}`,
        job_title_pl__c: `Job Title ${i}`,
        position_info: {
          roleName: `Role ${i}`,
          clientName: `Client ${i % 10}`,
          positionAllocations: [{ Name: `Location ${i % 5}` }]
        },
        created_at: new Date().toISOString()
      })),
      total: 10000,
      page: 1,
      chunk_size: 1000,
      total_pages: 10
    };
    
    route.fulfill({
      status: 200,
      contentType: 'application/json',
      body: JSON.stringify(largeDataset)
    });
  });

  const startTime = Date.now();
  
  await page.goto('http://localhost:5173');
  await page.waitForSelector('.ant-table', { timeout: 15000 });
  
  const loadTime = Date.now() - startTime;
  console.log(`Large dataset loaded in ${loadTime}ms`);
  
  // Performance expectations
  expect(loadTime).toBeLessThan(10000); // 10 second budget
  
  // Test scrolling performance
  const scrollStartTime = Date.now();
  await page.evaluate(() => {
    const table = document.querySelector('.ant-table-tbody');
    if (table) {
      table.scrollTop = table.scrollHeight;
    }
  });
  
  const scrollTime = Date.now() - scrollStartTime;
  expect(scrollTime).toBeLessThan(1000); // 1 second for scrolling
  
  // Test search performance with large dataset
  const searchStartTime = Date.now();
  await page.fill('input[placeholder*="Search"]', 'Job Title 500');
  await page.press('input[placeholder*="Search"]', 'Enter');
  await page.waitForSelector('.ant-spin-spinning', { state: 'detached' });
  
  const searchTime = Date.now() - searchStartTime;
  expect(searchTime).toBeLessThan(3000); // 3 second budget for search
});
```

### Memory Leak Detection
```javascript
test('Memory usage during extended operations', async ({ page }) => {
  // Monitor memory usage
  const getMemoryUsage = async () => {
    return await page.evaluate(() => {
      if (performance.memory) {
        return {
          used: performance.memory.usedJSHeapSize,
          total: performance.memory.totalJSHeapSize,
          limit: performance.memory.jsHeapSizeLimit
        };
      }
      return null;
    });
  };

  await page.goto('http://localhost:5173');
  const initialMemory = await getMemoryUsage();
  
  // Perform memory-intensive operations
  for (let i = 0; i < 10; i++) {
    // Navigate between pages
    await page.goto('http://localhost:5173/candidates');
    await page.waitForSelector('.ant-table, .ant-empty');
    
    await page.goto('http://localhost:5173');
    await page.waitForSelector('.ant-table, .ant-empty');
    
    // Trigger data loading
    if (await page.locator('.ant-table-row').count() > 0) {
      await page.click('.ant-table-row:first-child');
      await page.waitForURL('**/job/**');
      await page.goBack();
      await page.waitForURL('/');
    }
  }

  const finalMemory = await getMemoryUsage();
  
  if (initialMemory && finalMemory) {
    const memoryIncrease = finalMemory.used - initialMemory.used;
    const memoryIncreasePercent = (memoryIncrease / initialMemory.used) * 100;
    
    console.log(`Memory increase: ${memoryIncrease} bytes (${memoryIncreasePercent.toFixed(2)}%)`);
    
    // Memory increase should be reasonable (less than 50% increase)
    expect(memoryIncreasePercent).toBeLessThan(50);
  }
});
```

## 🌐 Network Instability Test Scenarios

### Intermittent Connection Issues
```javascript
test('Handling intermittent network failures', async ({ page }) => {
  let requestCount = 0;
  
  // Simulate intermittent failures (fail every 3rd request)
  await page.route('**/api/**', route => {
    requestCount++;
    
    if (requestCount % 3 === 0) {
      // Simulate network timeout
      setTimeout(() => route.abort(), 5000);
    } else {
      route.continue();
    }
  });

  await page.goto('http://localhost:5173');
  
  // Perform multiple operations that trigger API calls
  const operations = [
    () => page.fill('input[placeholder*="Search"]', 'test'),
    () => page.press('input[placeholder*="Search"]', 'Enter'),
    () => page.click('.ant-pagination-next'),
    () => page.selectOption('.ant-select', { index: 0 })
  ];

  for (const operation of operations) {
    try {
      await operation();
      await page.waitForTimeout(2000); // Allow time for request
      
      // Check for error notifications or retry mechanisms
      const hasError = await page.locator('.ant-notification-error').isVisible();
      const hasRetry = await page.locator('button:has-text("Retry")').isVisible();
      
      if (hasError || hasRetry) {
        console.log('Network error handled gracefully');
      }
      
    } catch (error) {
      console.log(`Operation failed gracefully: ${error.message}`);
    }
  }
});
```

### Slow Network Conditions
```javascript
test('Performance under slow network conditions', async ({ page }) => {
  // Simulate slow network (2G connection)
  await page.route('**/api/**', async route => {
    // Add 3-5 second delay to simulate slow connection
    const delay = 3000 + Math.random() * 2000;
    await new Promise(resolve => setTimeout(resolve, delay));
    route.continue();
  });

  const startTime = Date.now();
  
  await page.goto('http://localhost:5173');
  
  // Verify loading states are shown
  const loadingSpinner = page.locator('.ant-spin-spinning');
  await expect(loadingSpinner).toBeVisible();
  
  // Wait for content to load
  await page.waitForSelector('.ant-table, .ant-empty', { timeout: 30000 });
  
  const loadTime = Date.now() - startTime;
  console.log(`Page loaded under slow network in ${loadTime}ms`);
  
  // Verify loading spinner is hidden
  await expect(loadingSpinner).not.toBeVisible();
  
  // Test user feedback during slow operations
  if (await page.locator('.ant-table-row').count() > 0) {
    await page.click('.ant-table-row:first-child');
    
    // Should show loading state during navigation
    await expect(loadingSpinner).toBeVisible();
    await page.waitForURL('**/job/**', { timeout: 30000 });
    await expect(loadingSpinner).not.toBeVisible();
  }
});
```

## 🔍 Data Boundary Test Scenarios

### Extreme Input Values
```javascript
test('Handling extreme input values', async ({ page }) => {
  await page.goto('http://localhost:5173/job/test-job-id');
  await page.click('.ant-tabs-tab:has-text("Interview")');
  await page.waitForSelector('.ant-spin-spinning', { state: 'detached' });

  // Test extreme question count values
  const extremeValues = [
    { value: '0', expectError: true, description: 'zero questions' },
    { value: '-5', expectError: true, description: 'negative questions' },
    { value: '999', expectError: true, description: 'excessive questions' },
    { value: '1', expectError: false, description: 'minimum valid' },
    { value: '20', expectError: false, description: 'maximum valid' }
  ];

  for (const testCase of extremeValues) {
    const questionInput = page.locator('.ant-input-number input');
    if (await questionInput.isVisible()) {
      await questionInput.clear();
      await questionInput.fill(testCase.value);
      
      const generateButton = page.locator('button:has-text("Generate")');
      if (await generateButton.isVisible()) {
        await generateButton.click();
        
        if (testCase.expectError) {
          // Should show validation error or error notification
          const hasValidationError = await page.locator('.ant-form-item-has-error').isVisible();
          const hasErrorNotification = await page.locator('.ant-notification-error').isVisible();
          
          expect(hasValidationError || hasErrorNotification).toBeTruthy();
          console.log(`Correctly handled ${testCase.description}`);
        } else {
          // Should succeed or show loading
          const hasLoading = await page.locator('.ant-spin-spinning').isVisible();
          const hasSuccess = await page.locator('.ant-notification-success').isVisible();
          
          if (hasLoading) {
            await page.waitForSelector('.ant-spin-spinning', { state: 'detached', timeout: 30000 });
          }
          
          console.log(`Successfully handled ${testCase.description}`);
        }
      }
    }
  }
});
```

### Unicode and Special Characters
```javascript
test('Handling unicode and special characters', async ({ page }) => {
  const specialInputs = [
    '🚀 Senior Developer 💻',           // Emojis
    'Développeur Sénior',               // Accented characters
    '高级开发工程师',                    // Chinese characters
    'Старший разработчик',              // Cyrillic
    '<script>alert("xss")</script>',    // XSS attempt
    'SELECT * FROM users; --',          // SQL injection attempt
    'A'.repeat(1000),                   // Very long string
    '   ',                              // Only whitespace
    '\n\t\r',                          // Control characters
    '../../etc/passwd'                  // Path traversal attempt
  ];

  await page.goto('http://localhost:5173');

  for (const input of specialInputs) {
    const searchField = page.locator('input[placeholder*="Search"]');
    if (await searchField.isVisible()) {
      await searchField.clear();
      await searchField.fill(input);
      await searchField.press('Enter');
      
      // Wait for response
      await page.waitForTimeout(2000);
      
      // Verify no XSS or injection occurred
      const hasAlert = await page.locator('text="xss"').isVisible();
      expect(hasAlert).toBeFalsy();
      
      // Verify application didn't crash
      const hasError = await page.locator('.ant-notification-error').isVisible();
      if (hasError) {
        const errorText = await page.locator('.ant-notification-error').textContent();
        console.log(`Input "${input.substring(0, 20)}..." caused error: ${errorText}`);
      }
      
      // Application should remain functional
      await expect(page.locator('body')).toBeVisible();
    }
  }
});
```

## 🔄 State Corruption Test Scenarios

### Invalid Browser State
```javascript
test('Recovery from corrupted browser state', async ({ page }) => {
  await page.goto('http://localhost:5173');
  
  // Corrupt localStorage
  await page.evaluate(() => {
    localStorage.setItem('candidatesTableState', 'invalid-json{');
    localStorage.setItem('jobOrdersState', '{"invalid": json}');
    localStorage.setItem('userPreferences', 'null');
  });

  // Reload page
  await page.reload();
  await page.waitForSelector('.ant-table, .ant-empty', { timeout: 10000 });
  
  // Application should recover gracefully
  const hasError = await page.locator('.ant-notification-error').isVisible();
  if (hasError) {
    console.log('Application showed error notification for corrupted state');
  }
  
  // Core functionality should still work
  const searchField = page.locator('input[placeholder*="Search"]');
  if (await searchField.isVisible()) {
    await searchField.fill('test');
    await searchField.press('Enter');
    await page.waitForTimeout(2000);
  }
  
  // Verify state was reset to defaults
  const currentState = await page.evaluate(() => {
    return {
      candidates: localStorage.getItem('candidatesTableState'),
      jobs: localStorage.getItem('jobOrdersState')
    };
  });
  
  // Should have valid JSON or be reset
  if (currentState.candidates) {
    expect(() => JSON.parse(currentState.candidates)).not.toThrow();
  }
});
```

// Helper function for quick form filling
async function fillFeedbackFormQuickly(page, data) {
  const recruiterInput = page.locator('input[name*="recruiter"]');
  if (await recruiterInput.isVisible()) {
    await recruiterInput.fill(data.recruiter);
  }

  const datePicker = page.locator('.ant-picker').first();
  if (await datePicker.isVisible()) {
    await datePicker.click();
    await page.click('.ant-picker-today-btn');
  }

  const statusSelect = page.locator('.ant-select:has(.ant-select-selection-item:has-text("Status"))');
  if (await statusSelect.isVisible()) {
    await statusSelect.click();
    await page.click(`.ant-select-item-option:has-text("${data.status}")`);
  }

  const commentsTextArea = page.locator('textarea[name*="comments"]');
  if (await commentsTextArea.isVisible()) {
    await commentsTextArea.fill(data.comments);
  }
}

This comprehensive advanced test scenarios documentation provides thorough coverage of edge cases, error conditions, and stress testing scenarios for the SmartHR application.
