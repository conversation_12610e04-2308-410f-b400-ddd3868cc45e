# SmartHR Playwright Testing Troubleshooting Guide

## Common Issues and Solutions

This guide covers the most common issues encountered when running SmartHR Playwright tests and their solutions.

## Test Execution Issues

### Issue 1: Test Stops Too Early (Wrong Loader Detection)
**Symptoms:**
- Test completes in 2-3 seconds
- No questions are extracted
- Log shows "Generation completed" but no actual generation occurred

**Root Cause:**
The test is waiting for the candidate list loader instead of the interview generation loader.

**Solution:**
```typescript
// ❌ WRONG - Generic loading detection
const loadingSpinner = page.locator('.ant-spin-spinning').first();

// ✅ CORRECT - Specific button loading detection
const generateAILoadingButton = page.locator('.ant-btn-loading:has-text("Generate AI Interview")');
await generateAILoadingButton.waitFor({ state: 'hidden', timeout: 60000 });
```

**Key Points:**
- There are TWO loaders in the SmartHR application
- Loader 1: Candidate list (ignore this)
- Loader 2: Interview generation (wait for this)
- Always wait for the "Generate AI Interview" button to stop loading

### Issue 2: Button Not Found Errors
**Symptoms:**
```
Error: locator.click: Error: Element not found
```

**Common Causes:**
1. Page not fully loaded
2. Modal not opened
3. Incorrect selector
4. Element hidden or disabled

**Solutions:**

**A. Add Proper Wait Conditions:**
```typescript
// Wait for page to be ready
await page.waitForLoadState('networkidle');

// Wait for element to be visible
await expect(button).toBeVisible({ timeout: 15000 });

// Wait for element to be enabled
await expect(button).toBeEnabled();
```

**B. Use Multiple Selector Strategy:**
```typescript
const buttonSelectors = [
  '#primary-selector',
  '.fallback-selector',
  'button:has-text("Button Text")'
];

let button = null;
for (const selector of buttonSelectors) {
  const element = page.locator(selector);
  if (await element.isVisible({ timeout: 5000 })) {
    button = element;
    break;
  }
}
```

### Issue 3: Position Not Found
**Symptoms:**
- "Salesforce QA position not found"
- Test falls back to first available position

**Solutions:**

**A. Check Available Positions:**
```typescript
// Debug: List all available positions
const jobRows = page.locator('.ant-table-row, .job-row, .ant-card');
const count = await jobRows.count();
console.log(`Found ${count} job positions:`);

for (let i = 0; i < count; i++) {
  const text = await jobRows.nth(i).textContent();
  console.log(`Position ${i + 1}: ${text}`);
}
```

**B. Implement Flexible Position Matching:**
```typescript
// Try multiple variations
const positionVariations = [
  'Salesforce QA',
  'Salesforce Quality Assurance',
  'QA Engineer - Salesforce',
  'Salesforce Tester'
];

let foundPosition = null;
for (const variation of positionVariations) {
  const position = jobRows.filter({ hasText: variation });
  if (await position.count() > 0) {
    foundPosition = position.first();
    break;
  }
}
```

## Loading and Timing Issues

### Issue 4: Timeout Errors
**Symptoms:**
```
Error: Timeout 30000ms exceeded
```

**Solutions:**

**A. Increase Timeouts for AI Generation:**
```typescript
// AI generation can take up to 60 seconds
await loadingElement.waitFor({ state: 'hidden', timeout: 60000 });
```

**B. Add Progressive Timeout Strategy:**
```typescript
// Start with short timeout, increase if needed
const timeouts = [10000, 30000, 60000];
let success = false;

for (const timeout of timeouts) {
  try {
    await element.waitFor({ state: 'visible', timeout });
    success = true;
    break;
  } catch (error) {
    console.log(`Timeout ${timeout}ms exceeded, trying longer timeout...`);
  }
}
```

### Issue 5: Questions Not Extracted
**Symptoms:**
- Test completes successfully
- "Extracted 0 questions" in logs
- Empty generated-questions file

**Root Causes:**
1. Wrong question selector
2. Questions in different format
3. Generation not completed

**Solutions:**

**A. Debug Question Selectors:**
```typescript
// Test all possible selectors
const questionSelectors = [
  '.question-item',
  '[data-testid="question"]',
  '.ant-card:has-text("?")',
  'div:has-text("?")',
  '*:contains("Expected Response")'
];

for (const selector of questionSelectors) {
  const count = await page.locator(selector).count();
  console.log(`Selector "${selector}": ${count} elements found`);
}
```

**B. Inspect Page Content:**
```typescript
// Get all page text to see what's available
const pageContent = await page.textContent('body');
console.log('Page content preview:', pageContent.substring(0, 500));

// Look for question patterns
const hasQuestions = pageContent.includes('Expected Response');
console.log('Contains "Expected Response":', hasQuestions);
```

## Browser and Environment Issues

### Issue 6: Mobile Browser Failures
**Symptoms:**
- Tests pass on desktop, fail on mobile
- Screenshot size errors
- Touch interaction failures

**Solutions:**

**A. Mobile-Specific Screenshot Handling:**
```typescript
const isMobile = testInfo.project.name.includes('Mobile');
const screenshotOptions = isMobile ? {} : { fullPage: true };
await page.screenshot({ path: 'screenshot.png', ...screenshotOptions });
```

**B. Touch vs Click Interactions:**
```typescript
if (isMobile) {
  await element.tap();        // Use tap for mobile
} else {
  await element.click();      // Use click for desktop
}
```

### Issue 7: Docker Backend Connection Issues
**Symptoms:**
- "Connection refused" errors
- Backend API calls failing
- Frontend loads but no data

**Solutions:**

**A. Verify Backend Status:**
```bash
# Check if Docker container is running
docker ps | grep smarthr

# Check port accessibility
curl -s http://localhost:8080/health || echo "Backend not accessible"
```

**B. Add Backend Health Check:**
```typescript
// Add to test setup
test.beforeEach(async ({ page }) => {
  // Verify backend is accessible
  const response = await page.request.get('http://localhost:8080/health');
  expect(response.status()).toBe(200);
});
```

## Debugging Strategies

### Debug Mode Execution
```bash
# Run with debug mode for step-by-step execution
npx playwright test --debug

# Run with headed browser to see actions
npx playwright test --headed

# Run with trace for detailed analysis
npx playwright test --trace on
```

### Visual Debugging
```typescript
// Take screenshots at key points
await page.screenshot({ path: `debug-step-${stepNumber}.png` });

// Highlight elements before interaction
await element.highlight();
await page.waitForTimeout(1000); // Pause to see highlight

// Log element states
console.log('Element visible:', await element.isVisible());
console.log('Element enabled:', await element.isEnabled());
console.log('Element text:', await element.textContent());
```

### Network Debugging
```typescript
// Monitor network requests
page.on('request', request => {
  console.log('Request:', request.method(), request.url());
});

page.on('response', response => {
  console.log('Response:', response.status(), response.url());
});
```

## Performance Issues

### Issue 8: Slow Test Execution
**Symptoms:**
- Tests take longer than expected
- Multiple unnecessary waits

**Solutions:**

**A. Optimize Wait Strategies:**
```typescript
// ❌ SLOW - Fixed waits
await page.waitForTimeout(5000);

// ✅ FAST - Conditional waits
await page.waitForLoadState('networkidle');
await element.waitFor({ state: 'visible' });
```

**B. Parallel Execution:**
```typescript
// Run independent operations in parallel
await Promise.all([
  page.screenshot({ path: 'screenshot.png' }),
  page.locator('.element').textContent(),
  page.waitForLoadState('networkidle')
]);
```

## Configuration Issues

### Issue 9: Playwright Config Problems
**Common Config Issues:**
- Wrong base URL
- Insufficient timeouts
- Missing browser installations

**Solution - Verify Config:**
```typescript
// playwright.config.ts
export default defineConfig({
  testDir: './tests',
  timeout: 30000,
  expect: { timeout: 5000 },
  
  use: {
    baseURL: 'http://localhost:5173',  // Correct frontend URL
    actionTimeout: 15000,              // Sufficient for UI actions
    navigationTimeout: 30000,          // Sufficient for page loads
  },
  
  projects: [
    { name: 'chromium', use: { ...devices['Desktop Chrome'] } },
    { name: 'Mobile Chrome', use: { ...devices['Pixel 5'] } }
  ]
});
```

## Error Recovery Patterns

### Retry Mechanism
```typescript
async function retryOperation(operation, maxAttempts = 3) {
  for (let attempt = 1; attempt <= maxAttempts; attempt++) {
    try {
      return await operation();
    } catch (error) {
      if (attempt === maxAttempts) throw error;
      console.log(`Attempt ${attempt} failed, retrying...`);
      await page.waitForTimeout(2000);
    }
  }
}
```

### Graceful Degradation
```typescript
// Try primary approach, fall back to alternatives
try {
  await primaryApproach();
} catch (error) {
  console.log('Primary approach failed, trying fallback...');
  try {
    await fallbackApproach();
  } catch (fallbackError) {
    console.log('All approaches failed, using default behavior');
    await defaultBehavior();
  }
}
```

## Maintenance Checklist

### Regular Maintenance Tasks
1. **Verify Selectors**: Check if UI changes broke selectors
2. **Update Timeouts**: Adjust based on performance changes
3. **Test Position Names**: Ensure test positions still exist
4. **Validate Output**: Check generated question format
5. **Browser Compatibility**: Test on all supported browsers

### Health Check Script
```typescript
// Add to CI/CD pipeline
test('SmartHR Health Check', async ({ page }) => {
  // Verify frontend loads
  await page.goto('/');
  await expect(page.locator('body')).toBeVisible();
  
  // Verify backend connectivity
  const response = await page.request.get('/api/health');
  expect(response.status()).toBe(200);
  
  // Verify key elements exist
  await expect(page.locator('a:has-text("Jobs")')).toBeVisible();
});
```

---

**Remember:**
- Always check the most recent logs for specific error details
- Use visual debugging (--headed) when troubleshooting
- Test changes on multiple browsers and devices
- Keep selectors updated with UI changes

*This troubleshooting guide is continuously updated based on common issues encountered in SmartHR testing.*
