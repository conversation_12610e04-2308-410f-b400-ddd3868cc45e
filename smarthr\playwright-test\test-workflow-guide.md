# SmartHR Test Workflow Guide

## Complete Generate New Questions Workflow

This guide provides step-by-step instructions for understanding and implementing the Generate New Questions test workflow.

## Workflow Overview

```mermaid
graph TD
    A[Start Test] --> B[Navigate to Homepage]
    B --> C[Go to Jobs Page]
    C --> D[Find Specific Position]
    D --> E[Click Generate AI Interview]
    E --> F[Modal Opens]
    F --> G[Configure Options]
    G --> H[Click Generate New Questions]
    H --> I[Wait for Button Loading]
    I --> J[Extract Questions]
    J --> K[Save to Files]
    K --> L[Test Complete]
```

## Detailed Step-by-Step Process

### Step 1: Environment Setup
```typescript
// Test initialization
const startTime = Date.now();
const isMobile = projectName === 'Mobile Chrome' || projectName === 'Mobile Safari';
const screenshotOptions = isMobile ? {} : { fullPage: true };
```

**Key Points:**
- Detect mobile vs desktop for different interaction methods
- Initialize timing for performance tracking
- Set screenshot options based on device type

### Step 2: Homepage Navigation
```typescript
await page.goto('/');
await page.waitForLoadState('networkidle');
```

**Expected Result:**
- SmartHR homepage loads completely
- All network requests complete
- Page is ready for interaction

### Step 3: Jobs Page Navigation
```typescript
const jobsLink = page.locator('a:has-text("Jobs"), a:has-text("Positions"), .ant-menu-item:has-text("Jobs"), [href*="job"]');
await jobsLink.first().click();
```

**Mobile Considerations:**
```typescript
if (isMobile) {
  const mobileMenuButton = page.locator('.ant-drawer-trigger, .hamburger, .menu-trigger');
  if (await mobileMenuButton.isVisible({ timeout: 5000 })) {
    await mobileMenuButton.click();
  }
}
```

### Step 4: Position Selection (Critical)
```typescript
// Primary: Look for exact position match
const salesforceQAJob = jobRows.filter({ hasText: 'Salesforce QA' });

// Fallback: Alternative matches
const alternativeSelectors = [
  jobRows.filter({ hasText: 'Salesforce' }),
  jobRows.filter({ hasText: 'QA' }),
  jobRows.filter({ hasText: 'Quality Assurance' })
];
```

**Important Notes:**
- Always search for specific position first
- Implement fallback options for robustness
- Extract position ID from URL for tracking

### Step 5: Generate AI Interview Button (First Button)
```typescript
const generateAIButton = page.locator('#root > div > div > main > div > div:nth-child(2) > div:nth-child(1) > div > div:nth-child(4)');
await generateAIButton.click();
```

**Critical Selector:**
- This exact CSS selector is required
- Button opens the AI Interview modal
- Wait for modal to appear after clicking

### Step 6: Modal Configuration
```typescript
// Wait for modal content to load
await page.waitForTimeout(3000);

// Configure question count
const questionCountInput = page.locator('input[type="number"], .ant-input-number input');
if (await questionCountInput.isVisible({ timeout: 3000 })) {
  await questionCountInput.clear();
  await questionCountInput.fill('10');
}
```

**Configuration Options:**
- Question count (default: 10)
- Skill categories (Technical, Soft, Methodologies, Language)
- Seniority levels (Junior, Mid, Senior)

### Step 7: Generate New Questions Button (Second Button)
```typescript
const generateNewQuestionsButton = page.locator('#root > div > div > main > div > div:nth-child(2) > div:nth-child(2) > div > div.ant-col.ant-col-xs-24.ant-col-md-14.css-dev-only-do-not-override-ni1kz0 > div:nth-child(1) > div > div > div:nth-child(1) > button');
await generateNewQuestionsButton.click();
```

**Critical Notes:**
- This is the second button in the modal
- Triggers the actual AI question generation
- Must wait for this button's loading state

### Step 8: Loading Detection (Most Critical)
```typescript
// Wait for "Generate AI Interview" button to start loading
const generateAILoadingButton = page.locator('.ant-btn-loading:has-text("Generate AI Interview")');

if (await generateAILoadingButton.isVisible({ timeout: 5000 })) {
  console.log('🔄 "Generate AI Interview" button is loading - waiting for generation to complete...');
  
  // Wait for loading to complete (up to 60 seconds)
  await generateAILoadingButton.waitFor({ state: 'hidden', timeout: 60000 });
  
  // Wait for button to return to normal state
  const normalGenerateButton = page.locator('button:has-text("Generate AI Interview"):not(.ant-btn-loading)');
  await normalGenerateButton.waitFor({ state: 'visible', timeout: 10000 });
}
```

**Why This Is Critical:**
- There are TWO loaders in the application
- Loader 1: Candidate list (ignore this)
- Loader 2: Interview generation (wait for this)
- Must wait for the "Generate AI Interview" button to stop loading

### Step 9: Question Extraction
```typescript
// Look for questions in the interface
const questionSelectors = [
  '.question-item',
  '[data-testid="question"]',
  '.ant-card:has-text("?")',
  'div:has-text("?")'
];

// Parse questions and answers
function parseQuestionsAndAnswers(fullText: string): GeneratedQuestion[] {
  const questionPattern = /(\d+)\.\s*([^]*?)(?=\d+\.\s|$)/g;
  // Split by "Expected Response:"
  const parts = questionContent.split('Expected Response:');
}
```

**Parsing Logic:**
1. Find questions numbered with "1.", "2.", "3.", etc.
2. Extract text after "Expected Response:"
3. Identify skill categories (TECHNICAL SKILLS, SOFT SKILLS, etc.)
4. Clean up formatting

### Step 10: File Generation
```typescript
// Create generated-questions folder
const generatedQuestionsDir = join('generated-questions');
mkdirSync(generatedQuestionsDir, { recursive: true });

// Save questions and answers
const questionsContent = generateQuestionsAndAnswersContent(results);
const questionsPath = join(generatedQuestionsDir, `${results.positionTitle.replace(/[^a-zA-Z0-9]/g, '-')}-questions-${Date.now()}.md`);
writeFileSync(questionsPath, questionsContent, 'utf8');
```

**Output Format:**
```markdown
# Salesforce QA - Interview Questions and Answers

## 1. Can you describe your experience with Salesforce configurations?

**Expected Response**: I have extensive experience with Salesforce configurations...

---

## 2. How do you approach testing in an Agile environment?

**Expected Response**: In an Agile environment, I prioritize continuous testing...
```

## Error Handling and Recovery

### Common Failure Points
1. **Position Not Found**: Implement fallback position selection
2. **Button Not Visible**: Add retry logic with different selectors
3. **Loading Timeout**: Extend timeout for slow AI generation
4. **Question Parsing Failed**: Add alternative parsing methods

### Recovery Strategies
```typescript
// Retry mechanism for button clicks
for (let attempt = 1; attempt <= 3; attempt++) {
  try {
    await button.click();
    break;
  } catch (error) {
    if (attempt === 3) throw error;
    await page.waitForTimeout(2000);
  }
}
```

## Performance Optimization

### Timing Considerations
- **Modal Loading**: 3 seconds wait after first button click
- **Generation Time**: 2-5 seconds typical, up to 60 seconds maximum
- **Total Test Time**: 12-15 seconds for successful execution

### Screenshot Strategy
- Take screenshots at each major step
- Use viewport screenshots for mobile to avoid size limits
- Store in test-results folder with descriptive names

## Mobile Device Considerations

### Touch Interactions
```typescript
if (isMobile) {
  await element.tap();  // Use tap for mobile
} else {
  await element.click(); // Use click for desktop
}
```

### Responsive Elements
- Mobile menu handling
- Different button sizes
- Viewport-specific screenshots

## Debugging Tips

### Visual Debugging
```bash
# Run with headed browser to see actions
npx playwright test --headed

# Run with debug mode for step-by-step execution
npx playwright test --debug
```

### Logging Strategy
- Log each major step with emoji indicators
- Include timing information
- Capture element states before interactions

### Common Debug Commands
```typescript
// Check element visibility
console.log('Button visible:', await button.isVisible());

// Get element text content
console.log('Button text:', await button.textContent());

// Take debug screenshot
await page.screenshot({ path: 'debug-screenshot.png' });
```

---

*This workflow guide ensures consistent and reliable test execution across different environments and scenarios.*
