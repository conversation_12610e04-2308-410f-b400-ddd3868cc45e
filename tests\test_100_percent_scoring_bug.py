import pytest
from unittest.mock import patch, MagicMock
from models.interview import (
    EvaluationResult,
    QuestionEvaluation,
    Seniority,
    TranscriptQuestions,
    TranscriptQuestion
)
from controllers.interview_controller import _validate_evaluation_result


class TestHundredPercentScoringBug:
    """Test cases to reproduce the 100% scoring bug with incomplete/garbled responses."""

    def test_incomplete_garbled_responses_should_not_get_100_percent(self):
        """Test that incomplete/garbled responses like 'I could¿ve know' should not result in 100% score."""
        
        # Create evaluation result with problematic responses from user's example
        result = EvaluationResult(
            overall_seniority=Seniority.JUNIOR,
            per_question=[
                QuestionEvaluation(
                    question_number=1,
                    expected_seniority=Seniority.SENIOR,
                    detected_seniority=Seniority.JUNIOR,
                    explanation="<PERSON><PERSON><PERSON> responded 'I could¿ve know' - incomplete/garbled response"
                ),
                QuestionEvaluation(
                    question_number=2,
                    expected_seniority=Seniority.SENIOR,
                    detected_seniority=Seniority.JUNIOR,
                    explanation="Candidate said 'I don´t know' - honest admission but very brief"
                ),
                QuestionEvaluation(
                    question_number=3,
                    expected_seniority=Seniority.SENIOR,
                    detected_seniority=Seniority.JUNIOR,
                    explanation="Can<PERSON><PERSON> responded 'I don´t know' - no experience indicated"
                )
            ],
            percentage_of_match=100.0,  # This is the bug - should not be 100%
            explanation="Overall junior level due to limited experience in most areas"
        )
        
        # Validate the result - should detect issues
        issues = _validate_evaluation_result(result, 3, "test")
        
        # Should have issues because garbled responses shouldn't count as 100%
        print(f"Validation issues found: {issues}")
        
        # The validation should catch this as suspicious
        assert len(issues) > 0, "Validation should detect issues with garbled responses getting 100%"

    def test_problematic_transcript_from_user_example(self):
        """Test using the actual problematic responses from the user's example."""
        
        # Simulate the problematic transcript content from user's example
        problematic_responses = [
            "I could¿ve know",  # Garbled/incomplete response
            "I don´t know",     # Brief admission
            "I don´t know",     # Another brief admission
            "I don´t know",     # Yet another brief admission
        ]
        
        # Create evaluation that would incorrectly give 100%
        per_question_evals = []
        for i, response in enumerate(problematic_responses, 1):
            per_question_evals.append(QuestionEvaluation(
                question_number=i,
                expected_seniority=Seniority.SENIOR,
                detected_seniority=Seniority.JUNIOR,
                explanation=f"Candidate responded '{response}' showing limited knowledge"
            ))
        
        result = EvaluationResult(
            overall_seniority=Seniority.JUNIOR,
            per_question=per_question_evals,
            percentage_of_match=100.0,  # This is the problematic score
            explanation="Junior level due to limited experience"
        )
        
        # This should be flagged as suspicious
        issues = _validate_evaluation_result(result, len(problematic_responses), "test")
        print(f"Issues with problematic responses: {issues}")
        
        # Should detect the issue
        assert len(issues) > 0, "Should detect issues with mostly incomplete responses getting 100%"

    def test_mixed_quality_responses_percentage_calculation(self):
        """Test that mixed quality responses get appropriate percentage scores."""
        
        # Mix of good, poor, and garbled responses
        result = EvaluationResult(
            overall_seniority=Seniority.JUNIOR,
            per_question=[
                QuestionEvaluation(
                    question_number=1,
                    expected_seniority=Seniority.SENIOR,
                    detected_seniority=Seniority.SENIOR,
                    explanation="Candidate provided detailed explanation of microservices architecture"
                ),
                QuestionEvaluation(
                    question_number=2,
                    expected_seniority=Seniority.MID,
                    detected_seniority=Seniority.JUNIOR,
                    explanation="Candidate said 'I don't know React' - honest admission"
                ),
                QuestionEvaluation(
                    question_number=3,
                    expected_seniority=Seniority.SENIOR,
                    detected_seniority=Seniority.JUNIOR,
                    explanation="Candidate responded 'I could¿ve know' - garbled/incomplete response"
                ),
                QuestionEvaluation(
                    question_number=4,
                    expected_seniority=Seniority.MID,
                    detected_seniority=Seniority.MID,
                    explanation="Candidate showed good understanding of SQL basics"
                )
            ],
            percentage_of_match=100.0,  # This should be lower due to garbled response
            explanation="Mixed responses with one garbled answer"
        )
        
        # Should detect percentage issues
        issues = _validate_evaluation_result(result, 4, "test")
        print(f"Mixed quality response issues: {issues}")
        
        # Should flag the percentage as suspicious
        percentage_issues = [issue for issue in issues if "percentage" in issue.lower()]
        assert len(percentage_issues) > 0, "Should detect percentage calculation issues with garbled responses"

    def test_garbled_vs_honest_admission_distinction(self):
        """Test that the system should distinguish between garbled responses and honest admissions."""
        
        honest_admissions = [
            "I don't know",
            "I'm not sure",
            "No experience with that",
            "Never used it",
            "I don't have experience with that"
        ]
        
        garbled_responses = [
            "I could¿ve know",
            "I don´t know",  # Note the accent character
            "I couldve",     # Incomplete
            "I dont",        # Incomplete
            "I could",       # Incomplete
        ]
        
        # Honest admissions should count toward percentage (as junior level)
        for admission in honest_admissions:
            result = EvaluationResult(
                overall_seniority=Seniority.JUNIOR,
                per_question=[
                    QuestionEvaluation(
                        question_number=1,
                        expected_seniority=Seniority.SENIOR,
                        detected_seniority=Seniority.JUNIOR,
                        explanation=f"Candidate responded '{admission}' - honest admission of lack of knowledge"
                    )
                ],
                percentage_of_match=100.0,  # Should be valid for honest admissions
                explanation="Junior level due to limited experience"
            )
            
            issues = _validate_evaluation_result(result, 1, "test")
            assert len(issues) == 0, f"Honest admission '{admission}' should be valid and count toward percentage"
        
        # Garbled responses should be flagged as problematic
        for garbled in garbled_responses:
            result = EvaluationResult(
                overall_seniority=Seniority.JUNIOR,
                per_question=[
                    QuestionEvaluation(
                        question_number=1,
                        expected_seniority=Seniority.SENIOR,
                        detected_seniority=Seniority.JUNIOR,
                        explanation=f"Candidate responded '{garbled}' - garbled/incomplete response"
                    )
                ],
                percentage_of_match=100.0,  # This should be flagged as suspicious
                explanation="Garbled response"
            )
            
            issues = _validate_evaluation_result(result, 1, "test")
            print(f"Garbled response '{garbled}' validation issues: {issues}")
            # Note: Current validation might not catch this yet - this test documents the expected behavior

    def test_user_example_technical_questions(self):
        """Test using the specific technical questions from the user's example."""

        # Questions from user's example
        questions_and_responses = [
            ("Can you describe your experience with Power Platform and its various components?", "I don´t know"),
            ("How do you stay current with the latest developments and features in Power Platform?", "I don´t know"),
            ("Can you describe your experience with Azure and how you have used it in your work?", "I don´t know"),
            ("Can you describe your experience with JavaScript and how you have used it in your work?", "I don´t know"),
            ("Can you describe your experience with C# and how you have used it in your work?", "I don´t know"),
            ("Can you describe your experience with SQL and how you have used it in your work?", "I don´t know"),
            ("Can you describe your experience with Power BI and how you have used it in your work?", "I could¿ve know"),
            ("Can you describe your experience with custom connectors and how you have used them in your work?", "I don´t know"),
            ("Can you describe your experience with Power Pages and how you have used it in your work?", "I don´t know"),
            ("Can you describe your experience with PL-400 or PL-600 certification and how you have applied it in your work?", "I don´t know")
        ]

        per_question_evals = []
        for i, (question, response) in enumerate(questions_and_responses, 1):
            if "could¿ve" in response:
                explanation = f"Question: '{question}' - Candidate responded '{response}' - incomplete/garbled response"
            else:
                explanation = f"Question: '{question}' - Candidate responded '{response}' - garbled text with encoding issues"

            per_question_evals.append(QuestionEvaluation(
                question_number=i,
                expected_seniority=Seniority.SENIOR,
                detected_seniority=Seniority.JUNIOR,
                explanation=explanation
            ))

        # This scenario should NOT result in 100% match
        result = EvaluationResult(
            overall_seniority=Seniority.JUNIOR,
            per_question=per_question_evals,
            percentage_of_match=100.0,  # This is the bug the user is reporting
            explanation="Candidate showed limited knowledge across all technical areas"
        )

        issues = _validate_evaluation_result(result, len(questions_and_responses), "test")
        print(f"User example validation issues: {issues}")

        # Should detect this as problematic
        assert len(issues) > 0, "Should detect issues when most responses are garbled or incomplete"

    def test_comprehensive_response_scenarios(self):
        """Test comprehensive scenarios covering all response types."""

        # Test scenario 1: Mix of honest admissions (should count) and garbled responses (should not count)
        mixed_result = EvaluationResult(
            overall_seniority=Seniority.JUNIOR,
            per_question=[
                QuestionEvaluation(
                    question_number=1,
                    expected_seniority=Seniority.SENIOR,
                    detected_seniority=Seniority.JUNIOR,
                    explanation="Candidate said 'I don't know' - honest admission of lack of knowledge"
                ),
                QuestionEvaluation(
                    question_number=2,
                    expected_seniority=Seniority.SENIOR,
                    detected_seniority=Seniority.JUNIOR,
                    explanation="Candidate responded 'I could¿ve know' - incomplete/garbled response"
                ),
                QuestionEvaluation(
                    question_number=3,
                    expected_seniority=Seniority.SENIOR,
                    detected_seniority=Seniority.JUNIOR,
                    explanation="Candidate said 'I don´t know' - garbled text with encoding issues"
                ),
                QuestionEvaluation(
                    question_number=4,
                    expected_seniority=Seniority.MID,
                    detected_seniority=Seniority.MID,
                    explanation="Candidate provided good technical explanation"
                )
            ],
            percentage_of_match=50.0,  # Should be 50% - only 2 adequate responses out of 4
            explanation="Mixed response quality"
        )

        issues = _validate_evaluation_result(mixed_result, 4, "test")
        print(f"Mixed scenario validation issues: {issues}")

        # Should pass validation with correct percentage
        percentage_issues = [issue for issue in issues if "percentage" in issue.lower()]
        assert len(percentage_issues) == 0, "Should pass validation with correct 50% calculation"

    def test_honest_admissions_vs_garbled_responses(self):
        """Test that honest admissions are treated differently from garbled responses."""

        # Honest admissions should count toward percentage
        honest_result = EvaluationResult(
            overall_seniority=Seniority.JUNIOR,
            per_question=[
                QuestionEvaluation(
                    question_number=1,
                    expected_seniority=Seniority.SENIOR,
                    detected_seniority=Seniority.JUNIOR,
                    explanation="Candidate said 'I don't know' - honest admission of lack of knowledge"
                ),
                QuestionEvaluation(
                    question_number=2,
                    expected_seniority=Seniority.SENIOR,
                    detected_seniority=Seniority.JUNIOR,
                    explanation="Candidate said 'I'm not sure' - honest admission showing limited experience"
                )
            ],
            percentage_of_match=100.0,  # Should be valid - both are honest admissions
            explanation="Honest admissions of limited knowledge"
        )

        honest_issues = _validate_evaluation_result(honest_result, 2, "test")
        assert len(honest_issues) == 0, "Honest admissions should be valid and count toward percentage"

        # Garbled responses should NOT count toward percentage
        garbled_result = EvaluationResult(
            overall_seniority=Seniority.JUNIOR,
            per_question=[
                QuestionEvaluation(
                    question_number=1,
                    expected_seniority=Seniority.SENIOR,
                    detected_seniority=Seniority.JUNIOR,
                    explanation="Candidate responded 'I could¿ve know' - incomplete/garbled response"
                ),
                QuestionEvaluation(
                    question_number=2,
                    expected_seniority=Seniority.SENIOR,
                    detected_seniority=Seniority.JUNIOR,
                    explanation="Candidate said 'I don´t know' - garbled text with encoding issues"
                )
            ],
            percentage_of_match=100.0,  # This should be flagged as suspicious
            explanation="Garbled responses"
        )

        garbled_issues = _validate_evaluation_result(garbled_result, 2, "test")
        assert len(garbled_issues) > 0, "Garbled responses should be flagged as suspicious when getting 100%"
