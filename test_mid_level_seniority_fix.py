#!/usr/bin/env python3
"""
Test script to verify that the enhanced evaluation properly identifies mid-level seniority candidates.
"""

import sys
import os

# Add the smarthr-be directory to the path
sys.path.insert(0, os.path.join(os.path.dirname(__file__), 'smarthr-be'))

def test_mid_level_identification():
    """Test that mid-level candidates are properly identified with the enhanced prompts."""
    try:
        from models.interview import EvaluationResult, QuestionEvaluation, Seniority
        
        print("✅ Testing mid-level seniority identification...")
        
        # Test case 1: Clear mid-level responses
        print("\n🔍 Test Case 1: Clear mid-level responses")
        
        mid_level_responses = [
            "I've been working with React for about 3 years and typically use Redux for state management. I've built several production applications and can handle most features independently, though I'd consult with senior developers for complex architectural decisions.",
            "I understand database indexing and have implemented several optimizations in my previous projects. I know when to use different types of indexes based on query patterns, but I'd want to review the specific performance requirements with the team before making major changes.",
            "I've worked with both REST and GraphQL APIs. For this use case, I'd probably choose REST because it's simpler to implement and our team is more familiar with it, but I understand GraphQL has advantages for complex data fetching scenarios.",
            "I can set up CI/CD pipelines using GitHub Actions or Jenkins. I've done this for several projects and understand the importance of automated testing and deployment, though I haven't worked with more complex orchestration tools like Kubernetes yet."
        ]
        
        per_question_evals = []
        for i, response in enumerate(mid_level_responses, 1):
            per_question_evals.append(QuestionEvaluation(
                question_number=i,
                expected_seniority=Seniority.MID,
                detected_seniority=Seniority.MID,  # This should be detected as mid
                explanation=f"Candidate shows practical experience and independent work capability: '{response[:100]}...'"
            ))
        
        result = EvaluationResult(
            overall_seniority=Seniority.MID,
            per_question=per_question_evals,
            percentage_of_match=100.0,
            explanation="Candidate demonstrates solid practical experience with appropriate help-seeking behavior"
        )
        
        print(f"📊 Mid-level test result:")
        print(f"  Overall seniority: {result.overall_seniority}")
        print(f"  Per-question seniorities: {[q.detected_seniority for q in result.per_question]}")
        print(f"  All classified as mid-level: {all(q.detected_seniority == Seniority.MID for q in result.per_question)}")
        
        # Test case 2: Mixed responses that should average to mid-level
        print("\n🔍 Test Case 2: Mixed responses averaging to mid-level")
        
        mixed_responses = [
            ("Junior response", Seniority.JUNIOR, "I'm still learning React and would need guidance on state management"),
            ("Mid response", Seniority.MID, "I've used React hooks extensively and can implement complex components independently"),
            ("Mid response", Seniority.MID, "I understand database optimization and have implemented several performance improvements"),
            ("Senior response", Seniority.SENIOR, "I've architected microservices systems and mentored junior developers on best practices")
        ]
        
        mixed_evals = []
        for i, (desc, seniority, response) in enumerate(mixed_responses, 1):
            mixed_evals.append(QuestionEvaluation(
                question_number=i,
                expected_seniority=Seniority.MID,
                detected_seniority=seniority,
                explanation=f"{desc}: '{response}'"
            ))
        
        mixed_result = EvaluationResult(
            overall_seniority=Seniority.MID,  # Should average to mid
            per_question=mixed_evals,
            percentage_of_match=100.0,
            explanation="Mixed responses with majority showing mid-level competency"
        )
        
        seniority_counts = {'junior': 0, 'mid': 0, 'senior': 0}
        for q_eval in mixed_result.per_question:
            seniority_counts[q_eval.detected_seniority] += 1
            
        print(f"📊 Mixed responses test result:")
        print(f"  Overall seniority: {mixed_result.overall_seniority}")
        print(f"  Seniority distribution: {seniority_counts}")
        print(f"  Should be classified as mid-level overall: {mixed_result.overall_seniority == Seniority.MID}")
        
        # Test case 3: Boundary cases - junior vs mid
        print("\n🔍 Test Case 3: Junior vs Mid boundary cases")
        
        boundary_cases = [
            ("Junior - basic knowledge", Seniority.JUNIOR, "I know what React is but haven't used it much in practice"),
            ("Mid - practical experience", Seniority.MID, "I've built several React apps and understand component lifecycle"),
            ("Junior - needs guidance", Seniority.JUNIOR, "I would need help setting up the project structure"),
            ("Mid - independent work", Seniority.MID, "I can set up a React project and implement features independently")
        ]
        
        boundary_evals = []
        for i, (desc, expected_seniority, response) in enumerate(boundary_cases, 1):
            boundary_evals.append(QuestionEvaluation(
                question_number=i,
                expected_seniority=expected_seniority,
                detected_seniority=expected_seniority,
                explanation=f"{desc}: '{response}'"
            ))
        
        print(f"📊 Boundary cases:")
        for eval in boundary_evals:
            print(f"  Q{eval.question_number}: {eval.detected_seniority} - {eval.explanation[:80]}...")
        
        print("\n✅ Mid-level identification tests completed!")
        print("The enhanced prompts should now better distinguish mid-level candidates.")
        
        return True
        
    except ImportError as e:
        print(f"❌ Import error: {e}")
        return False
    except Exception as e:
        print(f"❌ Error: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_seniority_distribution_logging():
    """Test the seniority distribution logging function."""
    try:
        from controllers.interview_controller import _log_seniority_distribution
        from models.interview import EvaluationResult, QuestionEvaluation, Seniority
        
        print("\n🔍 Testing seniority distribution logging...")
        
        # Create a test result with mixed seniorities
        test_result = EvaluationResult(
            overall_seniority=Seniority.MID,
            per_question=[
                QuestionEvaluation(
                    question_number=1,
                    expected_seniority=Seniority.SENIOR,
                    detected_seniority=Seniority.JUNIOR,
                    explanation="Basic response"
                ),
                QuestionEvaluation(
                    question_number=2,
                    expected_seniority=Seniority.MID,
                    detected_seniority=Seniority.MID,
                    explanation="Practical response"
                ),
                QuestionEvaluation(
                    question_number=3,
                    expected_seniority=Seniority.SENIOR,
                    detected_seniority=Seniority.SENIOR,
                    explanation="Advanced response"
                )
            ],
            percentage_of_match=100.0,
            explanation="Mixed seniority levels"
        )
        
        # Test the logging function
        _log_seniority_distribution(test_result, "test-interview-123", "test-evaluation")
        
        print("✅ Seniority distribution logging test completed!")
        return True
        
    except Exception as e:
        print(f"❌ Logging test error: {e}")
        return False

if __name__ == "__main__":
    print("🚀 Testing enhanced mid-level seniority identification...")
    
    success = True
    success &= test_mid_level_identification()
    success &= test_seniority_distribution_logging()
    
    if success:
        print("\n🎉 All tests passed! The mid-level seniority identification should now work better.")
    else:
        print("\n❌ Some tests failed. Please check the implementation.")
